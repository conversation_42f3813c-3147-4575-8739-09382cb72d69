/**
 * Hydration-safe utilities for FantasyPro
 * Prevents React hydration mismatches between server and client
 */

import React, { useState, useEffect } from 'react';

/**
 * Hook to safely handle client-only content that might differ between server and client
 * @param clientValue - The value to use on the client
 * @param serverValue - The value to use on the server (optional, defaults to null)
 */
export function useClientOnly<T>(clientValue: T, serverValue: T | null = null): T | null {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient ? clientValue : serverValue;
}

/**
 * Hook to safely handle timestamps that might differ between server and client
 */
export function useClientTimestamp(): string | null {
  const [timestamp, setTimestamp] = useState<string | null>(null);

  useEffect(() => {
    setTimestamp(new Date().toISOString());
  }, []);

  return timestamp;
}

/**
 * Component wrapper that only renders children on the client
 */
export function ClientOnly({ children, fallback = null }: { 
  children: React.ReactNode; 
  fallback?: React.ReactNode;
}) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient ? <>{children}</> : <>{fallback}</>;
}

/**
 * Safe timestamp formatter that works consistently across server and client
 */
export function formatTimestamp(timestamp: string | null): string {
  if (!timestamp) return 'Loading...';
  
  try {
    const date = new Date(timestamp);
    return date.toLocaleString();
  } catch (error) {
    return 'Invalid date';
  }
}

/**
 * Generate a stable timestamp for API responses that won't cause hydration issues
 */
export function getStableTimestamp(): string {
  // Use a fixed timestamp for SSR, real timestamp for client
  if (typeof window === 'undefined') {
    // Server-side: return a stable timestamp
    return '2024-01-01T00:00:00.000Z';
  }
  // Client-side: return actual timestamp
  return new Date().toISOString();
}

/**
 * Hook for handling time-sensitive data that might cause hydration mismatches
 */
export function useHydrationSafeTime() {
  const [time, setTime] = useState<{
    timestamp: string;
    formatted: string;
    isClient: boolean;
  }>({
    timestamp: '2024-01-01T00:00:00.000Z',
    formatted: 'Loading...',
    isClient: false
  });

  useEffect(() => {
    const now = new Date();
    setTime({
      timestamp: now.toISOString(),
      formatted: now.toLocaleString(),
      isClient: true
    });
  }, []);

  return time;
}

/**
 * Wrapper for components that might have hydration issues
 */
export function HydrationBoundary({ 
  children, 
  fallback = <div>Loading...</div> 
}: { 
  children: React.ReactNode; 
  fallback?: React.ReactNode;
}) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Safe way to access window object
 */
export function useIsClient(): boolean {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook for safely handling localStorage
 */
export function useLocalStorage<T>(key: string, defaultValue: T): [T, (value: T) => void] {
  const [value, setValue] = useState<T>(defaultValue);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    try {
      const stored = localStorage.getItem(key);
      if (stored) {
        setValue(JSON.parse(stored));
      }
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
    }
  }, [key]);

  const setStoredValue = (newValue: T) => {
    setValue(newValue);
    if (isClient) {
      try {
        localStorage.setItem(key, JSON.stringify(newValue));
      } catch (error) {
        console.warn(`Error writing to localStorage key "${key}":`, error);
      }
    }
  };

  return [value, setStoredValue];
}
