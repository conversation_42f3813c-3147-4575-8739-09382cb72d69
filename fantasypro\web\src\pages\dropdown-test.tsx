import React, { useState } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import { motion } from 'framer-motion';
import Layout from '../components/Layout';
import PredictiveSearch from '../components/PredictiveSearch';
import SimplePlayerSearch from '../components/SimplePlayerSearch';
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  InformationCircleIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

interface Player {
  id: string;
  name: string;
  team: string;
  position: string;
  price: number;
  average: number;
}

const DropdownTest: NextPage = () => {
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);

  const handlePlayerSelect = (player: Player) => {
    setSelectedPlayer(player);
  };

  return (
    <Layout>
      <Head>
        <title>Dropdown Z-Index Test - FantasyPro</title>
        <meta name="description" content="Test page for dropdown positioning and z-index fixes" />
      </Head>

      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-center space-x-3 mb-4"
          >
            <CheckCircleIcon className="w-8 h-8 text-green-400" />
            <h1 className="text-4xl font-bold themed-text-primary">Dropdown Z-Index Test</h1>
            <CheckCircleIcon className="w-8 h-8 text-green-400" />
          </motion.div>
          <p className="text-lg themed-text-tertiary max-w-3xl mx-auto">
            Testing dropdown positioning and z-index fixes to ensure search dropdowns appear above widget containers.
          </p>
        </div>

        {/* Test Cases */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          
          {/* Test Case 1: Widget Container */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="card widget-container p-6"
            style={{ overflow: 'hidden', height: '300px' }}
          >
            <div className="flex items-center space-x-3 mb-4">
              <ExclamationTriangleIcon className="w-6 h-6 text-yellow-400" />
              <h3 className="text-xl font-semibold themed-text-primary">Test Case 1: Clipped Container</h3>
            </div>
            
            <p className="text-sm themed-text-tertiary mb-4">
              This container has <code>overflow: hidden</code> and fixed height. 
              The dropdown should still appear above it.
            </p>
            
            <PredictiveSearch
              placeholder="Search should dropdown above container..."
              onPlayerSelect={handlePlayerSelect}
              showPlayerDetails={true}
              maxResults={6}
              minQueryLength={1}
            />
            
            <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded">
              <div className="text-sm text-yellow-400">
                ⚠️ Container has overflow:hidden and height:300px
              </div>
            </div>
          </motion.div>

          {/* Test Case 2: Normal Container */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="card p-6"
          >
            <div className="flex items-center space-x-3 mb-4">
              <CheckCircleIcon className="w-6 h-6 text-green-400" />
              <h3 className="text-xl font-semibold themed-text-primary">Test Case 2: Normal Container</h3>
            </div>
            
            <p className="text-sm themed-text-tertiary mb-4">
              This container has normal overflow. The dropdown should work perfectly.
            </p>
            
            <SimplePlayerSearch
              placeholder="Search in normal container..."
              onPlayerSelect={handlePlayerSelect}
              maxResults={6}
              minQueryLength={1}
            />
            
            <div className="mt-4 p-3 bg-green-500/10 border border-green-500/20 rounded">
              <div className="text-sm text-green-400">
                ✅ Container has normal overflow behavior
              </div>
            </div>
          </motion.div>

          {/* Test Case 3: Nested Containers */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="card p-6"
          >
            <div className="flex items-center space-x-3 mb-4">
              <InformationCircleIcon className="w-6 h-6 text-blue-400" />
              <h3 className="text-xl font-semibold themed-text-primary">Test Case 3: Nested Containers</h3>
            </div>
            
            <div className="border themed-border rounded p-4" style={{ overflow: 'hidden', height: '200px' }}>
              <div className="border themed-border rounded p-3" style={{ overflow: 'hidden' }}>
                <p className="text-sm themed-text-tertiary mb-3">
                  Nested containers with overflow:hidden
                </p>
                
                <PredictiveSearch
                  placeholder="Search in nested containers..."
                  onPlayerSelect={handlePlayerSelect}
                  showPlayerDetails={true}
                  maxResults={5}
                  minQueryLength={1}
                />
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded">
              <div className="text-sm text-blue-400">
                ℹ️ Multiple nested containers with overflow:hidden
              </div>
            </div>
          </motion.div>

          {/* Test Case 4: Grid Layout */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="card p-6"
          >
            <div className="flex items-center space-x-3 mb-4">
              <SparklesIcon className="w-6 h-6 text-purple-400" />
              <h3 className="text-xl font-semibold themed-text-primary">Test Case 4: Grid Layout</h3>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="border themed-border rounded p-3" style={{ overflow: 'hidden', height: '120px' }}>
                <p className="text-xs themed-text-tertiary mb-2">Grid Item 1</p>
                <PredictiveSearch
                  placeholder="Search..."
                  onPlayerSelect={handlePlayerSelect}
                  maxResults={4}
                  minQueryLength={1}
                />
              </div>
              
              <div className="border themed-border rounded p-3" style={{ overflow: 'hidden', height: '120px' }}>
                <p className="text-xs themed-text-tertiary mb-2">Grid Item 2</p>
                <SimplePlayerSearch
                  placeholder="Search..."
                  onPlayerSelect={handlePlayerSelect}
                  maxResults={4}
                  minQueryLength={1}
                />
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-purple-500/10 border border-purple-500/20 rounded">
              <div className="text-sm text-purple-400">
                ✨ Grid layout with constrained containers
              </div>
            </div>
          </motion.div>
        </div>

        {/* Selected Player Display */}
        {selectedPlayer && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="card-premium p-6"
          >
            <div className="flex items-center space-x-3 mb-4">
              <CheckCircleIcon className="w-6 h-6 text-green-400" />
              <h3 className="text-xl font-semibold themed-text-primary">Dropdown Test Result</h3>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
              <div>
                <div className="font-semibold text-lg text-green-400">✅ {selectedPlayer.name}</div>
                <div className="text-sm themed-text-tertiary">
                  {selectedPlayer.team} • {selectedPlayer.position}
                </div>
                <div className="text-xs text-green-400 mt-1">
                  Dropdown selection successful!
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-green-400">
                  ${(selectedPlayer.price / 1000000).toFixed(2)}M
                </div>
                <div className="text-sm themed-text-tertiary">
                  Avg: {selectedPlayer.average.toFixed(1)}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Fix Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="card-premium p-6"
        >
          <h3 className="text-xl font-semibold themed-text-primary mb-4">Z-Index Fix Status</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-green-400 mb-3">✅ Implemented Fixes</h4>
              <ul className="space-y-2 text-sm themed-text-tertiary">
                <li>• Ultra-high z-index (9999) for dropdowns</li>
                <li>• CSS !important overrides for positioning</li>
                <li>• Portal-based dropdown rendering</li>
                <li>• Container overflow fixes</li>
                <li>• Responsive positioning</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold text-blue-400 mb-3">🔧 Technical Details</h4>
              <ul className="space-y-2 text-sm themed-text-tertiary">
                <li>• React Portal for DOM escape</li>
                <li>• Dynamic positioning calculation</li>
                <li>• Scroll and resize event handling</li>
                <li>• CSS class-based overrides</li>
                <li>• Cross-browser compatibility</li>
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Instructions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="card p-6"
        >
          <h3 className="text-xl font-semibold themed-text-primary mb-4">Test Instructions</h3>
          
          <div className="space-y-3 text-sm themed-text-tertiary">
            <p>
              <strong>1. Type in any search box:</strong> Try typing "Na", "Re", "FLB", or "Storm"
            </p>
            <p>
              <strong>2. Check dropdown visibility:</strong> Ensure dropdowns appear fully above containers
            </p>
            <p>
              <strong>3. Test different containers:</strong> Try all test cases to verify z-index fixes
            </p>
            <p>
              <strong>4. Verify selection:</strong> Click on any player to confirm dropdown functionality
            </p>
          </div>
        </motion.div>
      </div>
    </Layout>
  );
};

export default DropdownTest;
