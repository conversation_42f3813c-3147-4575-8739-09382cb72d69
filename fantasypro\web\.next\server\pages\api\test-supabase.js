"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/test-supabase";
exports.ids = ["pages/api/test-supabase"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-supabase&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-supabase.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-supabase&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-supabase.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_test_supabase_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\test-supabase.ts */ \"(api)/./src/pages/api/test-supabase.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_test_supabase_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_test_supabase_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/test-supabase\",\n        pathname: \"/api/test-supabase\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_test_supabase_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-supabase&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-supabase.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/test-supabase.ts":
/*!****************************************!*\
  !*** ./src/pages/api/test-supabase.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _services_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../services/supabase */ \"(api)/./src/services/supabase.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        console.log(\"\\uD83D\\uDD0D Testing Supabase connection and data...\");\n        // Test connection\n        const { data: connectionTest, error: connectionError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"players\").select(\"count\").limit(1);\n        if (connectionError) {\n            console.error(\"❌ Supabase connection failed:\", connectionError);\n            return res.status(500).json({\n                success: false,\n                error: \"Supabase connection failed\",\n                details: connectionError.message\n            });\n        }\n        console.log(\"✅ Supabase connection successful\");\n        // Get player count\n        const { count: playerCount, error: countError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"players\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        // Get sample players\n        const { data: samplePlayers, error: sampleError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"players\").select(\"*\").limit(10).order(\"points\", {\n            ascending: false\n        });\n        // Check for other possible table names\n        const tableTests = [];\n        // Test nrl_players table\n        const { count: nrlPlayersCount, error: nrlPlayersError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"nrl_players\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        tableTests.push({\n            table: \"nrl_players\",\n            count: nrlPlayersCount,\n            error: nrlPlayersError?.message\n        });\n        // Test nrl_player_stats table\n        const { count: nrlStatsCount, error: nrlStatsError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"nrl_player_stats\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        tableTests.push({\n            table: \"nrl_player_stats\",\n            count: nrlStatsCount,\n            error: nrlStatsError?.message\n        });\n        // Test nrl_teams table\n        const { count: nrlTeamsCount, error: nrlTeamsError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"nrl_teams\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        tableTests.push({\n            table: \"nrl_teams\",\n            count: nrlTeamsCount,\n            error: nrlTeamsError?.message\n        });\n        // Get sample from the largest table\n        let largestTable = \"players\";\n        let largestCount = playerCount || 0;\n        tableTests.forEach((test)=>{\n            if (test.count && test.count > largestCount) {\n                largestTable = test.table;\n                largestCount = test.count;\n            }\n        });\n        let sampleFromLargest = [];\n        if (largestTable !== \"players\") {\n            const { data: largestSample, error: largestError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(largestTable).select(\"*\").limit(5);\n            if (!largestError) {\n                sampleFromLargest = largestSample || [];\n            }\n        }\n        const result = {\n            success: true,\n            connection: \"successful\",\n            tables: {\n                players: {\n                    count: playerCount,\n                    error: countError?.message,\n                    sample: samplePlayers?.slice(0, 3) || []\n                },\n                ...tableTests.reduce((acc, test)=>{\n                    acc[test.table] = {\n                        count: test.count,\n                        error: test.error\n                    };\n                    return acc;\n                }, {})\n            },\n            largest_table: {\n                name: largestTable,\n                count: largestCount,\n                sample: largestTable !== \"players\" ? sampleFromLargest.slice(0, 3) : []\n            },\n            analysis: {\n                total_players_found: Math.max(playerCount || 0, largestCount),\n                expected_players: 581,\n                data_complete: largestCount >= 500,\n                recommended_table: largestCount >= 500 ? largestTable : \"Need data import\",\n                issues: []\n            },\n            timestamp: new Date().toISOString()\n        };\n        // Add analysis issues\n        if (largestCount < 500) {\n            result.analysis.issues.push(\"Player count below expected 581 - may need data import\");\n        }\n        if (largestCount === 0) {\n            result.analysis.issues.push(\"No player data found in any table - database may be empty\");\n        }\n        if (largestTable !== \"players\") {\n            result.analysis.issues.push(`Main data is in '${largestTable}' table, not 'players' table`);\n        }\n        console.log(`📊 Supabase Analysis Complete:`);\n        console.log(`   - Largest table: ${largestTable} (${largestCount} records)`);\n        console.log(`   - Expected: 581 players`);\n        console.log(`   - Data complete: ${result.analysis.data_complete}`);\n        return res.status(200).json(result);\n    } catch (error) {\n        console.error(\"❌ Supabase test error:\", error);\n        return res.status(500).json({\n            success: false,\n            error: \"Test failed\",\n            details: error.message,\n            timestamp: new Date().toISOString()\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/test-supabase.ts\n");

/***/ }),

/***/ "(api)/./src/services/supabase.ts":
/*!**********************************!*\
  !*** ./src/services/supabase.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: () => (/* binding */ AnalyticsService),\n/* harmony export */   CacheService: () => (/* binding */ CacheService),\n/* harmony export */   InjuryService: () => (/* binding */ InjuryService),\n/* harmony export */   PlayerService: () => (/* binding */ PlayerService),\n/* harmony export */   SquadService: () => (/* binding */ SquadService),\n/* harmony export */   TradeService: () => (/* binding */ TradeService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Supabase configuration\nconst supabaseUrl = \"https://fuxpdgsixnbbsdspusmp.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ3NzE5NzQsImV4cCI6MjA1MDM0Nzk3NH0.Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5\" || 0;\n// Create Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Player service functions\nclass PlayerService {\n    // Get all players with search and filtering\n    static async getPlayers(options = {}) {\n        let query = supabase.from(\"players\").select(\"*\");\n        if (options.search) {\n            query = query.or(`name.ilike.%${options.search}%,team.ilike.%${options.search}%,position.ilike.%${options.search}%`);\n        }\n        if (options.position) {\n            query = query.eq(\"position\", options.position);\n        }\n        if (options.team) {\n            query = query.eq(\"team\", options.team);\n        }\n        if (options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n        }\n        const { data, error } = await query.order(\"points\", {\n            ascending: false\n        });\n        if (error) {\n            console.error(\"Error fetching players:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Get ALL players (for Players page - should return 581 players)\n    static async getAllPlayers() {\n        console.log(\"\\uD83D\\uDD04 PlayerService.getAllPlayers() - fetching complete dataset from Supabase\");\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"points\", {\n            ascending: false\n        });\n        if (error) {\n            console.error(\"❌ Error fetching all players from Supabase:\", error);\n            throw error;\n        }\n        console.log(`✅ Supabase returned ${data?.length || 0} players`);\n        return data;\n    }\n    // Get player by ID\n    static async getPlayer(id) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").eq(\"id\", id).single();\n        if (error) {\n            console.error(\"Error fetching player:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Search players with predictive algorithm\n    static async searchPlayers(query, limit = 10) {\n        if (!query || query.length < 2) return [];\n        const { data, error } = await supabase.from(\"players\").select(\"*\").or(`name.ilike.%${query}%,team.ilike.%${query}%,position.ilike.%${query}%`).order(\"points\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error searching players:\", error);\n            return [];\n        }\n        return data;\n    }\n    // Get top performers\n    static async getTopPerformers(limit = 10) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"points\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching top performers:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Get price risers\n    static async getPriceRisers(limit = 10) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"price\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching price risers:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Update player data\n    static async updatePlayer(id, updates) {\n        const { data, error } = await supabase.from(\"players\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", id).select().single();\n        if (error) {\n            console.error(\"Error updating player:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Trade recommendation service\nclass TradeService {\n    // Get trade recommendations for user\n    static async getTradeRecommendations(userId, limit = 10) {\n        const { data, error } = await supabase.from(\"trade_recommendations\").select(`\n        *,\n        player_out:players!player_out_id(*),\n        player_in:players!player_in_id(*)\n      `).eq(\"user_id\", userId).order(\"confidence\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching trade recommendations:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Create trade recommendation\n    static async createTradeRecommendation(recommendation) {\n        const { data, error } = await supabase.from(\"trade_recommendations\").insert({\n            ...recommendation,\n            created_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating trade recommendation:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Execute trade (log the trade)\n    static async executeTrade(tradeId, userId) {\n        const { data, error } = await supabase.from(\"executed_trades\").insert({\n            trade_recommendation_id: tradeId,\n            user_id: userId,\n            executed_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error executing trade:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Injury service\nclass InjuryService {\n    // Get current injury reports\n    static async getInjuryReports(limit = 10) {\n        const { data, error } = await supabase.from(\"injury_reports\").select(`\n        *,\n        player:players(*)\n      `).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching injury reports:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Create injury report\n    static async createInjuryReport(report) {\n        const { data, error } = await supabase.from(\"injury_reports\").insert({\n            ...report,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating injury report:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// User squad service\nclass SquadService {\n    // Get user's squad\n    static async getUserSquad(userId) {\n        const { data, error } = await supabase.from(\"user_squads\").select(`\n        *,\n        player:players(*)\n      `).eq(\"user_id\", userId).order(\"position_in_squad\");\n        if (error) {\n            console.error(\"Error fetching user squad:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Add player to squad\n    static async addPlayerToSquad(squadEntry) {\n        const { data, error } = await supabase.from(\"user_squads\").insert({\n            ...squadEntry,\n            created_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error adding player to squad:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Set captain\n    static async setCaptain(userId, playerId) {\n        // First, remove captain status from all players\n        await supabase.from(\"user_squads\").update({\n            is_captain: false,\n            is_vice_captain: false\n        }).eq(\"user_id\", userId);\n        // Then set the new captain\n        const { data, error } = await supabase.from(\"user_squads\").update({\n            is_captain: true\n        }).eq(\"user_id\", userId).eq(\"player_id\", playerId).select().single();\n        if (error) {\n            console.error(\"Error setting captain:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Analytics service\nclass AnalyticsService {\n    // Get dashboard stats\n    static async getDashboardStats() {\n        const [playersCount, teamsCount, injuriesCount] = await Promise.all([\n            supabase.from(\"players\").select(\"id\", {\n                count: \"exact\",\n                head: true\n            }),\n            supabase.from(\"players\").select(\"team\", {\n                count: \"exact\",\n                head: true\n            }).distinct(),\n            supabase.from(\"injury_reports\").select(\"id\", {\n                count: \"exact\",\n                head: true\n            })\n        ]);\n        return {\n            total_players: playersCount.count || 0,\n            total_teams: 17,\n            active_injuries: injuriesCount.count || 0,\n            last_updated: new Date().toISOString()\n        };\n    }\n    // Get trending players\n    static async getTrendingPlayers(limit = 10) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"form\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching trending players:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Cache service for offline support\nclass CacheService {\n    static{\n        this.CACHE_PREFIX = \"fantasypro_cache_\";\n    }\n    static{\n        this.CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n        ;\n    }\n    static set(key, data) {\n        const cacheData = {\n            data,\n            timestamp: Date.now()\n        };\n        localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheData));\n    }\n    static get(key) {\n        const cached = localStorage.getItem(this.CACHE_PREFIX + key);\n        if (!cached) return null;\n        const { data, timestamp } = JSON.parse(cached);\n        if (Date.now() - timestamp > this.CACHE_DURATION) {\n            localStorage.removeItem(this.CACHE_PREFIX + key);\n            return null;\n        }\n        return data;\n    }\n    static clear() {\n        Object.keys(localStorage).forEach((key)=>{\n            if (key.startsWith(this.CACHE_PREFIX)) {\n                localStorage.removeItem(key);\n            }\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/services/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-supabase&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-supabase.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();