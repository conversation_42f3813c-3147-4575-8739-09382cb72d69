"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/test-supabase";
exports.ids = ["pages/api/test-supabase"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-supabase&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-supabase.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-supabase&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-supabase.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_test_supabase_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\test-supabase.ts */ \"(api)/./src/pages/api/test-supabase.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_test_supabase_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_test_supabase_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/test-supabase\",\n        pathname: \"/api/test-supabase\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_test_supabase_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-supabase&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-supabase.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/test-supabase.ts":
/*!****************************************!*\
  !*** ./src/pages/api/test-supabase.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _services_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../services/supabase */ \"(api)/./src/services/supabase.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        console.log(\"\\uD83D\\uDD0D Testing Supabase connection and data...\");\n        // Test connection\n        const { data: connectionTest, error: connectionError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"players\").select(\"count\").limit(1);\n        if (connectionError) {\n            console.error(\"❌ Supabase connection failed:\", connectionError);\n            return res.status(500).json({\n                success: false,\n                error: \"Supabase connection failed\",\n                details: connectionError.message\n            });\n        }\n        console.log(\"✅ Supabase connection successful\");\n        // Get player count\n        const { count: playerCount, error: countError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"players\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        // Get sample players\n        const { data: samplePlayers, error: sampleError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"players\").select(\"*\").limit(10).order(\"points\", {\n            ascending: false\n        });\n        // Check for other possible table names\n        const tableTests = [];\n        // Test nrl_players table\n        const { count: nrlPlayersCount, error: nrlPlayersError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"nrl_players\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        tableTests.push({\n            table: \"nrl_players\",\n            count: nrlPlayersCount,\n            error: nrlPlayersError?.message\n        });\n        // Test nrl_player_stats table\n        const { count: nrlStatsCount, error: nrlStatsError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"nrl_player_stats\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        tableTests.push({\n            table: \"nrl_player_stats\",\n            count: nrlStatsCount,\n            error: nrlStatsError?.message\n        });\n        // Test nrl_teams table\n        const { count: nrlTeamsCount, error: nrlTeamsError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"nrl_teams\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        tableTests.push({\n            table: \"nrl_teams\",\n            count: nrlTeamsCount,\n            error: nrlTeamsError?.message\n        });\n        // Get sample from the largest table\n        let largestTable = \"players\";\n        let largestCount = playerCount || 0;\n        tableTests.forEach((test)=>{\n            if (test.count && test.count > largestCount) {\n                largestTable = test.table;\n                largestCount = test.count;\n            }\n        });\n        let sampleFromLargest = [];\n        if (largestTable !== \"players\") {\n            const { data: largestSample, error: largestError } = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(largestTable).select(\"*\").limit(5);\n            if (!largestError) {\n                sampleFromLargest = largestSample || [];\n            }\n        }\n        const result = {\n            success: true,\n            connection: \"successful\",\n            tables: {\n                players: {\n                    count: playerCount,\n                    error: countError?.message,\n                    sample: samplePlayers?.slice(0, 3) || []\n                },\n                ...tableTests.reduce((acc, test)=>{\n                    acc[test.table] = {\n                        count: test.count,\n                        error: test.error\n                    };\n                    return acc;\n                }, {})\n            },\n            largest_table: {\n                name: largestTable,\n                count: largestCount,\n                sample: largestTable !== \"players\" ? sampleFromLargest.slice(0, 3) : []\n            },\n            analysis: {\n                total_players_found: Math.max(playerCount || 0, largestCount),\n                expected_players: 581,\n                data_complete: largestCount >= 500,\n                recommended_table: largestCount >= 500 ? largestTable : \"Need data import\",\n                issues: []\n            },\n            timestamp: new Date().toISOString()\n        };\n        // Add analysis issues\n        if (largestCount < 500) {\n            result.analysis.issues.push(\"Player count below expected 581 - may need data import\");\n        }\n        if (largestCount === 0) {\n            result.analysis.issues.push(\"No player data found in any table - database may be empty\");\n        }\n        if (largestTable !== \"players\") {\n            result.analysis.issues.push(`Main data is in '${largestTable}' table, not 'players' table`);\n        }\n        console.log(`📊 Supabase Analysis Complete:`);\n        console.log(`   - Largest table: ${largestTable} (${largestCount} records)`);\n        console.log(`   - Expected: 581 players`);\n        console.log(`   - Data complete: ${result.analysis.data_complete}`);\n        return res.status(200).json(result);\n    } catch (error) {\n        console.error(\"❌ Supabase test error:\", error);\n        return res.status(500).json({\n            success: false,\n            error: \"Test failed\",\n            details: error.message,\n            timestamp: new Date().toISOString()\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/test-supabase.ts\n");

/***/ }),

/***/ "(api)/./src/services/supabase.ts":
/*!**********************************!*\
  !*** ./src/services/supabase.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: () => (/* binding */ AnalyticsService),\n/* harmony export */   CacheService: () => (/* binding */ CacheService),\n/* harmony export */   InjuryService: () => (/* binding */ InjuryService),\n/* harmony export */   PlayerService: () => (/* binding */ PlayerService),\n/* harmony export */   SquadService: () => (/* binding */ SquadService),\n/* harmony export */   TradeService: () => (/* binding */ TradeService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Supabase configuration\nconst supabaseUrl = \"https://fuxpdgsixnbbsdspusmp.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ3NzE5NzQsImV4cCI6MjA1MDM0Nzk3NH0.Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5\" || 0;\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDUwODA1NiwiZXhwIjoyMDY2MDg0MDU2fQ.f5qZOHQlAKz_sbIwoGmY4Gub8bGHyx_jxlRYZk-1VPg\";\n// Create Supabase clients\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n// Player service functions\nclass PlayerService {\n    // Get all players with search and filtering\n    static async getPlayers(options = {}) {\n        let query = supabase.from(\"players\").select(\"*\");\n        if (options.search) {\n            query = query.or(`name.ilike.%${options.search}%,team.ilike.%${options.search}%,position.ilike.%${options.search}%`);\n        }\n        if (options.position) {\n            query = query.eq(\"position\", options.position);\n        }\n        if (options.team) {\n            query = query.eq(\"team\", options.team);\n        }\n        if (options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n        }\n        const { data, error } = await query.order(\"points\", {\n            ascending: false\n        });\n        if (error) {\n            console.error(\"Error fetching players:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Get ALL players (for Players page - should return 581 players)\n    static async getAllPlayers() {\n        console.log(\"\\uD83D\\uDD04 PlayerService.getAllPlayers() - fetching complete dataset from Supabase\");\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"points\", {\n            ascending: false\n        });\n        if (error) {\n            console.error(\"❌ Error fetching all players from Supabase:\", error);\n            throw error;\n        }\n        console.log(`✅ Supabase returned ${data?.length || 0} players`);\n        return data;\n    }\n    // Get player by ID\n    static async getPlayer(id) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").eq(\"id\", id).single();\n        if (error) {\n            console.error(\"Error fetching player:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Search players with predictive algorithm\n    static async searchPlayers(query, limit = 10) {\n        if (!query || query.length < 2) return [];\n        const { data, error } = await supabase.from(\"players\").select(\"*\").or(`name.ilike.%${query}%,team.ilike.%${query}%,position.ilike.%${query}%`).order(\"points\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error searching players:\", error);\n            return [];\n        }\n        return data;\n    }\n    // Get top performers\n    static async getTopPerformers(limit = 10) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"points\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching top performers:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Get price risers\n    static async getPriceRisers(limit = 10) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"price\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching price risers:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Update player data\n    static async updatePlayer(id, updates) {\n        const { data, error } = await supabase.from(\"players\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", id).select().single();\n        if (error) {\n            console.error(\"Error updating player:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Trade recommendation service\nclass TradeService {\n    // Get trade recommendations for user\n    static async getTradeRecommendations(userId, limit = 10) {\n        const { data, error } = await supabase.from(\"trade_recommendations\").select(`\n        *,\n        player_out:players!player_out_id(*),\n        player_in:players!player_in_id(*)\n      `).eq(\"user_id\", userId).order(\"confidence\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching trade recommendations:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Create trade recommendation\n    static async createTradeRecommendation(recommendation) {\n        const { data, error } = await supabase.from(\"trade_recommendations\").insert({\n            ...recommendation,\n            created_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating trade recommendation:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Execute trade (log the trade)\n    static async executeTrade(tradeId, userId) {\n        const { data, error } = await supabase.from(\"executed_trades\").insert({\n            trade_recommendation_id: tradeId,\n            user_id: userId,\n            executed_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error executing trade:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Injury service\nclass InjuryService {\n    // Get current injury reports\n    static async getInjuryReports(limit = 10) {\n        const { data, error } = await supabase.from(\"injury_reports\").select(`\n        *,\n        player:players(*)\n      `).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching injury reports:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Create injury report\n    static async createInjuryReport(report) {\n        const { data, error } = await supabase.from(\"injury_reports\").insert({\n            ...report,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating injury report:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// User squad service\nclass SquadService {\n    // Get user's squad\n    static async getUserSquad(userId) {\n        const { data, error } = await supabase.from(\"user_squads\").select(`\n        *,\n        player:players(*)\n      `).eq(\"user_id\", userId).order(\"position_in_squad\");\n        if (error) {\n            console.error(\"Error fetching user squad:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Add player to squad\n    static async addPlayerToSquad(squadEntry) {\n        const { data, error } = await supabase.from(\"user_squads\").insert({\n            ...squadEntry,\n            created_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error adding player to squad:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Set captain\n    static async setCaptain(userId, playerId) {\n        // First, remove captain status from all players\n        await supabase.from(\"user_squads\").update({\n            is_captain: false,\n            is_vice_captain: false\n        }).eq(\"user_id\", userId);\n        // Then set the new captain\n        const { data, error } = await supabase.from(\"user_squads\").update({\n            is_captain: true\n        }).eq(\"user_id\", userId).eq(\"player_id\", playerId).select().single();\n        if (error) {\n            console.error(\"Error setting captain:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Analytics service\nclass AnalyticsService {\n    // Get dashboard stats\n    static async getDashboardStats() {\n        const [playersCount, teamsCount, injuriesCount] = await Promise.all([\n            supabase.from(\"players\").select(\"id\", {\n                count: \"exact\",\n                head: true\n            }),\n            supabase.from(\"players\").select(\"team\", {\n                count: \"exact\",\n                head: true\n            }).distinct(),\n            supabase.from(\"injury_reports\").select(\"id\", {\n                count: \"exact\",\n                head: true\n            })\n        ]);\n        return {\n            total_players: playersCount.count || 0,\n            total_teams: 17,\n            active_injuries: injuriesCount.count || 0,\n            last_updated: new Date().toISOString()\n        };\n    }\n    // Get trending players\n    static async getTrendingPlayers(limit = 10) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"form\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching trending players:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Cache service for offline support\nclass CacheService {\n    static{\n        this.CACHE_PREFIX = \"fantasypro_cache_\";\n    }\n    static{\n        this.CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n        ;\n    }\n    static set(key, data) {\n        const cacheData = {\n            data,\n            timestamp: Date.now()\n        };\n        localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheData));\n    }\n    static get(key) {\n        const cached = localStorage.getItem(this.CACHE_PREFIX + key);\n        if (!cached) return null;\n        const { data, timestamp } = JSON.parse(cached);\n        if (Date.now() - timestamp > this.CACHE_DURATION) {\n            localStorage.removeItem(this.CACHE_PREFIX + key);\n            return null;\n        }\n        return data;\n    }\n    static clear() {\n        Object.keys(localStorage).forEach((key)=>{\n            if (key.startsWith(this.CACHE_PREFIX)) {\n                localStorage.removeItem(key);\n            }\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/services/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-supabase&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-supabase.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();