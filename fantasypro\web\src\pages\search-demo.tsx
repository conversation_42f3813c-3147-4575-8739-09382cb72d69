import React, { useState } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import { motion } from 'framer-motion';
import Layout from '../components/Layout';
import PredictiveSearch from '../components/PredictiveSearch';
import SimplePlayerSearch from '../components/SimplePlayerSearch';
import { 
  MagnifyingGlassIcon, 
  SparklesIcon, 
  TrophyIcon,
  UserGroupIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface Player {
  id: string;
  name: string;
  team: string;
  position: string;
  price: number;
  average: number;
}

const SearchDemo: NextPage = () => {
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [captain, setCaptain] = useState<Player | null>(null);
  const [searchResults, setSearchResults] = useState<Player[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  const handlePlayerSelect = (player: Player) => {
    setSelectedPlayer(player);
    console.log('Player selected:', player);
  };

  const handleCaptainSelect = (player: Player) => {
    setCaptain(player);
    console.log('Captain selected:', player);
  };

  const handleSearchChange = (query: string, results: Player[]) => {
    setSearchQuery(query);
    setSearchResults(results);
  };

  const formatCurrency = (amount: number | undefined | null) => {
    if (!amount || amount === 0) return '$0.00M';
    return `$${(amount / 1000000).toFixed(2)}M`;
  };

  const examples = [
    { query: 'Na', description: 'Shows Nathan Cleary, Nathan Brown, etc.' },
    { query: 'Re', description: 'Shows Reece Walsh, Reagan Campbell-Gillard, etc.' },
    { query: 'Jam', description: 'Shows James Tedesco, James Fisher-Harris, etc.' },
    { query: 'FLB', description: 'Shows all Fullbacks' },
    { query: 'Broncos', description: 'Shows all Brisbane Broncos players' },
    { query: 'Storm', description: 'Shows all Melbourne Storm players' }
  ];

  return (
    <Layout>
      <Head>
        <title>Predictive Search Demo - FantasyPro</title>
        <meta name="description" content="Demonstration of FantasyPro's predictive search capabilities" />
      </Head>

      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-center space-x-3 mb-4"
          >
            <SparklesIcon className="w-8 h-8 text-blue-400" />
            <h1 className="text-4xl font-bold themed-text-primary">Predictive Search Demo</h1>
            <SparklesIcon className="w-8 h-8 text-blue-400" />
          </motion.div>
          <p className="text-lg themed-text-tertiary max-w-3xl mx-auto">
            Experience FantasyPro's intelligent predictive search with real-time suggestions, 
            fuzzy matching, and instant player discovery across all 114+ NRL players.
          </p>
        </div>

        {/* Live Demo Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="card-premium p-8"
        >
          <div className="flex items-center space-x-3 mb-6">
            <MagnifyingGlassIcon className="w-6 h-6 text-green-400" />
            <h2 className="text-2xl font-semibold themed-text-primary">Live Predictive Search</h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Main Search */}
            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-3">
                🔍 Enhanced Predictive Search
              </label>
              <PredictiveSearch
                placeholder="Type 'Na' for Nathan Cleary, 'Re' for Reece Walsh..."
                onPlayerSelect={handlePlayerSelect}
                onSearchChange={handleSearchChange}
                showPlayerDetails={true}
                maxResults={8}
                minQueryLength={1}
                className="mb-4"
              />
              
              <div className="text-sm themed-text-tertiary">
                <strong>Features:</strong> Real-time suggestions, fuzzy matching, keyboard navigation, 
                relevance scoring, position/team filtering
              </div>
            </div>

            {/* Captain Search */}
            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-3">
                👑 Captain Selection Search
              </label>
              <PredictiveSearch
                placeholder="Search for captain..."
                showCaptainOption={true}
                onCaptainSelect={handleCaptainSelect}
                onPlayerSelect={handlePlayerSelect}
                showPlayerDetails={true}
                maxResults={6}
                minQueryLength={1}
                className="mb-4"
              />
              
              <div className="text-sm themed-text-tertiary">
                <strong>Features:</strong> Captain selection button, enhanced player details, 
                smart relevance ranking
              </div>
            </div>
          </div>

          {/* Search Stats */}
          {searchQuery && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg"
            >
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-blue-400">Search: "{searchQuery}"</div>
                  <div className="text-sm themed-text-tertiary">
                    Found {searchResults.length} matching players
                  </div>
                </div>
                <ClockIcon className="w-5 h-5 text-blue-400" />
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* Example Searches */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="card p-6"
        >
          <h3 className="text-xl font-semibold themed-text-primary mb-4">Try These Example Searches</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {examples.map((example, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                className="p-4 bg-slate-800 rounded-lg hover:bg-slate-700 transition-colors cursor-pointer"
                onClick={() => {
                  // This would trigger the search in a real implementation
                  console.log(`Example search: ${example.query}`);
                }}
              >
                <div className="font-mono text-green-400 font-semibold mb-2">"{example.query}"</div>
                <div className="text-sm themed-text-tertiary">{example.description}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Selected Player Display */}
        {selectedPlayer && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="card-premium p-6"
          >
            <div className="flex items-center space-x-3 mb-4">
              <UserGroupIcon className="w-6 h-6 text-purple-400" />
              <h3 className="text-xl font-semibold themed-text-primary">Selected Player</h3>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-slate-800 rounded-lg">
              <div>
                <div className="font-semibold text-lg themed-text-primary">{selectedPlayer.name}</div>
                <div className="text-sm themed-text-tertiary">
                  {selectedPlayer.team} • {selectedPlayer.position}
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold themed-text-primary">{formatCurrency(selectedPlayer.price)}</div>
                <div className="text-sm themed-text-tertiary">Avg: {(selectedPlayer.average || 0).toFixed(1)}</div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Captain Display */}
        {captain && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="card-premium p-6"
          >
            <div className="flex items-center space-x-3 mb-4">
              <TrophyIcon className="w-6 h-6 text-yellow-400" />
              <h3 className="text-xl font-semibold themed-text-primary">Selected Captain</h3>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg">
              <div>
                <div className="font-semibold text-lg text-yellow-400">{captain.name}</div>
                <div className="text-sm themed-text-tertiary">
                  {captain.team} • {captain.position} • Captain (2x points)
                </div>
              </div>
              <div className="text-right">
                <div className="font-semibold text-yellow-400">{formatCurrency(captain.price)}</div>
                <div className="text-sm themed-text-tertiary">Avg: {(captain.average || 0).toFixed(1)}</div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Features List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="card p-6"
        >
          <h3 className="text-xl font-semibold themed-text-primary mb-4">Predictive Search Features</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold themed-text-secondary mb-3">🚀 Performance Features</h4>
              <ul className="space-y-2 text-sm themed-text-tertiary">
                <li>• Real-time search with 150ms debouncing</li>
                <li>• Fuzzy matching and relevance scoring</li>
                <li>• Keyboard navigation (↑↓ Enter Esc)</li>
                <li>• Smart caching and optimization</li>
                <li>• Instant results from 114+ players</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold themed-text-secondary mb-3">🎯 Search Capabilities</h4>
              <ul className="space-y-2 text-sm themed-text-tertiary">
                <li>• Search by player name (partial matching)</li>
                <li>• Search by team name</li>
                <li>• Search by position</li>
                <li>• Highlighted matching text</li>
                <li>• Position and team filtering</li>
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Implementation Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="card-premium p-6"
        >
          <h3 className="text-xl font-semibold themed-text-primary mb-4">Implementation Status</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-500/10 rounded-lg">
              <div className="text-2xl font-bold text-green-400">✅</div>
              <div className="font-semibold themed-text-primary">Players Page</div>
              <div className="text-sm themed-text-tertiary">Enhanced with predictive search</div>
            </div>
            
            <div className="text-center p-4 bg-green-500/10 rounded-lg">
              <div className="text-2xl font-bold text-green-400">✅</div>
              <div className="font-semibold themed-text-primary">Dashboard</div>
              <div className="text-sm themed-text-tertiary">Multiple predictive search bars</div>
            </div>
            
            <div className="text-center p-4 bg-green-500/10 rounded-lg">
              <div className="text-2xl font-bold text-green-400">✅</div>
              <div className="font-semibold themed-text-primary">My Team</div>
              <div className="text-sm themed-text-tertiary">Player search & captain selection</div>
            </div>
          </div>
        </motion.div>
      </div>
    </Layout>
  );
};

export default SearchDemo;
