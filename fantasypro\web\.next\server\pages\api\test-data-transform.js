"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/test-data-transform";
exports.ids = ["pages/api/test-data-transform"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-data-transform&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-data-transform.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-data-transform&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-data-transform.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_test_data_transform_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\test-data-transform.ts */ \"(api)/./src/pages/api/test-data-transform.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_test_data_transform_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_test_data_transform_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/test-data-transform\",\n        pathname: \"/api/test-data-transform\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_test_data_transform_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-data-transform&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-data-transform.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/test-data-transform.ts":
/*!**********************************************!*\
  !*** ./src/pages/api/test-data-transform.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _services_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../services/supabase */ \"(api)/./src/services/supabase.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        console.log(\"\\uD83D\\uDD0D Testing data transformation...\");\n        // Test getting a few players\n        const players = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.PlayerService.getAllPlayers();\n        if (!players || players.length === 0) {\n            return res.status(500).json({\n                success: false,\n                error: \"No players returned from Supabase\",\n                timestamp: new Date().toISOString()\n            });\n        }\n        // Check if all required fields are present\n        const samplePlayer = players[0];\n        const requiredFields = [\n            \"id\",\n            \"name\",\n            \"team\",\n            \"position\",\n            \"price\",\n            \"points\",\n            \"average\",\n            \"form\"\n        ];\n        const missingFields = requiredFields.filter((field)=>!(field in samplePlayer));\n        // Test search functionality\n        let searchResults = [];\n        try {\n            searchResults = await _services_supabase__WEBPACK_IMPORTED_MODULE_0__.PlayerService.searchPlayers(\"Nathan\", 3);\n        } catch (searchError) {\n            console.warn(\"Search test failed:\", searchError);\n        }\n        const result = {\n            success: true,\n            data_transformation_test: {\n                total_players: players.length,\n                sample_player: samplePlayer,\n                required_fields_present: missingFields.length === 0,\n                missing_fields: missingFields,\n                all_fields: Object.keys(samplePlayer),\n                data_types: {\n                    id: typeof samplePlayer.id,\n                    name: typeof samplePlayer.name,\n                    team: typeof samplePlayer.team,\n                    position: typeof samplePlayer.position,\n                    price: typeof samplePlayer.price,\n                    points: typeof samplePlayer.points,\n                    average: typeof samplePlayer.average,\n                    form: typeof samplePlayer.form\n                }\n            },\n            search_test: {\n                query: \"Nathan\",\n                results_count: searchResults.length,\n                sample_results: searchResults.slice(0, 2),\n                search_working: searchResults.length > 0\n            },\n            validation: {\n                data_complete: players.length >= 500,\n                fields_valid: missingFields.length === 0,\n                search_functional: searchResults.length > 0,\n                ready_for_ui: players.length >= 500 && missingFields.length === 0\n            },\n            timestamp: new Date().toISOString()\n        };\n        console.log(`✅ Data transformation test complete:`);\n        console.log(`   - Players: ${players.length}`);\n        console.log(`   - Missing fields: ${missingFields.join(\", \") || \"None\"}`);\n        console.log(`   - Search results: ${searchResults.length}`);\n        console.log(`   - Ready for UI: ${result.validation.ready_for_ui}`);\n        return res.status(200).json(result);\n    } catch (error) {\n        console.error(\"❌ Data transformation test failed:\", error);\n        return res.status(500).json({\n            success: false,\n            error: \"Data transformation test failed\",\n            details: error.message,\n            stack: error.stack,\n            timestamp: new Date().toISOString()\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/test-data-transform.ts\n");

/***/ }),

/***/ "(api)/./src/services/supabase.ts":
/*!**********************************!*\
  !*** ./src/services/supabase.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: () => (/* binding */ AnalyticsService),\n/* harmony export */   CacheService: () => (/* binding */ CacheService),\n/* harmony export */   InjuryService: () => (/* binding */ InjuryService),\n/* harmony export */   PlayerService: () => (/* binding */ PlayerService),\n/* harmony export */   SquadService: () => (/* binding */ SquadService),\n/* harmony export */   TradeService: () => (/* binding */ TradeService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Supabase configuration\nconst supabaseUrl = \"https://fuxpdgsixnbbsdspusmp.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ3NzE5NzQsImV4cCI6MjA1MDM0Nzk3NH0.Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5\" || 0;\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDUwODA1NiwiZXhwIjoyMDY2MDg0MDU2fQ.f5qZOHQlAKz_sbIwoGmY4Gub8bGHyx_jxlRYZk-1VPg\";\n// Create Supabase clients\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n// Player service functions\nclass PlayerService {\n    // Get all players with search and filtering\n    static async getPlayers(options = {}) {\n        let query = supabase.from(\"players\").select(\"*\");\n        if (options.search) {\n            query = query.or(`name.ilike.%${options.search}%,team.ilike.%${options.search}%,position.ilike.%${options.search}%`);\n        }\n        if (options.position) {\n            query = query.eq(\"position\", options.position);\n        }\n        if (options.team) {\n            query = query.eq(\"team\", options.team);\n        }\n        if (options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n        }\n        const { data, error } = await query.order(\"points\", {\n            ascending: false\n        });\n        if (error) {\n            console.error(\"Error fetching players:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Get ALL players (for Players page - should return 581 players)\n    static async getAllPlayers() {\n        console.log(\"\\uD83D\\uDD04 PlayerService.getAllPlayers() - fetching complete 581 player dataset from nrl_players table\");\n        try {\n            // Get base player data from nrl_players\n            const { data: playersData, error: playersError } = await supabase.from(\"nrl_players\").select(\"*\").order(\"name\");\n            if (playersError) {\n                console.error(\"❌ Error fetching players from nrl_players table:\", playersError);\n                throw playersError;\n            }\n            // Get supplementary stats from nrl_player_stats\n            const { data: statsData, error: statsError } = await supabase.from(\"nrl_player_stats\").select(\"*\");\n            if (statsError) {\n                console.warn(\"⚠️ Could not fetch supplementary stats:\", statsError);\n            }\n            console.log(`✅ Supabase nrl_players table returned ${playersData?.length || 0} players`);\n            console.log(`✅ Supabase nrl_player_stats table returned ${statsData?.length || 0} stat records`);\n            // Create a map of stats by player name for quick lookup\n            const statsMap = new Map();\n            if (statsData) {\n                statsData.forEach((stat)=>{\n                    if (stat.name) {\n                        statsMap.set(stat.name.toLowerCase(), stat);\n                    }\n                });\n            }\n            // Transform nrl_players data to match our Player interface\n            const transformedPlayers = playersData?.map((player)=>{\n                const baseStats = player.statistics || {};\n                const supplementaryStats = statsMap.get(player.name.toLowerCase()) || {};\n                // Use supplementary stats if available, otherwise use base stats\n                const price = supplementaryStats.price || baseStats.price || 300000;\n                const totalPoints = supplementaryStats.total_points || baseStats.total_points || 0;\n                const averagePoints = supplementaryStats.average_points || baseStats.average_points || 0;\n                const breakeven = supplementaryStats.breakeven || baseStats.breakeven || 0;\n                // Calculate average if not available\n                const calculatedAverage = averagePoints > 0 ? averagePoints : totalPoints > 0 && supplementaryStats.games_played > 0 ? totalPoints / supplementaryStats.games_played : 0;\n                return {\n                    id: player.id.toString(),\n                    name: player.name,\n                    position: player.position || supplementaryStats.position || \"Unknown\",\n                    team: player.team_name || supplementaryStats.team || \"Unknown Team\",\n                    price: price,\n                    points: totalPoints,\n                    average: calculatedAverage,\n                    form: baseStats.form || calculatedAverage / 10,\n                    ownership: baseStats.ownership || 0,\n                    breakeven: breakeven,\n                    image_url: player.image_url,\n                    created_at: player.created_at,\n                    updated_at: player.updated_at\n                };\n            }) || [];\n            console.log(`✅ Transformed ${transformedPlayers.length} players with enhanced stats`);\n            return transformedPlayers;\n        } catch (error) {\n            console.error(\"❌ Error in getAllPlayers:\", error);\n            throw error;\n        }\n    }\n    // Get player by ID\n    static async getPlayer(id) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").eq(\"id\", id).single();\n        if (error) {\n            console.error(\"Error fetching player:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Search players with predictive algorithm (searches all 581 players)\n    static async searchPlayers(query, limit = 10) {\n        if (!query || query.length < 2) return [];\n        console.log(`🔍 Searching nrl_players table for: \"${query}\"`);\n        const { data, error } = await supabase.from(\"nrl_players\").select(\"*\").or(`name.ilike.%${query}%,team_name.ilike.%${query}%,position.ilike.%${query}%`).order(\"name\").limit(limit);\n        if (error) {\n            console.error(\"Error searching nrl_players:\", error);\n            return [];\n        }\n        console.log(`✅ Search found ${data?.length || 0} players in nrl_players table`);\n        // Transform search results to match our Player interface\n        const transformedResults = data?.map((player)=>{\n            const stats = player.statistics || {};\n            const totalPoints = stats.total_points || 0;\n            const gamesPlayed = stats.games_played || 1;\n            const averagePoints = gamesPlayed > 0 ? totalPoints / gamesPlayed : 0;\n            return {\n                id: player.id.toString(),\n                name: player.name,\n                position: player.position || \"Unknown\",\n                team: player.team_name || \"Unknown Team\",\n                price: stats.price || 300000,\n                points: totalPoints,\n                average: averagePoints,\n                form: stats.form || averagePoints / 10,\n                ownership: stats.ownership || 0,\n                breakeven: stats.breakeven || 0,\n                image_url: player.image_url,\n                created_at: player.created_at,\n                updated_at: player.updated_at\n            };\n        }) || [];\n        return transformedResults;\n    }\n    // Get top performers\n    static async getTopPerformers(limit = 10) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"points\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching top performers:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Get price risers\n    static async getPriceRisers(limit = 10) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"price\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching price risers:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Update player data\n    static async updatePlayer(id, updates) {\n        const { data, error } = await supabase.from(\"players\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", id).select().single();\n        if (error) {\n            console.error(\"Error updating player:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Trade recommendation service\nclass TradeService {\n    // Get trade recommendations for user\n    static async getTradeRecommendations(userId, limit = 10) {\n        const { data, error } = await supabase.from(\"trade_recommendations\").select(`\n        *,\n        player_out:players!player_out_id(*),\n        player_in:players!player_in_id(*)\n      `).eq(\"user_id\", userId).order(\"confidence\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching trade recommendations:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Create trade recommendation\n    static async createTradeRecommendation(recommendation) {\n        const { data, error } = await supabase.from(\"trade_recommendations\").insert({\n            ...recommendation,\n            created_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating trade recommendation:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Execute trade (log the trade)\n    static async executeTrade(tradeId, userId) {\n        const { data, error } = await supabase.from(\"executed_trades\").insert({\n            trade_recommendation_id: tradeId,\n            user_id: userId,\n            executed_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error executing trade:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Injury service\nclass InjuryService {\n    // Get current injury reports\n    static async getInjuryReports(limit = 10) {\n        const { data, error } = await supabase.from(\"injury_reports\").select(`\n        *,\n        player:players(*)\n      `).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching injury reports:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Create injury report\n    static async createInjuryReport(report) {\n        const { data, error } = await supabase.from(\"injury_reports\").insert({\n            ...report,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating injury report:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// User squad service\nclass SquadService {\n    // Get user's squad\n    static async getUserSquad(userId) {\n        const { data, error } = await supabase.from(\"user_squads\").select(`\n        *,\n        player:players(*)\n      `).eq(\"user_id\", userId).order(\"position_in_squad\");\n        if (error) {\n            console.error(\"Error fetching user squad:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Add player to squad\n    static async addPlayerToSquad(squadEntry) {\n        const { data, error } = await supabase.from(\"user_squads\").insert({\n            ...squadEntry,\n            created_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error adding player to squad:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Set captain\n    static async setCaptain(userId, playerId) {\n        // First, remove captain status from all players\n        await supabase.from(\"user_squads\").update({\n            is_captain: false,\n            is_vice_captain: false\n        }).eq(\"user_id\", userId);\n        // Then set the new captain\n        const { data, error } = await supabase.from(\"user_squads\").update({\n            is_captain: true\n        }).eq(\"user_id\", userId).eq(\"player_id\", playerId).select().single();\n        if (error) {\n            console.error(\"Error setting captain:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Analytics service\nclass AnalyticsService {\n    // Get dashboard stats\n    static async getDashboardStats() {\n        const [playersCount, teamsCount, injuriesCount] = await Promise.all([\n            supabase.from(\"players\").select(\"id\", {\n                count: \"exact\",\n                head: true\n            }),\n            supabase.from(\"players\").select(\"team\", {\n                count: \"exact\",\n                head: true\n            }).distinct(),\n            supabase.from(\"injury_reports\").select(\"id\", {\n                count: \"exact\",\n                head: true\n            })\n        ]);\n        return {\n            total_players: playersCount.count || 0,\n            total_teams: 17,\n            active_injuries: injuriesCount.count || 0,\n            last_updated: new Date().toISOString()\n        };\n    }\n    // Get trending players\n    static async getTrendingPlayers(limit = 10) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"form\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching trending players:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Cache service for offline support\nclass CacheService {\n    static{\n        this.CACHE_PREFIX = \"fantasypro_cache_\";\n    }\n    static{\n        this.CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n        ;\n    }\n    static set(key, data) {\n        const cacheData = {\n            data,\n            timestamp: Date.now()\n        };\n        localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheData));\n    }\n    static get(key) {\n        const cached = localStorage.getItem(this.CACHE_PREFIX + key);\n        if (!cached) return null;\n        const { data, timestamp } = JSON.parse(cached);\n        if (Date.now() - timestamp > this.CACHE_DURATION) {\n            localStorage.removeItem(this.CACHE_PREFIX + key);\n            return null;\n        }\n        return data;\n    }\n    static clear() {\n        Object.keys(localStorage).forEach((key)=>{\n            if (key.startsWith(this.CACHE_PREFIX)) {\n                localStorage.removeItem(key);\n            }\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/services/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Ftest-data-transform&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ctest-data-transform.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();