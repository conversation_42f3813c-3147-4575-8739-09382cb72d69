/**
 * Supabase Player Data Service
 * Connects to Supabase to get the complete 581 NRL player dataset
 */

import { supabase } from './supabase';

interface SupabasePlayer {
  id: number;
  sportradar_id?: string;
  name: string;
  team_id?: string;
  team_name?: string;
  position: string;
  jersey_number?: number;
  height?: number;
  weight?: number;
  age?: number;
  date_of_birth?: string;
  statistics?: any;
  created_at?: string;
  updated_at?: string;
}

interface SupabasePlayerStats {
  id: number;
  name: string;
  position?: string;
  team?: string;
  price?: number;
  breakeven?: number;
  average_points?: number;
  total_points?: number;
  games_played?: number;
  minutes_played?: number;
  tries?: number;
  try_assists?: number;
  linebreaks?: number;
  tackles?: number;
  created_at?: string;
  updated_at?: string;
}

interface FantasyProPlayer {
  id: string;
  name: string;
  team: string;
  position: string;
  price: number;
  points: number;
  average: number;
  form: number;
  ownership?: number;
  breakeven?: number;
  games_played?: number;
  minutes_per_game?: number;
  source: string;
  last_updated: string;
}

export class SupabasePlayerService {
  
  /**
   * Get all players from Supabase nrl_players table
   */
  static async getAllPlayersFromSupabase(): Promise<FantasyProPlayer[]> {
    try {
      console.log('🔄 Fetching all players from Supabase...');
      
      // Try nrl_players table first
      const { data: players, error: playersError } = await supabase
        .from('nrl_players')
        .select('*')
        .order('name');
      
      if (playersError) {
        console.warn('⚠️ nrl_players table error:', playersError);
        
        // Try nrl_player_stats table as fallback
        return await this.getAllPlayersFromStatsTable();
      }
      
      if (players && players.length > 0) {
        console.log(`✅ Found ${players.length} players in nrl_players table`);
        return this.transformSupabasePlayersToFantasyPro(players);
      }
      
      // If no players found, try stats table
      return await this.getAllPlayersFromStatsTable();
      
    } catch (error) {
      console.error('❌ Error fetching players from Supabase:', error);
      return [];
    }
  }
  
  /**
   * Get players from nrl_player_stats table as fallback
   */
  static async getAllPlayersFromStatsTable(): Promise<FantasyProPlayer[]> {
    try {
      console.log('🔄 Trying nrl_player_stats table...');
      
      const { data: playerStats, error: statsError } = await supabase
        .from('nrl_player_stats')
        .select('*')
        .order('name');
      
      if (statsError) {
        console.warn('⚠️ nrl_player_stats table error:', statsError);
        return [];
      }
      
      if (playerStats && playerStats.length > 0) {
        console.log(`✅ Found ${playerStats.length} players in nrl_player_stats table`);
        return this.transformStatsPlayersToFantasyPro(playerStats);
      }
      
      return [];
      
    } catch (error) {
      console.error('❌ Error fetching from nrl_player_stats:', error);
      return [];
    }
  }
  
  /**
   * Search players in Supabase
   */
  static async searchPlayers(query: string, limit: number = 20): Promise<FantasyProPlayer[]> {
    try {
      console.log(`🔍 Searching Supabase for: "${query}"`);
      
      const { data: players, error } = await supabase
        .from('nrl_players')
        .select('*')
        .or(`name.ilike.%${query}%,team_name.ilike.%${query}%,position.ilike.%${query}%`)
        .limit(limit)
        .order('name');
      
      if (error) {
        console.warn('⚠️ Search error:', error);
        return [];
      }
      
      if (players && players.length > 0) {
        console.log(`✅ Search found ${players.length} players`);
        return this.transformSupabasePlayersToFantasyPro(players);
      }
      
      return [];
      
    } catch (error) {
      console.error('❌ Error searching players:', error);
      return [];
    }
  }
  
  /**
   * Get players by position
   */
  static async getPlayersByPosition(position: string): Promise<FantasyProPlayer[]> {
    try {
      console.log(`🎯 Getting players by position: ${position}`);
      
      const { data: players, error } = await supabase
        .from('nrl_players')
        .select('*')
        .eq('position', position)
        .order('name');
      
      if (error) {
        console.warn('⚠️ Position filter error:', error);
        return [];
      }
      
      if (players && players.length > 0) {
        console.log(`✅ Found ${players.length} ${position} players`);
        return this.transformSupabasePlayersToFantasyPro(players);
      }
      
      return [];
      
    } catch (error) {
      console.error('❌ Error getting players by position:', error);
      return [];
    }
  }
  
  /**
   * Get players by team
   */
  static async getPlayersByTeam(teamName: string): Promise<FantasyProPlayer[]> {
    try {
      console.log(`🏉 Getting players by team: ${teamName}`);
      
      const { data: players, error } = await supabase
        .from('nrl_players')
        .select('*')
        .ilike('team_name', `%${teamName}%`)
        .order('name');
      
      if (error) {
        console.warn('⚠️ Team filter error:', error);
        return [];
      }
      
      if (players && players.length > 0) {
        console.log(`✅ Found ${players.length} ${teamName} players`);
        return this.transformSupabasePlayersToFantasyPro(players);
      }
      
      return [];
      
    } catch (error) {
      console.error('❌ Error getting players by team:', error);
      return [];
    }
  }
  
  /**
   * Transform Supabase nrl_players data to FantasyPro format
   */
  static transformSupabasePlayersToFantasyPro(players: SupabasePlayer[]): FantasyProPlayer[] {
    return players.map((player, index) => {
      const stats = player.statistics || {};
      
      return {
        id: player.id.toString(),
        name: player.name,
        team: player.team_name || 'Unknown Team',
        position: player.position || 'Unknown',
        price: stats.price || stats.current_price || 300000, // Default price
        points: stats.total_points || stats.points || 0,
        average: stats.average_points || stats.average || 0,
        form: this.calculateForm(stats.average_points || stats.average || 0),
        ownership: stats.ownership || stats.ownership_percentage || 0,
        breakeven: stats.breakeven || stats.be || 0,
        games_played: stats.games_played || stats.played || 0,
        minutes_per_game: stats.minutes_per_game || stats.mins || 80,
        source: 'supabase',
        last_updated: new Date().toISOString()
      };
    });
  }
  
  /**
   * Transform nrl_player_stats data to FantasyPro format
   */
  static transformStatsPlayersToFantasyPro(playerStats: SupabasePlayerStats[]): FantasyProPlayer[] {
    return playerStats.map((player, index) => {
      return {
        id: player.id.toString(),
        name: player.name,
        team: player.team || 'Unknown Team',
        position: player.position || 'Unknown',
        price: player.price || 300000,
        points: player.total_points || 0,
        average: player.average_points || 0,
        form: this.calculateForm(player.average_points || 0),
        ownership: 0, // Not available in stats table
        breakeven: player.breakeven || 0,
        games_played: player.games_played || 0,
        minutes_per_game: Math.round((player.minutes_played || 0) / Math.max(player.games_played || 1, 1)),
        source: 'supabase_stats',
        last_updated: new Date().toISOString()
      };
    });
  }
  
  /**
   * Calculate form rating from average points
   */
  static calculateForm(averagePoints: number): number {
    // Simple form calculation: average / 10, capped at 10
    return Math.min(10, Math.max(0, averagePoints / 10));
  }
  
  /**
   * Get database statistics
   */
  static async getDatabaseStats(): Promise<any> {
    try {
      // Get player count from nrl_players
      const { count: playersCount, error: playersError } = await supabase
        .from('nrl_players')
        .select('*', { count: 'exact', head: true });
      
      // Get stats count from nrl_player_stats
      const { count: statsCount, error: statsError } = await supabase
        .from('nrl_player_stats')
        .select('*', { count: 'exact', head: true });
      
      // Get teams count
      const { count: teamsCount, error: teamsError } = await supabase
        .from('nrl_teams')
        .select('*', { count: 'exact', head: true });
      
      return {
        nrl_players_count: playersCount || 0,
        nrl_player_stats_count: statsCount || 0,
        nrl_teams_count: teamsCount || 0,
        last_checked: new Date().toISOString(),
        errors: {
          players: playersError?.message,
          stats: statsError?.message,
          teams: teamsError?.message
        }
      };
      
    } catch (error) {
      console.error('❌ Error getting database stats:', error);
      return {
        nrl_players_count: 0,
        nrl_player_stats_count: 0,
        nrl_teams_count: 0,
        last_checked: new Date().toISOString(),
        error: error.message
      };
    }
  }
  
  /**
   * Test Supabase connection
   */
  static async testConnection(): Promise<boolean> {
    try {
      console.log('🔗 Testing Supabase connection...');
      
      const { data, error } = await supabase
        .from('nrl_players')
        .select('count')
        .limit(1);
      
      if (error) {
        console.warn('⚠️ Connection test failed:', error);
        return false;
      }
      
      console.log('✅ Supabase connection successful');
      return true;
      
    } catch (error) {
      console.error('❌ Connection test error:', error);
      return false;
    }
  }
}

export default SupabasePlayerService;
