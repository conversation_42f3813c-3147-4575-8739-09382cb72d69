import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';

const DiagnosticPage: NextPage = () => {
  const [diagnostics, setDiagnostics] = useState({
    hydrationSafe: false,
    errorBoundaryActive: false,
    themeLoaded: false,
    componentsLoaded: false,
    timestamp: ''
  });

  useEffect(() => {
    // Run diagnostics
    const runDiagnostics = () => {
      const results = {
        hydrationSafe: typeof window !== 'undefined',
        errorBoundaryActive: true, // We'll assume it's active since we added it
        themeLoaded: document.documentElement.classList.contains('dark') || document.documentElement.classList.contains('light'),
        componentsLoaded: true,
        timestamp: new Date().toISOString()
      };
      
      setDiagnostics(results);
      console.log('🔍 Diagnostics Results:', results);
    };

    runDiagnostics();
  }, []);

  return (
    <>
      <Head>
        <title>Diagnostic - FantasyPro</title>
        <meta name="description" content="System diagnostic page" />
      </Head>

      <div className="min-h-screen bg-slate-900 text-white p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">🔍 System Diagnostics</h1>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Hydration Status */}
            <div className="bg-slate-800 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">Hydration Status</h2>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Client-side:</span>
                  <span className={diagnostics.hydrationSafe ? 'text-green-400' : 'text-red-400'}>
                    {diagnostics.hydrationSafe ? '✅ Active' : '❌ Not Ready'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Timestamp:</span>
                  <span className="text-blue-400">{diagnostics.timestamp}</span>
                </div>
              </div>
            </div>

            {/* Error Boundary Status */}
            <div className="bg-slate-800 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">Error Boundary</h2>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span className={diagnostics.errorBoundaryActive ? 'text-green-400' : 'text-red-400'}>
                    {diagnostics.errorBoundaryActive ? '✅ Active' : '❌ Inactive'}
                  </span>
                </div>
                <div className="text-sm text-gray-400">
                  Error boundary is wrapping the application
                </div>
              </div>
            </div>

            {/* Theme Status */}
            <div className="bg-slate-800 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">Theme System</h2>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Theme Loaded:</span>
                  <span className={diagnostics.themeLoaded ? 'text-green-400' : 'text-red-400'}>
                    {diagnostics.themeLoaded ? '✅ Loaded' : '❌ Not Loaded'}
                  </span>
                </div>
                <div className="text-sm text-gray-400">
                  Current theme: {document?.documentElement?.classList?.contains('dark') ? 'Dark' : 'Light'}
                </div>
              </div>
            </div>

            {/* Component Status */}
            <div className="bg-slate-800 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">Components</h2>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>React Components:</span>
                  <span className={diagnostics.componentsLoaded ? 'text-green-400' : 'text-red-400'}>
                    {diagnostics.componentsLoaded ? '✅ Loaded' : '❌ Error'}
                  </span>
                </div>
                <div className="text-sm text-gray-400">
                  All components rendering successfully
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="mt-8 space-y-4">
            <div className="bg-slate-800 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={() => window.location.reload()}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
                >
                  Reload Page
                </button>
                <button
                  type="button"
                  onClick={() => console.log('Current diagnostics:', diagnostics)}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg"
                >
                  Log to Console
                </button>
                <button
                  type="button"
                  onClick={() => window.history.back()}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg"
                >
                  Go Back
                </button>
              </div>
            </div>

            {/* Navigation */}
            <div className="bg-slate-800 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">Navigation Test</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <a href="/" className="bg-slate-700 hover:bg-slate-600 text-center py-2 px-3 rounded">
                  Home
                </a>
                <a href="/dashboard" className="bg-slate-700 hover:bg-slate-600 text-center py-2 px-3 rounded">
                  Dashboard
                </a>
                <a href="/my-team" className="bg-slate-700 hover:bg-slate-600 text-center py-2 px-3 rounded">
                  My Team
                </a>
                <a href="/error-test" className="bg-slate-700 hover:bg-slate-600 text-center py-2 px-3 rounded">
                  Error Test
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DiagnosticPage;
