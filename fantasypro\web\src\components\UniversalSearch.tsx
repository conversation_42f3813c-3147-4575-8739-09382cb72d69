import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  UserIcon,
  TrophyIcon,
  ArrowTrendingUpIcon,
  StarIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import APIService from '../services/api';

interface Player {
  id: string;
  name: string;
  position: string;
  team: string;
  price: number;
  points: number;
  form: number;
  ownership: number;
  breakeven: number;
  image?: string;
}

interface SearchResult {
  player: Player;
  relevanceScore: number;
  matchType: 'name' | 'position' | 'team';
}

interface UniversalSearchProps {
  placeholder?: string;
  onPlayerSelect?: (player: Player) => void;
  onCaptainSelect?: (player: Player) => void;
  showCaptainOption?: boolean;
  maxResults?: number;
  className?: string;
}

const UniversalSearch: React.FC<UniversalSearchProps> = ({
  placeholder = "Search 581 NRL players...",
  onPlayerSelect,
  onCaptainSelect,
  showCaptainOption = false,
  maxResults = 8,
  className = ""
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [recentSearches, setRecentSearches] = useState<Player[]>([]);
  const [allPlayers, setAllPlayers] = useState<Player[]>([]);

  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Mock NRL players data - in production this would come from API
  const mockPlayers: Player[] = [
    {
      id: '1',
      name: 'Nathan Cleary',
      position: 'Halfback',
      team: 'Penrith Panthers',
      price: 750000,
      points: 1250,
      form: 8.5,
      ownership: 45.2,
      breakeven: 65
    },
    {
      id: '2',
      name: 'Kalyn Ponga',
      position: 'Fullback',
      team: 'Newcastle Knights',
      price: 680000,
      points: 1180,
      form: 7.8,
      ownership: 38.7,
      breakeven: 72
    },
    {
      id: '3',
      name: 'James Tedesco',
      position: 'Fullback',
      team: 'Sydney Roosters',
      price: 720000,
      points: 1320,
      form: 9.2,
      ownership: 52.1,
      breakeven: 58
    },
    {
      id: '4',
      name: 'Daly Cherry-Evans',
      position: 'Halfback',
      team: 'Manly Sea Eagles',
      price: 650000,
      points: 1150,
      form: 7.5,
      ownership: 28.3,
      breakeven: 68
    },
    {
      id: '5',
      name: 'Cameron Munster',
      position: 'Five-eighth',
      team: 'Melbourne Storm',
      price: 700000,
      points: 1200,
      form: 8.1,
      ownership: 41.5,
      breakeven: 62
    }
  ];

  // Load real player data on component mount
  useEffect(() => {
    const loadPlayers = async () => {
      try {
        console.log('🔍 UniversalSearch loading real player data...');
        const response = await fetch('/api/players');

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data.players) {
            console.log(`✅ UniversalSearch loaded ${data.data.players.length} real players`);
            setAllPlayers(data.data.players);
            return;
          }
        }

        console.warn('⚠️ Failed to load real players, using mock data');
        setAllPlayers(mockPlayers);
      } catch (error) {
        console.error('❌ Error loading players for search:', error);
        setAllPlayers(mockPlayers);
      }
    };

    loadPlayers();
  }, []);

  // Cloud-powered predictive search algorithm
  const searchPlayers = async (searchQuery: string): Promise<SearchResult[]> => {
    if (!searchQuery.trim()) return [];

    setIsLoading(true);

    try {
      // Use loaded player data for search
      const players = allPlayers.length > 0 ? allPlayers : mockPlayers;

      const query = searchQuery.toLowerCase();
      const searchResults: SearchResult[] = [];

      players.forEach(player => {
        let relevanceScore = 0;
        let matchType: 'name' | 'position' | 'team' = 'name';

        // Name matching (highest priority)
        if (player.name.toLowerCase().includes(query)) {
          relevanceScore += player.name.toLowerCase().startsWith(query) ? 100 : 80;
          matchType = 'name';
        }

        // Position matching
        if (player.position.toLowerCase().includes(query)) {
          relevanceScore += 60;
          matchType = 'position';
        }

        // Team matching
        if (player.team.toLowerCase().includes(query)) {
          relevanceScore += 40;
          matchType = 'team';
        }

        // Boost score for high-performing players
        relevanceScore += player.form * 2;
        relevanceScore += (player.points / 100);

        searchResults.push({
          player,
          relevanceScore,
          matchType
        });
      });

      // Sort by relevance score
      searchResults.sort((a, b) => b.relevanceScore - a.relevanceScore);

      setIsLoading(false);
      return searchResults;
    } catch (error) {
      console.error('Search failed:', error);
      setIsLoading(false);
      return [];
    }
  };

  // Handle search input
  useEffect(() => {
    const delayedSearch = setTimeout(async () => {
      if (query.length >= 2) {
        const searchResults = await searchPlayers(query);
        setResults(searchResults);
        setIsOpen(true);
      } else {
        setResults([]);
        setIsOpen(false);
      }
      setSelectedIndex(-1);
    }, 200);

    return () => clearTimeout(delayedSearch);
  }, [query]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < results.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
          break;
        case 'Enter':
          e.preventDefault();
          if (selectedIndex >= 0 && results[selectedIndex]) {
            handlePlayerSelect(results[selectedIndex].player);
          }
          break;
        case 'Escape':
          setIsOpen(false);
          setSelectedIndex(-1);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, results, selectedIndex]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handlePlayerSelect = (player: Player) => {
    setQuery(player.name);
    setIsOpen(false);
    setSelectedIndex(-1);
    
    // Add to recent searches
    setRecentSearches(prev => {
      const filtered = prev.filter(p => p.id !== player.id);
      return [player, ...filtered].slice(0, 5);
    });

    onPlayerSelect?.(player);
  };

  const handleCaptainSelect = (player: Player) => {
    onCaptainSelect?.(player);
    setIsOpen(false);
  };

  const clearSearch = () => {
    setQuery('');
    setResults([]);
    setIsOpen(false);
    inputRef.current?.focus();
  };

  const getPositionColor = (position: string) => {
    const colors: Record<string, string> = {
      'Fullback': 'text-blue-400',
      'Halfback': 'text-green-400',
      'Five-eighth': 'text-purple-400',
      'Hooker': 'text-yellow-400',
      'Prop': 'text-red-400',
      'Second Row': 'text-orange-400',
      'Lock': 'text-pink-400',
      'Winger': 'text-cyan-400',
      'Centre': 'text-indigo-400'
    };
    return colors[position] || 'text-gray-400';
  };

  return (
    <div ref={searchRef} className={`relative search-container dropdown-container ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 themed-text-tertiary" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => {
            if (results.length > 0) setIsOpen(true);
          }}
          placeholder={placeholder}
          className="input-primary w-full pl-10 pr-10"
        />

        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <XMarkIcon className="h-4 w-4 themed-text-tertiary hover:themed-text-primary" />
          </button>
        )}

        {isLoading && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <div className="w-4 h-4 border-2 border-green-400 border-t-transparent rounded-full animate-spin" />
          </div>
        )}
      </div>

      {/* Search Results Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="search-dropdown dropdown-content themed-bg-secondary themed-border rounded-lg themed-shadow-xl overflow-hidden"
          >
            {results.length > 0 ? (
              <div className="max-h-96 overflow-y-auto">
                {results.map((result, index) => (
                  <motion.div
                    key={result.player.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.05 }}
                    className={`p-4 cursor-pointer transition-colors ${
                      index === selectedIndex 
                        ? 'themed-bg-tertiary' 
                        : 'hover:themed-bg-tertiary'
                    }`}
                    onClick={() => handlePlayerSelect(result.player)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 themed-bg-primary rounded-full flex items-center justify-center">
                          <UserIcon className="w-5 h-5 themed-text-secondary" />
                        </div>
                        
                        <div>
                          <div className="font-medium themed-text-primary">
                            {result.player.name}
                          </div>
                          <div className="text-sm themed-text-tertiary">
                            <span className={getPositionColor(result.player.position)}>
                              {result.player.position}
                            </span>
                            {' • '}
                            {result.player.team}
                          </div>
                        </div>
                      </div>

                      <div className="text-right space-y-1">
                        <div className="text-sm font-medium themed-text-primary">
                          ${((result.player.price || 0) / 1000).toFixed(0)}k
                        </div>
                        <div className="text-xs themed-text-tertiary">
                          {result.player.points} pts
                        </div>
                      </div>
                    </div>

                    {showCaptainOption && (
                      <div className="mt-3 pt-3 border-t themed-border">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCaptainSelect(result.player);
                          }}
                          className="w-full btn-outline btn-ripple text-sm flex items-center justify-center space-x-2"
                        >
                          <StarIcon className="w-4 h-4" />
                          <span>Set as Captain</span>
                        </button>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            ) : query.length >= 2 ? (
              <div className="p-4 text-center themed-text-tertiary">
                No players found for "{query}"
              </div>
            ) : (
              <div className="p-4 text-center themed-text-tertiary">
                Type at least 2 characters to search
              </div>
            )}

            {/* Search Stats */}
            <div className="px-4 py-2 bg-slate-800/50 border-t themed-border">
              <div className="text-xs themed-text-tertiary text-center">
                {results.length > 0 && (
                  <>Showing {results.length} of 581 players • </>
                )}
                Powered by AI predictive search
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default UniversalSearch;
