import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import { motion } from 'framer-motion';
import Layout from '../components/Layout';
import PredictiveSearch from '../components/PredictiveSearch';
import ErrorBoundary from '../components/ErrorBoundary';
import APIService from '../services/api';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ChartBarIcon,
  TrophyIcon,
  CurrencyDollarIcon,
  FireIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  StarIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

interface Player {
  id: string;
  name: string;
  team: string;
  position: string;
  price: number;
  points: number;
  average: number;
  form: number;
  ownership?: number;
  breakeven?: number;
  injury_status?: string;
}

const Players: NextPage = () => {
  const [players, setPlayers] = useState<Player[]>([]);
  const [filteredPlayers, setFilteredPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [filters, setFilters] = useState({
    position: '',
    team: '',
    priceRange: '',
    sortBy: 'points'
  });
  const [searchTerm, setSearchTerm] = useState('');

  const positions = ['FLB', 'CTW', 'HFB', '5/8', 'HOK', 'FRF', '2RF', 'LCK'];
  const teams = [
    'Brisbane Broncos', 'Sydney Roosters', 'Melbourne Storm', 'Penrith Panthers',
    'Cronulla Sharks', 'Manly Sea Eagles', 'South Sydney Rabbitohs', 'Parramatta Eels',
    'Newcastle Knights', 'Canberra Raiders', 'Gold Coast Titans', 'St George Illawarra Dragons',
    'Canterbury Bulldogs', 'Wests Tigers', 'North Queensland Cowboys', 'New Zealand Warriors',
    'Dolphins'
  ];

  useEffect(() => {
    const fetchPlayers = async () => {
      try {
        const playersData = await APIService.getAllPlayers();
        setPlayers(playersData);
        setFilteredPlayers(playersData);
      } catch (error) {
        console.error('Error fetching players:', error);
        // Fallback to mock data
        const mockPlayers: Player[] = [
          {
            id: '1',
            name: 'James Tedesco',
            team: 'Sydney Roosters',
            position: 'FLB',
            price: 817700,
            points: 1247,
            average: 88.0,
            form: 8.5,
            ownership: 45.2,
            breakeven: 65
          },
          {
            id: '2',
            name: 'Herbie Farnworth',
            team: 'Dolphins',
            position: 'CTW',
            price: 815400,
            points: 1161,
            average: 82.9,
            form: 8.7,
            ownership: 38.1,
            breakeven: 58
          },
          // Add more mock players...
        ];
        setPlayers(mockPlayers);
        setFilteredPlayers(mockPlayers);
      } finally {
        setLoading(false);
      }
    };

    fetchPlayers();
  }, []);

  useEffect(() => {
    let filtered = [...players];

    // Apply filters
    if (filters.position) {
      filtered = filtered.filter(p => p.position === filters.position);
    }
    if (filters.team) {
      filtered = filtered.filter(p => p.team === filters.team);
    }
    if (filters.priceRange) {
      const [min, max] = filters.priceRange.split('-').map(Number);
      filtered = filtered.filter(p => p.price >= min && p.price <= max);
    }

    // Apply search
    if (searchTerm) {
      filtered = filtered.filter(p => 
        p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.team.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'points':
          return b.points - a.points;
        case 'average':
          return b.average - a.average;
        case 'price':
          return b.price - a.price;
        case 'form':
          return b.form - a.form;
        case 'ownership':
          return (b.ownership || 0) - (a.ownership || 0);
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

    setFilteredPlayers(filtered);
  }, [players, filters, searchTerm]);

  const formatCurrency = (amount: number | undefined | null) => {
    if (!amount || amount === 0) return '$0.00M';
    return `$${(amount / 1000000).toFixed(2)}M`;
  };

  const getFormColor = (form: number) => {
    if (form >= 8) return 'text-green-400';
    if (form >= 6) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getPositionColor = (position: string) => {
    const colors: { [key: string]: string } = {
      'FLB': 'bg-purple-600/20 text-purple-400',
      'CTW': 'bg-blue-600/20 text-blue-400',
      'HFB': 'bg-green-600/20 text-green-400',
      '5/8': 'bg-green-600/20 text-green-400',
      'HOK': 'bg-orange-600/20 text-orange-400',
      'FRF': 'bg-red-600/20 text-red-400',
      '2RF': 'bg-red-600/20 text-red-400',
      'LCK': 'bg-red-600/20 text-red-400'
    };
    return colors[position] || 'bg-slate-600/20 text-slate-400';
  };

  const handlePlayerSelect = (player: any) => {
    setSelectedPlayer(player);
  };

  const handleAddToTeam = (player: Player) => {
    alert(`${player.name} added to team! (Feature coming soon)`);
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Players - FantasyPro</title>
        <meta name="description" content="Browse and analyze all NRL SuperCoach players" />
      </Head>

      <ErrorBoundary>
        <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold themed-text-primary">Players</h1>
            <p className="themed-text-tertiary mt-1">Browse and analyze all {players.length} NRL SuperCoach players</p>
          </div>
          <div className="flex items-center space-x-4">
            <span className="status-live">{filteredPlayers.length} Players</span>
          </div>
        </div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="card-premium p-6"
        >
          <div className="flex items-center space-x-3 mb-6">
            <MagnifyingGlassIcon className="w-6 h-6 text-green-400" />
            <h2 className="text-xl font-semibold themed-text-primary">Search & Filter Players</h2>
          </div>

          <div className="mb-6">
            <div className="max-w-2xl mx-auto">
              <label className="block text-sm font-medium themed-text-secondary mb-2 text-center">
                🔍 Search & Discover Players
              </label>
              <PredictiveSearch
                placeholder="Type 'Na' for Nathan Cleary, 'Re' for Reece Walsh, 'FLB' for Fullbacks, 'Storm' for Melbourne..."
                onPlayerSelect={handlePlayerSelect}
                onSearchChange={(query, results) => {
                  setSearchTerm(query);
                  // Update filtered players based on search results
                  if (results.length > 0) {
                    setFilteredPlayers(results);
                  } else if (query === '') {
                    // Reset to all players when search is cleared
                    setFilteredPlayers(players);
                  }
                }}
                showPlayerDetails={true}
                maxResults={12}
                minQueryLength={1}
                clearOnSelect={false}
                className="w-full"
              />
              <div className="text-center mt-2 text-xs themed-text-tertiary">
                Search by player name, team, or position • Click any result to view details
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">Position</label>
              <select
                className="input-primary w-full"
                value={filters.position}
                onChange={(e) => setFilters({ ...filters, position: e.target.value })}
              >
                <option value="">All Positions</option>
                {positions.map(pos => (
                  <option key={pos} value={pos}>{pos}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">Team</label>
              <select
                className="input-primary w-full"
                value={filters.team}
                onChange={(e) => setFilters({ ...filters, team: e.target.value })}
              >
                <option value="">All Teams</option>
                {teams.map(team => (
                  <option key={team} value={team}>{team}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">Price Range</label>
              <select
                className="input-primary w-full"
                value={filters.priceRange}
                onChange={(e) => setFilters({ ...filters, priceRange: e.target.value })}
              >
                <option value="">All Prices</option>
                <option value="0-400000">Under $400k</option>
                <option value="400000-600000">$400k - $600k</option>
                <option value="600000-800000">$600k - $800k</option>
                <option value="800000-1200000">$800k+</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">Sort By</label>
              <select
                className="input-primary w-full"
                value={filters.sortBy}
                onChange={(e) => setFilters({ ...filters, sortBy: e.target.value })}
              >
                <option value="points">Total Points</option>
                <option value="average">Average</option>
                <option value="price">Price</option>
                <option value="form">Form</option>
                <option value="ownership">Ownership</option>
                <option value="name">Name</option>
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={() => {
                  setFilters({ position: '', team: '', priceRange: '', sortBy: 'points' });
                  setSearchTerm('');
                }}
                className="btn-outline w-full"
              >
                Clear Filters
              </button>
            </div>

            <div className="flex items-end">
              <button
                onClick={() => window.location.reload()}
                className="btn-secondary w-full"
              >
                Refresh Data
              </button>
            </div>
          </div>
        </motion.div>

        {/* Players Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="card"
        >
          <div className="p-6 border-b themed-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <UserGroupIcon className="w-5 h-5 text-blue-400" />
                <h2 className="text-lg font-semibold themed-text-primary">All Players</h2>
                <span className="text-xs text-blue-400">
                  {filteredPlayers.length} players found
                </span>
              </div>
              <div className="text-sm themed-text-tertiary">
                Click players to view details and add to team
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredPlayers.map((player, index) => (
                <motion.div
                  key={player.id}
                  className="card-hover p-4 cursor-pointer"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.02 }}
                  onClick={() => setSelectedPlayer(player)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold themed-text-primary text-sm mb-1">
                        {player.name}
                      </h3>
                      <p className="text-xs themed-text-tertiary">{player.team}</p>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getPositionColor(player.position)}`}>
                      {player.position}
                    </span>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span className="themed-text-secondary">Price:</span>
                      <span className="themed-text-primary font-medium">{formatCurrency(player.price)}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span className="themed-text-secondary">Points:</span>
                      <span className="themed-text-primary font-medium">{player.points.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span className="themed-text-secondary">Average:</span>
                      <span className="themed-text-primary font-medium">{(player.average || 0).toFixed(1)}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span className="themed-text-secondary">Form:</span>
                      <span className={`font-medium ${getFormColor(player.form || 0)}`}>
                        {(player.form || 0).toFixed(1)}/10
                      </span>
                    </div>
                    {player.ownership && (
                      <div className="flex justify-between text-xs">
                        <span className="themed-text-secondary">Owned:</span>
                        <span className="themed-text-primary font-medium">{(player.ownership || 0).toFixed(1)}%</span>
                      </div>
                    )}
                  </div>

                  <div className="mt-3 pt-3 border-t themed-border">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddToTeam(player);
                      }}
                      className="btn-primary btn-ripple w-full text-xs py-2"
                    >
                      <PlusIcon className="w-3 h-3 mr-1" />
                      Add to Team
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>

            {filteredPlayers.length === 0 && (
              <div className="text-center py-12 themed-text-tertiary">
                <UserGroupIcon className="w-12 h-12 mx-auto mb-4 text-slate-600" />
                <div className="text-lg font-medium mb-2">No players found</div>
                <div className="text-sm mb-4">Try adjusting your filters or search terms</div>
                <button
                  onClick={() => {
                    setFilters({ position: '', team: '', priceRange: '', sortBy: 'points' });
                    setSearchTerm('');
                  }}
                  className="btn-primary"
                >
                  Clear All Filters
                </button>
              </div>
            )}
          </div>
        </motion.div>

        {/* Selected Player Details */}
        {selectedPlayer && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="card-premium p-6"
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <StarIcon className="w-6 h-6 text-yellow-400" />
                <h2 className="text-xl font-semibold themed-text-primary">Player Details</h2>
              </div>
              <button
                onClick={() => setSelectedPlayer(null)}
                className="btn-outline"
              >
                Close
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold themed-text-primary mb-4">{selectedPlayer.name}</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="themed-text-secondary">Team:</span>
                    <span className="themed-text-primary font-medium">{selectedPlayer.team}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="themed-text-secondary">Position:</span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getPositionColor(selectedPlayer.position)}`}>
                      {selectedPlayer.position}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="themed-text-secondary">Current Price:</span>
                    <span className="themed-text-primary font-medium">{formatCurrency(selectedPlayer.price)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="themed-text-secondary">Season Points:</span>
                    <span className="themed-text-primary font-medium">{selectedPlayer.points.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="themed-text-secondary">Season Average:</span>
                    <span className="themed-text-primary font-medium">{(selectedPlayer.average || 0).toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="themed-text-secondary">Form Rating:</span>
                    <span className={`font-medium ${getFormColor(selectedPlayer.form || 0)}`}>
                      {(selectedPlayer.form || 0).toFixed(1)}/10
                    </span>
                  </div>
                  {selectedPlayer.ownership && (
                    <div className="flex justify-between">
                      <span className="themed-text-secondary">Ownership:</span>
                      <span className="themed-text-primary font-medium">{(selectedPlayer.ownership || 0).toFixed(1)}%</span>
                    </div>
                  )}
                  {selectedPlayer.breakeven && (
                    <div className="flex justify-between">
                      <span className="themed-text-secondary">Breakeven:</span>
                      <span className="themed-text-primary font-medium">{selectedPlayer.breakeven}</span>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold themed-text-primary mb-4">Actions</h3>
                <div className="space-y-3">
                  <button
                    onClick={() => handleAddToTeam(selectedPlayer)}
                    className="btn-primary btn-ripple w-full"
                  >
                    <PlusIcon className="w-4 h-4 mr-2" />
                    Add to My Team
                  </button>
                  <button
                    onClick={() => alert('Trade analysis coming soon!')}
                    className="btn-secondary btn-ripple w-full"
                  >
                    <ArrowTrendingUpIcon className="w-4 h-4 mr-2" />
                    Analyze Trades
                  </button>
                  <button
                    onClick={() => alert('Set as captain coming soon!')}
                    className="btn-accent btn-ripple w-full"
                  >
                    <StarIcon className="w-4 h-4 mr-2" />
                    Set as Captain
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
        </div>
      </ErrorBoundary>
    </Layout>
  );
};

export default Players;
