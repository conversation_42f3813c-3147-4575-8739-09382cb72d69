/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/dropdown-fix.css":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/dropdown-fix.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* \\n * Dropdown Z-Index Fix\\n * Ensures predictive search dropdowns appear above all other elements\\n */\\n\\n/* High z-index for all dropdown containers */\\n.dropdown-container {\\n  position: relative;\\n  z-index: 1000;\\n}\\n\\n/* Ultra-high z-index for dropdown content */\\n.dropdown-content {\\n  position: absolute !important;\\n  z-index: 9999 !important;\\n  top: 100% !important;\\n  left: 0 !important;\\n  right: 0 !important;\\n  margin-top: 8px !important;\\n}\\n\\n/* Ensure parent containers don't clip dropdowns */\\n.widget-container,\\n.card,\\n.card-premium,\\n.card-glow {\\n  overflow: visible !important;\\n}\\n\\n/* Specific fix for search containers */\\n.search-container {\\n  position: relative;\\n  z-index: 1001;\\n}\\n\\n.search-dropdown {\\n  position: absolute !important;\\n  z-index: 9999 !important;\\n  top: calc(100% + 8px) !important;\\n  left: 0 !important;\\n  right: 0 !important;\\n  width: 100% !important;\\n  max-height: 400px !important;\\n  overflow-y: auto !important;\\n  background: var(--bg-secondary) !important;\\n  border: 1px solid var(--border-color) !important;\\n  border-radius: 8px !important;\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;\\n}\\n\\n/* Dark mode specific fixes */\\n@media (prefers-color-scheme: dark) {\\n  .search-dropdown {\\n    background: #1e293b !important;\\n    border-color: #334155 !important;\\n    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2) !important;\\n  }\\n}\\n\\n/* Ensure dropdowns work in grid layouts */\\n.grid > * {\\n  overflow: visible !important;\\n}\\n\\n/* Fix for specific FantasyPro components */\\n.my-team-container,\\n.dashboard-container,\\n.players-container {\\n  overflow: visible !important;\\n}\\n\\n.my-team-container .card,\\n.dashboard-container .card,\\n.players-container .card {\\n  overflow: visible !important;\\n}\\n\\n/* Portal dropdown styles */\\n#dropdown-portal {\\n  position: absolute !important;\\n  top: 0 !important;\\n  left: 0 !important;\\n  z-index: 9999 !important;\\n  pointer-events: none;\\n}\\n\\n#dropdown-portal > * {\\n  pointer-events: auto;\\n}\\n\\n/* Animation support for dropdowns */\\n.dropdown-enter {\\n  opacity: 0;\\n  transform: translateY(-10px) scale(0.95);\\n}\\n\\n.dropdown-enter-active {\\n  opacity: 1;\\n  transform: translateY(0) scale(1);\\n  transition: opacity 150ms ease-out, transform 150ms ease-out;\\n}\\n\\n.dropdown-exit {\\n  opacity: 1;\\n  transform: translateY(0) scale(1);\\n}\\n\\n.dropdown-exit-active {\\n  opacity: 0;\\n  transform: translateY(-10px) scale(0.95);\\n  transition: opacity 150ms ease-in, transform 150ms ease-in;\\n}\\n\\n/* Responsive dropdown positioning */\\n@media (max-width: 768px) {\\n  .search-dropdown {\\n    max-height: 300px !important;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .search-dropdown {\\n    max-height: 250px !important;\\n    left: -8px !important;\\n    right: -8px !important;\\n    width: calc(100% + 16px) !important;\\n  }\\n}\\n\\n/* Ensure dropdowns don't get cut off at screen edges */\\n.search-dropdown {\\n  max-width: 100vw;\\n  box-sizing: border-box;\\n}\\n\\n/* Fix for containers with transform properties */\\n.transform,\\n.scale-95,\\n.scale-100 {\\n  transform-style: preserve-3d;\\n}\\n\\n/* Ensure search inputs have proper stacking context */\\ninput[type=\\\"text\\\"]:focus + .search-dropdown,\\ninput[type=\\\"search\\\"]:focus + .search-dropdown {\\n  z-index: 9999 !important;\\n}\\n\\n/* Override any conflicting z-index values */\\n.z-10, .z-20, .z-30, .z-40, .z-50 {\\n  z-index: auto !important;\\n}\\n\\n.search-dropdown {\\n  z-index: 9999 !important;\\n}\\n\\n/* Specific fixes for FantasyPro layout */\\n.layout-container {\\n  overflow: visible !important;\\n}\\n\\n.main-content {\\n  overflow: visible !important;\\n}\\n\\n.content-wrapper {\\n  overflow: visible !important;\\n}\\n\\n/* Ensure modals and overlays don't interfere */\\n.modal-overlay {\\n  z-index: 10000 !important;\\n}\\n\\n.dropdown-overlay {\\n  z-index: 9999 !important;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/dropdown-fix.css\"],\"names\":[],\"mappings\":\"AAAA;;;EAGE;;AAEF,6CAA6C;AAC7C;EACE,kBAAkB;EAClB,aAAa;AACf;;AAEA,4CAA4C;AAC5C;EACE,6BAA6B;EAC7B,wBAAwB;EACxB,oBAAoB;EACpB,kBAAkB;EAClB,mBAAmB;EACnB,0BAA0B;AAC5B;;AAEA,kDAAkD;AAClD;;;;EAIE,4BAA4B;AAC9B;;AAEA,uCAAuC;AACvC;EACE,kBAAkB;EAClB,aAAa;AACf;;AAEA;EACE,6BAA6B;EAC7B,wBAAwB;EACxB,gCAAgC;EAChC,kBAAkB;EAClB,mBAAmB;EACnB,sBAAsB;EACtB,4BAA4B;EAC5B,2BAA2B;EAC3B,0CAA0C;EAC1C,gDAAgD;EAChD,6BAA6B;EAC7B,gGAAgG;AAClG;;AAEA,6BAA6B;AAC7B;EACE;IACE,8BAA8B;IAC9B,gCAAgC;IAChC,+FAA+F;EACjG;AACF;;AAEA,0CAA0C;AAC1C;EACE,4BAA4B;AAC9B;;AAEA,2CAA2C;AAC3C;;;EAGE,4BAA4B;AAC9B;;AAEA;;;EAGE,4BAA4B;AAC9B;;AAEA,2BAA2B;AAC3B;EACE,6BAA6B;EAC7B,iBAAiB;EACjB,kBAAkB;EAClB,wBAAwB;EACxB,oBAAoB;AACtB;;AAEA;EACE,oBAAoB;AACtB;;AAEA,oCAAoC;AACpC;EACE,UAAU;EACV,wCAAwC;AAC1C;;AAEA;EACE,UAAU;EACV,iCAAiC;EACjC,4DAA4D;AAC9D;;AAEA;EACE,UAAU;EACV,iCAAiC;AACnC;;AAEA;EACE,UAAU;EACV,wCAAwC;EACxC,0DAA0D;AAC5D;;AAEA,oCAAoC;AACpC;EACE;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE;IACE,4BAA4B;IAC5B,qBAAqB;IACrB,sBAAsB;IACtB,mCAAmC;EACrC;AACF;;AAEA,uDAAuD;AACvD;EACE,gBAAgB;EAChB,sBAAsB;AACxB;;AAEA,iDAAiD;AACjD;;;EAGE,4BAA4B;AAC9B;;AAEA,sDAAsD;AACtD;;EAEE,wBAAwB;AAC1B;;AAEA,4CAA4C;AAC5C;EACE,wBAAwB;AAC1B;;AAEA;EACE,wBAAwB;AAC1B;;AAEA,yCAAyC;AACzC;EACE,4BAA4B;AAC9B;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,4BAA4B;AAC9B;;AAEA,+CAA+C;AAC/C;EACE,yBAAyB;AAC3B;;AAEA;EACE,wBAAwB;AAC1B\",\"sourcesContent\":[\"/* \\n * Dropdown Z-Index Fix\\n * Ensures predictive search dropdowns appear above all other elements\\n */\\n\\n/* High z-index for all dropdown containers */\\n.dropdown-container {\\n  position: relative;\\n  z-index: 1000;\\n}\\n\\n/* Ultra-high z-index for dropdown content */\\n.dropdown-content {\\n  position: absolute !important;\\n  z-index: 9999 !important;\\n  top: 100% !important;\\n  left: 0 !important;\\n  right: 0 !important;\\n  margin-top: 8px !important;\\n}\\n\\n/* Ensure parent containers don't clip dropdowns */\\n.widget-container,\\n.card,\\n.card-premium,\\n.card-glow {\\n  overflow: visible !important;\\n}\\n\\n/* Specific fix for search containers */\\n.search-container {\\n  position: relative;\\n  z-index: 1001;\\n}\\n\\n.search-dropdown {\\n  position: absolute !important;\\n  z-index: 9999 !important;\\n  top: calc(100% + 8px) !important;\\n  left: 0 !important;\\n  right: 0 !important;\\n  width: 100% !important;\\n  max-height: 400px !important;\\n  overflow-y: auto !important;\\n  background: var(--bg-secondary) !important;\\n  border: 1px solid var(--border-color) !important;\\n  border-radius: 8px !important;\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;\\n}\\n\\n/* Dark mode specific fixes */\\n@media (prefers-color-scheme: dark) {\\n  .search-dropdown {\\n    background: #1e293b !important;\\n    border-color: #334155 !important;\\n    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2) !important;\\n  }\\n}\\n\\n/* Ensure dropdowns work in grid layouts */\\n.grid > * {\\n  overflow: visible !important;\\n}\\n\\n/* Fix for specific FantasyPro components */\\n.my-team-container,\\n.dashboard-container,\\n.players-container {\\n  overflow: visible !important;\\n}\\n\\n.my-team-container .card,\\n.dashboard-container .card,\\n.players-container .card {\\n  overflow: visible !important;\\n}\\n\\n/* Portal dropdown styles */\\n#dropdown-portal {\\n  position: absolute !important;\\n  top: 0 !important;\\n  left: 0 !important;\\n  z-index: 9999 !important;\\n  pointer-events: none;\\n}\\n\\n#dropdown-portal > * {\\n  pointer-events: auto;\\n}\\n\\n/* Animation support for dropdowns */\\n.dropdown-enter {\\n  opacity: 0;\\n  transform: translateY(-10px) scale(0.95);\\n}\\n\\n.dropdown-enter-active {\\n  opacity: 1;\\n  transform: translateY(0) scale(1);\\n  transition: opacity 150ms ease-out, transform 150ms ease-out;\\n}\\n\\n.dropdown-exit {\\n  opacity: 1;\\n  transform: translateY(0) scale(1);\\n}\\n\\n.dropdown-exit-active {\\n  opacity: 0;\\n  transform: translateY(-10px) scale(0.95);\\n  transition: opacity 150ms ease-in, transform 150ms ease-in;\\n}\\n\\n/* Responsive dropdown positioning */\\n@media (max-width: 768px) {\\n  .search-dropdown {\\n    max-height: 300px !important;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .search-dropdown {\\n    max-height: 250px !important;\\n    left: -8px !important;\\n    right: -8px !important;\\n    width: calc(100% + 16px) !important;\\n  }\\n}\\n\\n/* Ensure dropdowns don't get cut off at screen edges */\\n.search-dropdown {\\n  max-width: 100vw;\\n  box-sizing: border-box;\\n}\\n\\n/* Fix for containers with transform properties */\\n.transform,\\n.scale-95,\\n.scale-100 {\\n  transform-style: preserve-3d;\\n}\\n\\n/* Ensure search inputs have proper stacking context */\\ninput[type=\\\"text\\\"]:focus + .search-dropdown,\\ninput[type=\\\"search\\\"]:focus + .search-dropdown {\\n  z-index: 9999 !important;\\n}\\n\\n/* Override any conflicting z-index values */\\n.z-10, .z-20, .z-30, .z-40, .z-50 {\\n  z-index: auto !important;\\n}\\n\\n.search-dropdown {\\n  z-index: 9999 !important;\\n}\\n\\n/* Specific fixes for FantasyPro layout */\\n.layout-container {\\n  overflow: visible !important;\\n}\\n\\n.main-content {\\n  overflow: visible !important;\\n}\\n\\n.content-wrapper {\\n  overflow: visible !important;\\n}\\n\\n/* Ensure modals and overlays don't interfere */\\n.modal-overlay {\\n  z-index: 10000 !important;\\n}\\n\\n.dropdown-overlay {\\n  z-index: 9999 !important;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/dropdown-fix.css\n"));

/***/ }),

/***/ "./src/styles/dropdown-fix.css":
/*!*************************************!*\
  !*** ./src/styles/dropdown-fix.css ***!
  \*************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./dropdown-fix.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/dropdown-fix.css\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./dropdown-fix.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/dropdown-fix.css\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./dropdown-fix.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/dropdown-fix.css\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/styles/dropdown-fix.css\n"));

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ App; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"./node_modules/next/node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_dropdown_fix_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/dropdown-fix.css */ \"./src/styles/dropdown-fix.css\");\n/* harmony import */ var _styles_dropdown_fix_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_dropdown_fix_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"./src/contexts/ThemeContext.tsx\");\n\n\n\n\n\nfunction App(param) {\n    var Component = param.Component, pageProps = param.pageProps;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n        defaultTheme: \"dark\",\n        storageKey: \"fantasypro-theme\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_4__._)({}, pageProps), void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFDK0I7QUFDSztBQUNxQjtBQUUxQyxTQUFTQyxJQUFJLEtBQWtDO1FBQWhDQyxZQUFGLE1BQUVBLFdBQVdDLFlBQWIsTUFBYUE7SUFDdkMscUJBQ0UsOERBQUNILGlFQUFhQTtRQUFDSSxjQUFhO1FBQU9DLFlBQVc7a0JBQzVDLDRFQUFDSCxXQUFBQSw2REFBQUEsS0FBY0M7Ozs7Ozs7Ozs7QUFHckI7S0FOd0JGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgJy4uL3N0eWxlcy9kcm9wZG93bi1maXguY3NzJztcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICcuLi9jb250ZXh0cy9UaGVtZUNvbnRleHQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIGRlZmF1bHRUaGVtZT1cImRhcmtcIiBzdG9yYWdlS2V5PVwiZmFudGFzeXByby10aGVtZVwiPlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvVGhlbWVQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJUaGVtZVByb3ZpZGVyIiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiZGVmYXVsdFRoZW1lIiwic3RvcmFnZUtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n"));

/***/ })

});