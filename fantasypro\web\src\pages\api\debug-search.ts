import { NextApiRequest, NextApiResponse } from 'next';
import { PlayerService } from '../../services/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Debug: Testing search functionality...');
    
    // Test 1: Check if PlayerService.getAllPlayers() works
    console.log('Test 1: Getting all players...');
    const allPlayers = await PlayerService.getAllPlayers();
    console.log(`✅ Got ${allPlayers.length} players`);
    
    // Test 2: Check data structure
    const samplePlayer = allPlayers[0];
    console.log('Sample player:', samplePlayer);
    
    // Test 3: Test search logic
    const query = 'nathan';
    const searchResults = allPlayers.filter(player => 
      player.name.toLowerCase().includes(query.toLowerCase())
    );
    console.log(`✅ Search for "${query}" found ${searchResults.length} results`);
    
    // Test 4: Check API endpoint format
    const apiResponse = {
      success: true,
      data: {
        players: allPlayers.slice(0, 10) // First 10 players
      },
      total: allPlayers.length,
      timestamp: new Date().toISOString()
    };
    
    const result = {
      debug_info: {
        total_players: allPlayers.length,
        sample_player: samplePlayer,
        search_test: {
          query: query,
          results_count: searchResults.length,
          sample_results: searchResults.slice(0, 3)
        },
        api_format_test: {
          has_success: 'success' in apiResponse,
          has_data: 'data' in apiResponse,
          has_players: apiResponse.data && 'players' in apiResponse.data,
          players_count: apiResponse.data?.players?.length || 0
        }
      },
      api_response_sample: apiResponse,
      issues_found: [],
      recommendations: []
    };
    
    // Check for potential issues
    if (allPlayers.length === 0) {
      result.issues_found.push('No players loaded from database');
      result.recommendations.push('Check Supabase connection and data');
    }
    
    if (!samplePlayer.name) {
      result.issues_found.push('Player name field missing');
      result.recommendations.push('Check data transformation in PlayerService');
    }
    
    if (!samplePlayer.team) {
      result.issues_found.push('Player team field missing');
      result.recommendations.push('Check team field mapping');
    }
    
    if (!samplePlayer.position) {
      result.issues_found.push('Player position field missing');
      result.recommendations.push('Check position field mapping');
    }
    
    console.log(`🎯 Debug complete. Found ${result.issues_found.length} issues.`);
    
    return res.status(200).json(result);
    
  } catch (error) {
    console.error('❌ Debug search failed:', error);
    return res.status(500).json({
      success: false,
      error: 'Debug search failed',
      details: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
  }
}
