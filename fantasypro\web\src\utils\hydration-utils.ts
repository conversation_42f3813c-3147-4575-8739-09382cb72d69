/**
 * Hydration-safe utilities for FantasyPro (Non-JSX version)
 * Safe to import in API endpoints and other .ts files
 */

/**
 * Generate a stable timestamp for API responses that won't cause hydration issues
 */
export function getStableTimestamp(): string {
  // Use a fixed timestamp for SSR, real timestamp for client
  if (typeof window === 'undefined') {
    // Server-side: return a stable timestamp
    return '2024-01-01T00:00:00.000Z';
  }
  // Client-side: return actual timestamp
  return new Date().toISOString();
}

/**
 * Safe timestamp formatter that works consistently across server and client
 */
export function formatTimestamp(timestamp: string | null): string {
  if (!timestamp) return 'Loading...';
  
  try {
    const date = new Date(timestamp);
    return date.toLocaleString();
  } catch (error) {
    return 'Invalid date';
  }
}

/**
 * Check if we're running on the server side
 */
export function isServerSide(): boolean {
  return typeof window === 'undefined';
}

/**
 * Check if we're running on the client side
 */
export function isClientSide(): boolean {
  return typeof window !== 'undefined';
}

/**
 * Get a safe timestamp that won't cause hydration mismatches
 * Returns null on server, actual timestamp on client
 */
export function getSafeTimestamp(): string | null {
  if (isServerSide()) {
    return null;
  }
  return new Date().toISOString();
}

/**
 * Create a stable response object for API endpoints
 */
export function createStableResponse<T>(data: T, success: boolean = true): {
  success: boolean;
  data: T;
  timestamp: string;
} {
  return {
    success,
    data,
    timestamp: getStableTimestamp()
  };
}

/**
 * Create an error response object for API endpoints
 */
export function createErrorResponse(error: string, details?: any): {
  success: boolean;
  error: string;
  details?: any;
  timestamp: string;
} {
  return {
    success: false,
    error,
    details,
    timestamp: getStableTimestamp()
  };
}
