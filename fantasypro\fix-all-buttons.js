/**
 * Comprehensive Button Type Fix Script
 * Fixes all button type attributes across the FantasyPro codebase
 */

const fs = require('fs');
const path = require('path');

// Files to fix
const filesToFix = [
  'web/src/pages/settings.tsx',
  'web/src/pages/my-team.tsx',
  'web/src/pages/players.tsx',
  'web/src/pages/dashboard.tsx',
  'web/src/pages/analytics.tsx',
  'web/src/pages/trades.tsx',
  'web/src/pages/injuries.tsx',
  'web/src/components/TradeAnalysis.tsx',
  'web/src/components/ErrorBoundary.tsx'
];

function fixButtonTypes(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️ File not found: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Pattern 1: <button onClick={...} without type
    const pattern1 = /<button(\s+)onClick=/g;
    if (pattern1.test(content)) {
      content = content.replace(/<button(\s+)onClick=/g, '<button$1type="button" onClick=');
      modified = true;
      console.log(`✅ Fixed pattern 1 in ${filePath}`);
    }

    // Pattern 2: <button className={...} without type
    const pattern2 = /<button(\s+)className=/g;
    content = content.replace(pattern2, (match, whitespace) => {
      if (!match.includes('type=')) {
        modified = true;
        return `<button${whitespace}type="button" className=`;
      }
      return match;
    });

    // Pattern 3: <button disabled={...} without type
    const pattern3 = /<button(\s+)disabled=/g;
    content = content.replace(pattern3, (match, whitespace) => {
      if (!match.includes('type=')) {
        modified = true;
        return `<button${whitespace}type="button" disabled=`;
      }
      return match;
    });

    // Pattern 4: Multi-line buttons
    const multiLinePattern = /<button\s*\n\s*onClick=/g;
    if (multiLinePattern.test(content)) {
      content = content.replace(/<button(\s*\n\s*)onClick=/g, '<button$1type="button"\n          onClick=');
      modified = true;
      console.log(`✅ Fixed multi-line pattern in ${filePath}`);
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed button types in ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️ No button type issues found in ${filePath}`);
      return false;
    }

  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function fixAllButtons() {
  console.log('🔧 FIXING ALL BUTTON TYPE ISSUES');
  console.log('=================================');
  
  let totalFixed = 0;
  
  filesToFix.forEach(filePath => {
    if (fixButtonTypes(filePath)) {
      totalFixed++;
    }
  });
  
  console.log(`\n🎉 BUTTON FIX COMPLETE`);
  console.log(`   - Files processed: ${filesToFix.length}`);
  console.log(`   - Files modified: ${totalFixed}`);
  console.log(`   - All button type issues should now be resolved`);
  
  return totalFixed;
}

// Run the fix
if (require.main === module) {
  fixAllButtons();
}

module.exports = { fixAllButtons, fixButtonTypes };
