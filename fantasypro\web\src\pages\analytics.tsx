import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import { motion } from 'framer-motion';
import Layout from '../components/Layout';
import {
  ChartBarIcon,
  TrophyIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CurrencyDollarIcon,
  FireIcon,
  UserGroupIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  StarIcon,
  ShieldCheckIcon,
  BoltIcon
} from '@heroicons/react/24/outline';

interface AnalyticsData {
  performance: {
    total_points: number;
    average_per_round: number;
    rank: number;
    percentile: number;
  };
  trends: {
    last_5_rounds: number[];
    form_trend: 'rising' | 'falling' | 'stable';
    consistency_score: number;
  };
  predictions: {
    next_round_prediction: number;
    confidence: number;
    key_players: string[];
  };
  insights: {
    best_performers: Array<{
      name: string;
      position: string;
      points: number;
      roi: number;
    }>;
    worst_performers: Array<{
      name: string;
      position: string;
      points: number;
      roi: number;
    }>;
    trade_opportunities: Array<{
      player_out: string;
      player_in: string;
      expected_gain: number;
      confidence: number;
    }>;
  };
}

const Analytics: NextPage = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState('season');

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        // Simulate API call - replace with real data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockData: AnalyticsData = {
          performance: {
            total_points: 15847,
            average_per_round: 1079.1,
            rank: 12847,
            percentile: 85.2
          },
          trends: {
            last_5_rounds: [1156, 1089, 1034, 967, 1079],
            form_trend: 'rising',
            consistency_score: 7.8
          },
          predictions: {
            next_round_prediction: 1125,
            confidence: 78,
            key_players: ['Herbie Farnworth', 'James Tedesco', 'Keaon Koloamatangi']
          },
          insights: {
            best_performers: [
              { name: 'Herbie Farnworth', position: 'CTW', points: 1161, roi: 142.3 },
              { name: 'James Tedesco', position: 'FLB', points: 1144, roi: 139.9 },
              { name: 'Keaon Koloamatangi', position: '2RF', points: 987, roi: 124.1 }
            ],
            worst_performers: [
              { name: 'Ryan Papenhuyzen', position: 'FLB', points: 456, roi: 59.9 },
              { name: 'Tino Fa\'asuamaleaui', position: '2RF', points: 623, roi: 89.0 }
            ],
            trade_opportunities: [
              {
                player_out: 'Ryan Papenhuyzen',
                player_in: 'Scott Drinkwater',
                expected_gain: 45.2,
                confidence: 82
              },
              {
                player_out: 'Tino Fa\'asuamaleaui',
                player_in: 'Terrell May',
                expected_gain: 38.7,
                confidence: 76
              }
            ]
          }
        };

        setAnalyticsData(mockData);
      } catch (error) {
        console.error('Error fetching analytics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [selectedTimeframe]);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'rising':
        return <ArrowTrendingUpIcon className="w-5 h-5 text-green-400" />;
      case 'falling':
        return <ArrowTrendingDownIcon className="w-5 h-5 text-red-400" />;
      default:
        return <ClockIcon className="w-5 h-5 text-yellow-400" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'rising':
        return 'text-green-400';
      case 'falling':
        return 'text-red-400';
      default:
        return 'text-yellow-400';
    }
  };

  const getPerformanceColor = (percentile: number) => {
    if (percentile >= 90) return 'text-green-400';
    if (percentile >= 70) return 'text-blue-400';
    if (percentile >= 50) return 'text-yellow-400';
    return 'text-red-400';
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </Layout>
    );
  }

  if (!analyticsData) {
    return (
      <Layout>
        <div className="text-center py-12">
          <div className="text-red-400 text-xl">Error loading analytics data</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Analytics - FantasyPro</title>
        <meta name="description" content="Advanced analytics and insights for your NRL SuperCoach team" />
      </Head>

      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold themed-text-primary">Analytics</h1>
            <p className="themed-text-tertiary mt-1">Advanced insights and performance analysis for your team</p>
          </div>
          <div className="flex items-center space-x-4">
            <select
              className="input-primary"
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value)}
            >
              <option value="season">Full Season</option>
              <option value="last10">Last 10 Rounds</option>
              <option value="last5">Last 5 Rounds</option>
              <option value="recent">Recent Form</option>
            </select>
          </div>
        </div>

        {/* Performance Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            className="card-premium p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <TrophyIcon className="w-5 h-5 text-yellow-400" />
                <span className="text-sm themed-text-secondary">Total Points</span>
              </div>
              <span className="text-xs text-yellow-400">Season</span>
            </div>
            <div className="text-2xl font-bold themed-text-primary mb-1">
              {analyticsData.performance.total_points.toLocaleString()}
            </div>
            <div className="text-sm themed-text-tertiary">
              Avg: {analyticsData.performance.average_per_round.toFixed(1)} per round
            </div>
          </motion.div>

          <motion.div
            className="card-premium p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <ChartBarIcon className="w-5 h-5 text-blue-400" />
                <span className="text-sm themed-text-secondary">Rank</span>
              </div>
              <span className="text-xs text-blue-400">Overall</span>
            </div>
            <div className="text-2xl font-bold themed-text-primary mb-1">
              #{analyticsData.performance.rank.toLocaleString()}
            </div>
            <div className={`text-sm ${getPerformanceColor(analyticsData.performance.percentile)}`}>
              Top {(100 - analyticsData.performance.percentile).toFixed(1)}%
            </div>
          </motion.div>

          <motion.div
            className="card-premium p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                {getTrendIcon(analyticsData.trends.form_trend)}
                <span className="text-sm themed-text-secondary">Form Trend</span>
              </div>
              <span className="text-xs text-green-400">Last 5</span>
            </div>
            <div className={`text-2xl font-bold mb-1 ${getTrendColor(analyticsData.trends.form_trend)}`}>
              {analyticsData.trends.form_trend.charAt(0).toUpperCase() + analyticsData.trends.form_trend.slice(1)}
            </div>
            <div className="text-sm themed-text-tertiary">
              Consistency: {analyticsData.trends.consistency_score.toFixed(1)}/10
            </div>
          </motion.div>

          <motion.div
            className="card-premium p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <BoltIcon className="w-5 h-5 text-purple-400" />
                <span className="text-sm themed-text-secondary">Next Round</span>
              </div>
              <span className="text-xs text-purple-400">Prediction</span>
            </div>
            <div className="text-2xl font-bold themed-text-primary mb-1">
              {analyticsData.predictions.next_round_prediction}
            </div>
            <div className="text-sm themed-text-tertiary">
              {analyticsData.predictions.confidence}% confidence
            </div>
          </motion.div>
        </div>

        {/* Performance Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Best Performers */}
          <motion.div
            className="card-premium"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="p-6 border-b themed-border">
              <div className="flex items-center space-x-3">
                <StarIcon className="w-6 h-6 text-green-400" />
                <h2 className="text-xl font-semibold themed-text-primary">Best Performers</h2>
                <span className="status-live">ROI Leaders</span>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {analyticsData.insights.best_performers.map((player, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-green-500/10 rounded-lg border border-green-500/20">
                    <div>
                      <div className="font-semibold themed-text-primary">{player.name}</div>
                      <div className="text-sm themed-text-tertiary">{player.position} • {player.points} points</div>
                    </div>
                    <div className="text-right">
                      <div className="text-green-400 font-bold">{player.roi.toFixed(1)}%</div>
                      <div className="text-xs themed-text-tertiary">ROI</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Worst Performers */}
          <motion.div
            className="card-premium"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <div className="p-6 border-b themed-border">
              <div className="flex items-center space-x-3">
                <ExclamationTriangleIcon className="w-6 h-6 text-red-400" />
                <h2 className="text-xl font-semibold themed-text-primary">Underperformers</h2>
                <span className="status-warning">Needs Attention</span>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {analyticsData.insights.worst_performers.map((player, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-red-500/10 rounded-lg border border-red-500/20">
                    <div>
                      <div className="font-semibold themed-text-primary">{player.name}</div>
                      <div className="text-sm themed-text-tertiary">{player.position} • {player.points} points</div>
                    </div>
                    <div className="text-right">
                      <div className="text-red-400 font-bold">{player.roi.toFixed(1)}%</div>
                      <div className="text-xs themed-text-tertiary">ROI</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Trade Opportunities */}
        <motion.div
          className="card-premium"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <div className="p-6 border-b themed-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <ArrowTrendingUpIcon className="w-6 h-6 text-blue-400" />
                <h2 className="text-xl font-semibold themed-text-primary">AI Trade Recommendations</h2>
                <span className="status-live">Updated</span>
              </div>
              <button type="button" className="btn-primary btn-ripple">
                View All Trades
              </button>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {analyticsData.insights.trade_opportunities.map((trade, index) => (
                <div key={index} className="flex items-center justify-between p-4 card-hover">
                  <div className="flex items-center space-x-4">
                    <div className="text-center">
                      <div className="font-semibold themed-text-primary text-sm">{trade.player_out}</div>
                      <div className="text-xs text-red-400">OUT</div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <ArrowTrendingUpIcon className="w-4 h-4 text-blue-400" />
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold themed-text-primary text-sm">{trade.player_in}</div>
                      <div className="text-xs text-green-400">IN</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-green-400 font-bold">+{trade.expected_gain.toFixed(1)}</div>
                    <div className="text-xs themed-text-tertiary">{trade.confidence}% confidence</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Form Chart */}
        <motion.div
          className="card-premium"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <div className="p-6 border-b themed-border">
            <div className="flex items-center space-x-3">
              <ChartBarIcon className="w-6 h-6 text-purple-400" />
              <h2 className="text-xl font-semibold themed-text-primary">Recent Form</h2>
              <span className="text-xs text-purple-400">Last 5 Rounds</span>
            </div>
          </div>
          <div className="p-6">
            <div className="flex items-end justify-between h-32 space-x-2">
              {analyticsData.trends.last_5_rounds.map((score, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div
                    className="w-full bg-blue-500/20 rounded-t flex items-end justify-center text-xs font-medium themed-text-primary"
                    style={{
                      height: `${(score / Math.max(...analyticsData.trends.last_5_rounds)) * 100}%`,
                      minHeight: '20px'
                    }}
                  >
                    {score}
                  </div>
                  <div className="text-xs themed-text-tertiary mt-2">R{index + 12}</div>
                </div>
              ))}
            </div>
            <div className="mt-6 text-center">
              <div className="text-sm themed-text-tertiary">
                Average: {(analyticsData.trends.last_5_rounds.reduce((a, b) => a + b, 0) / analyticsData.trends.last_5_rounds.length).toFixed(1)} points
              </div>
            </div>
          </div>
        </motion.div>

        {/* Key Players for Next Round */}
        <motion.div
          className="card-premium"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <div className="p-6 border-b themed-border">
            <div className="flex items-center space-x-3">
              <FireIcon className="w-6 h-6 text-orange-400" />
              <h2 className="text-xl font-semibold themed-text-primary">Key Players - Next Round</h2>
              <span className="status-live">AI Predictions</span>
            </div>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {analyticsData.predictions.key_players.map((player, index) => (
                <div key={index} className="text-center p-4 bg-orange-500/10 rounded-lg border border-orange-500/20">
                  <div className="font-semibold themed-text-primary mb-2">{player}</div>
                  <div className="text-xs themed-text-tertiary">Expected high scorer</div>
                </div>
              ))}
            </div>
            <div className="mt-6 text-center">
              <button type="button" className="btn-primary btn-ripple">
                View Detailed Predictions
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </Layout>
  );
};

export default Analytics;
