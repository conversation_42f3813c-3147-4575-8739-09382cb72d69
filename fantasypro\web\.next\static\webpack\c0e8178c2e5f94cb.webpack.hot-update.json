{"c": ["pages/players", "webpack"], "r": ["pages/dropdown-test"], "m": ["./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js", "./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cjonah%5CDesktop%5CORIGVMI%5CGithub%5CPlayground%5COCTOAGENT%5Cfantasypro%5Cweb%5Csrc%5Cpages%5Cdropdown-test.tsx&page=%2Fdropdown-test!", "./src/components/SimplePlayerSearch.tsx", "./src/pages/dropdown-test.tsx", "__barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,SparklesIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=MagnifyingGlassIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js"]}