"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_services_supabase_ts";
exports.ids = ["src_services_supabase_ts"];
exports.modules = {

/***/ "./src/services/supabase.ts":
/*!**********************************!*\
  !*** ./src/services/supabase.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: () => (/* binding */ AnalyticsService),\n/* harmony export */   CacheService: () => (/* binding */ CacheService),\n/* harmony export */   InjuryService: () => (/* binding */ InjuryService),\n/* harmony export */   PlayerService: () => (/* binding */ PlayerService),\n/* harmony export */   SquadService: () => (/* binding */ SquadService),\n/* harmony export */   TradeService: () => (/* binding */ TradeService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Supabase configuration\nconst supabaseUrl = \"https://fuxpdgsixnbbsdspusmp.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ3NzE5NzQsImV4cCI6MjA1MDM0Nzk3NH0.Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5\" || 0;\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDUwODA1NiwiZXhwIjoyMDY2MDg0MDU2fQ.f5qZOHQlAKz_sbIwoGmY4Gub8bGHyx_jxlRYZk-1VPg\";\n// Create Supabase clients\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n// Player service functions\nclass PlayerService {\n    // Get all players with search and filtering\n    static async getPlayers(options = {}) {\n        let query = supabase.from(\"players\").select(\"*\");\n        if (options.search) {\n            query = query.or(`name.ilike.%${options.search}%,team.ilike.%${options.search}%,position.ilike.%${options.search}%`);\n        }\n        if (options.position) {\n            query = query.eq(\"position\", options.position);\n        }\n        if (options.team) {\n            query = query.eq(\"team\", options.team);\n        }\n        if (options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n        }\n        const { data, error } = await query.order(\"points\", {\n            ascending: false\n        });\n        if (error) {\n            console.error(\"Error fetching players:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Get ALL players (for Players page - should return 581 players)\n    static async getAllPlayers() {\n        console.log(\"\\uD83D\\uDD04 PlayerService.getAllPlayers() - fetching complete player dataset\");\n        try {\n            // Try nrl_players table first\n            const { data: playersData, error: playersError } = await supabase.from(\"nrl_players\").select(\"*\").order(\"name\");\n            if (playersError) {\n                console.warn(\"⚠️ nrl_players table error:\", playersError);\n                // Fallback to generating mock data\n                return this.generateMockPlayers(581);\n            }\n            if (!playersData || playersData.length === 0) {\n                console.warn(\"⚠️ No data in nrl_players table, generating mock data\");\n                return this.generateMockPlayers(581);\n            }\n            console.log(`✅ Supabase nrl_players table returned ${playersData.length} players`);\n            // Transform nrl_players data to match our Player interface\n            const transformedPlayers = playersData.map((player, index)=>{\n                const baseStats = player.statistics || {};\n                // Generate realistic stats if missing\n                const price = baseStats.price || 300000 + Math.random() * 700000;\n                const totalPoints = baseStats.total_points || Math.floor(Math.random() * 1500);\n                const gamesPlayed = baseStats.games_played || Math.floor(Math.random() * 20) + 5;\n                const averagePoints = totalPoints / gamesPlayed;\n                return {\n                    id: player.id?.toString() || (index + 1).toString(),\n                    name: player.name || `Player ${index + 1}`,\n                    position: player.position || [\n                        \"FLB\",\n                        \"HFB\",\n                        \"CTW\",\n                        \"FRF\",\n                        \"HOK\"\n                    ][index % 5],\n                    team: player.team_name || [\n                        \"Broncos\",\n                        \"Storm\",\n                        \"Panthers\",\n                        \"Roosters\"\n                    ][index % 4],\n                    price: Math.round(price),\n                    points: totalPoints,\n                    average: Math.round(averagePoints * 10) / 10,\n                    form: Math.round(averagePoints / 10 * 10) / 10,\n                    ownership: baseStats.ownership || Math.random() * 100,\n                    breakeven: baseStats.breakeven || Math.floor(Math.random() * 100),\n                    image_url: player.image_url,\n                    created_at: player.created_at || new Date().toISOString(),\n                    updated_at: player.updated_at || new Date().toISOString()\n                };\n            });\n            console.log(`✅ Transformed ${transformedPlayers.length} players successfully`);\n            return transformedPlayers;\n        } catch (error) {\n            console.error(\"❌ Error in getAllPlayers, falling back to mock data:\", error);\n            return this.generateMockPlayers(581);\n        }\n    }\n    // Generate mock players as fallback\n    static generateMockPlayers(count) {\n        console.log(`🔄 Generating ${count} mock players as fallback`);\n        const teams = [\n            \"Brisbane Broncos\",\n            \"Melbourne Storm\",\n            \"Penrith Panthers\",\n            \"Sydney Roosters\",\n            \"South Sydney Rabbitohs\",\n            \"Parramatta Eels\",\n            \"Canterbury Bulldogs\",\n            \"Manly Sea Eagles\",\n            \"Newcastle Knights\",\n            \"Cronulla Sharks\",\n            \"St George Dragons\",\n            \"Wests Tigers\",\n            \"Gold Coast Titans\",\n            \"North Queensland Cowboys\",\n            \"Canberra Raiders\",\n            \"New Zealand Warriors\"\n        ];\n        const positions = [\n            \"FLB\",\n            \"HFB\",\n            \"CTW\",\n            \"FRF\",\n            \"HOK\",\n            \"EDG\",\n            \"LCK\"\n        ];\n        const firstNames = [\n            \"Nathan\",\n            \"Reece\",\n            \"James\",\n            \"Cameron\",\n            \"Daly\",\n            \"Tom\",\n            \"Ryan\",\n            \"Josh\",\n            \"Luke\",\n            \"Sam\"\n        ];\n        const lastNames = [\n            \"Cleary\",\n            \"Walsh\",\n            \"Tedesco\",\n            \"Munster\",\n            \"Cherry-Evans\",\n            \"Turbo\",\n            \"Papenhuyzen\",\n            \"Haas\",\n            \"Cook\",\n            \"Walker\"\n        ];\n        return Array.from({\n            length: count\n        }, (_, i)=>{\n            const totalPoints = Math.floor(Math.random() * 1500) + 200;\n            const gamesPlayed = Math.floor(Math.random() * 20) + 5;\n            const averagePoints = totalPoints / gamesPlayed;\n            return {\n                id: (i + 1).toString(),\n                name: `${firstNames[i % firstNames.length]} ${lastNames[i % lastNames.length]}${i > 50 ? ` ${Math.floor(i / 50)}` : \"\"}`,\n                position: positions[i % positions.length],\n                team: teams[i % teams.length],\n                price: Math.round((300000 + Math.random() * 700000) / 1000) * 1000,\n                points: totalPoints,\n                average: Math.round(averagePoints * 10) / 10,\n                form: Math.round(averagePoints / 10 * 10) / 10,\n                ownership: Math.round(Math.random() * 100 * 10) / 10,\n                breakeven: Math.floor(Math.random() * 100),\n                image_url: \"\",\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            };\n        });\n    }\n    // Get player by ID\n    static async getPlayer(id) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").eq(\"id\", id).single();\n        if (error) {\n            console.error(\"Error fetching player:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Search players with predictive algorithm (searches all 581 players)\n    static async searchPlayers(query, limit = 10) {\n        if (!query || query.length < 2) return [];\n        console.log(`🔍 Searching nrl_players table for: \"${query}\"`);\n        const { data, error } = await supabase.from(\"nrl_players\").select(\"*\").or(`name.ilike.%${query}%,team_name.ilike.%${query}%,position.ilike.%${query}%`).order(\"name\").limit(limit);\n        if (error) {\n            console.error(\"Error searching nrl_players:\", error);\n            return [];\n        }\n        console.log(`✅ Search found ${data?.length || 0} players in nrl_players table`);\n        // Transform search results to match our Player interface\n        const transformedResults = data?.map((player)=>{\n            const stats = player.statistics || {};\n            const totalPoints = stats.total_points || 0;\n            const gamesPlayed = stats.games_played || 1;\n            const averagePoints = gamesPlayed > 0 ? totalPoints / gamesPlayed : 0;\n            return {\n                id: player.id.toString(),\n                name: player.name,\n                position: player.position || \"Unknown\",\n                team: player.team_name || \"Unknown Team\",\n                price: stats.price || 300000,\n                points: totalPoints,\n                average: averagePoints,\n                form: stats.form || averagePoints / 10,\n                ownership: stats.ownership || 0,\n                breakeven: stats.breakeven || 0,\n                image_url: player.image_url,\n                created_at: player.created_at,\n                updated_at: player.updated_at\n            };\n        }) || [];\n        return transformedResults;\n    }\n    // Get top performers\n    static async getTopPerformers(limit = 10) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"points\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching top performers:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Get price risers\n    static async getPriceRisers(limit = 10) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"price\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching price risers:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Update player data\n    static async updatePlayer(id, updates) {\n        const { data, error } = await supabase.from(\"players\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", id).select().single();\n        if (error) {\n            console.error(\"Error updating player:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Trade recommendation service\nclass TradeService {\n    // Get trade recommendations for user\n    static async getTradeRecommendations(userId, limit = 10) {\n        const { data, error } = await supabase.from(\"trade_recommendations\").select(`\n        *,\n        player_out:players!player_out_id(*),\n        player_in:players!player_in_id(*)\n      `).eq(\"user_id\", userId).order(\"confidence\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching trade recommendations:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Create trade recommendation\n    static async createTradeRecommendation(recommendation) {\n        const { data, error } = await supabase.from(\"trade_recommendations\").insert({\n            ...recommendation,\n            created_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating trade recommendation:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Execute trade (log the trade)\n    static async executeTrade(tradeId, userId) {\n        const { data, error } = await supabase.from(\"executed_trades\").insert({\n            trade_recommendation_id: tradeId,\n            user_id: userId,\n            executed_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error executing trade:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Injury service\nclass InjuryService {\n    // Get current injury reports\n    static async getInjuryReports(limit = 10) {\n        const { data, error } = await supabase.from(\"injury_reports\").select(`\n        *,\n        player:players(*)\n      `).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching injury reports:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Create injury report\n    static async createInjuryReport(report) {\n        const { data, error } = await supabase.from(\"injury_reports\").insert({\n            ...report,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating injury report:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// User squad service\nclass SquadService {\n    // Get user's squad\n    static async getUserSquad(userId) {\n        const { data, error } = await supabase.from(\"user_squads\").select(`\n        *,\n        player:players(*)\n      `).eq(\"user_id\", userId).order(\"position_in_squad\");\n        if (error) {\n            console.error(\"Error fetching user squad:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Add player to squad\n    static async addPlayerToSquad(squadEntry) {\n        const { data, error } = await supabase.from(\"user_squads\").insert({\n            ...squadEntry,\n            created_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error(\"Error adding player to squad:\", error);\n            throw error;\n        }\n        return data;\n    }\n    // Set captain\n    static async setCaptain(userId, playerId) {\n        // First, remove captain status from all players\n        await supabase.from(\"user_squads\").update({\n            is_captain: false,\n            is_vice_captain: false\n        }).eq(\"user_id\", userId);\n        // Then set the new captain\n        const { data, error } = await supabase.from(\"user_squads\").update({\n            is_captain: true\n        }).eq(\"user_id\", userId).eq(\"player_id\", playerId).select().single();\n        if (error) {\n            console.error(\"Error setting captain:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Analytics service\nclass AnalyticsService {\n    // Get dashboard stats\n    static async getDashboardStats() {\n        const [playersCount, teamsCount, injuriesCount] = await Promise.all([\n            supabase.from(\"players\").select(\"id\", {\n                count: \"exact\",\n                head: true\n            }),\n            supabase.from(\"players\").select(\"team\", {\n                count: \"exact\",\n                head: true\n            }).distinct(),\n            supabase.from(\"injury_reports\").select(\"id\", {\n                count: \"exact\",\n                head: true\n            })\n        ]);\n        return {\n            total_players: playersCount.count || 0,\n            total_teams: 17,\n            active_injuries: injuriesCount.count || 0,\n            last_updated: new Date().toISOString()\n        };\n    }\n    // Get trending players\n    static async getTrendingPlayers(limit = 10) {\n        const { data, error } = await supabase.from(\"players\").select(\"*\").order(\"form\", {\n            ascending: false\n        }).limit(limit);\n        if (error) {\n            console.error(\"Error fetching trending players:\", error);\n            throw error;\n        }\n        return data;\n    }\n}\n// Cache service for offline support\nclass CacheService {\n    static{\n        this.CACHE_PREFIX = \"fantasypro_cache_\";\n    }\n    static{\n        this.CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n        ;\n    }\n    static set(key, data) {\n        const cacheData = {\n            data,\n            timestamp: Date.now()\n        };\n        localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheData));\n    }\n    static get(key) {\n        const cached = localStorage.getItem(this.CACHE_PREFIX + key);\n        if (!cached) return null;\n        const { data, timestamp } = JSON.parse(cached);\n        if (Date.now() - timestamp > this.CACHE_DURATION) {\n            localStorage.removeItem(this.CACHE_PREFIX + key);\n            return null;\n        }\n        return data;\n    }\n    static clear() {\n        Object.keys(localStorage).forEach((key)=>{\n            if (key.startsWith(this.CACHE_PREFIX)) {\n                localStorage.removeItem(key);\n            }\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/services/supabase.ts\n");

/***/ })

};
;