import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import { motion } from 'framer-motion';
import Layout from '../components/Layout';
import { useTheme } from '../contexts/ThemeContext';
import {
  CogIcon,
  BellIcon,
  UserIcon,
  ShieldCheckIcon,
  PaintBrushIcon,
  GlobeAltIcon,
  DevicePhoneMobileIcon,
  ChartBarIcon,
  KeyIcon,
  TrashIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  SunIcon,
  MoonIcon
} from '@heroicons/react/24/outline';

interface UserSettings {
  profile: {
    name: string;
    email: string;
    team_name: string;
    favorite_team: string;
  };
  notifications: {
    injury_alerts: boolean;
    trade_recommendations: boolean;
    price_changes: boolean;
    team_news: boolean;
    email_notifications: boolean;
    push_notifications: boolean;
  };
  preferences: {
    theme: 'dark' | 'light' | 'auto';
    currency_format: 'short' | 'full';
    time_format: '12h' | '24h';
    language: string;
    auto_refresh: boolean;
    show_advanced_stats: boolean;
  };
  privacy: {
    public_profile: boolean;
    share_team: boolean;
    analytics_tracking: boolean;
    data_collection: boolean;
  };
}

const Settings: NextPage = () => {
  const { theme, toggleTheme } = useTheme();
  const [settings, setSettings] = useState<UserSettings>({
    profile: {
      name: 'SuperCoach Pro',
      email: '<EMAIL>',
      team_name: 'Sousside Rattlers',
      favorite_team: 'Brisbane Broncos'
    },
    notifications: {
      injury_alerts: true,
      trade_recommendations: true,
      price_changes: false,
      team_news: true,
      email_notifications: true,
      push_notifications: false
    },
    preferences: {
      theme: theme as 'dark' | 'light' | 'auto',
      currency_format: 'short',
      time_format: '12h',
      language: 'en',
      auto_refresh: true,
      show_advanced_stats: true
    },
    privacy: {
      public_profile: false,
      share_team: true,
      analytics_tracking: true,
      data_collection: true
    }
  });

  const [activeTab, setActiveTab] = useState('profile');
  const [hasChanges, setHasChanges] = useState(false);

  const teams = [
    'Brisbane Broncos', 'Sydney Roosters', 'Melbourne Storm', 'Penrith Panthers',
    'Cronulla Sharks', 'Manly Sea Eagles', 'South Sydney Rabbitohs', 'Parramatta Eels',
    'Newcastle Knights', 'Canberra Raiders', 'Gold Coast Titans', 'St George Illawarra Dragons',
    'Canterbury Bulldogs', 'Wests Tigers', 'North Queensland Cowboys', 'New Zealand Warriors',
    'Dolphins'
  ];

  const updateSettings = (section: keyof UserSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const saveSettings = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setHasChanges(false);
      alert('Settings saved successfully!');
    } catch (error) {
      alert('Error saving settings. Please try again.');
    }
  };

  const resetSettings = () => {
    if (confirm('Are you sure you want to reset all settings to default?')) {
      // Reset to default settings
      setHasChanges(true);
      alert('Settings reset to default values');
    }
  };

  const exportData = () => {
    alert('Data export feature coming soon!');
  };

  const deleteAccount = () => {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      alert('Account deletion feature coming soon!');
    }
  };

  const tabs = [
    { id: 'profile', name: 'Profile', icon: UserIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'preferences', name: 'Preferences', icon: CogIcon },
    { id: 'privacy', name: 'Privacy', icon: ShieldCheckIcon }
  ];

  return (
    <Layout>
      <Head>
        <title>Settings - FantasyPro</title>
        <meta name="description" content="Customize your FantasyPro experience" />
      </Head>

      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold themed-text-primary">Settings</h1>
            <p className="themed-text-tertiary mt-1">Customize your FantasyPro experience</p>
          </div>
          <div className="flex items-center space-x-4">
            {hasChanges && (
              <button
                type="button" onClick={saveSettings}
                className="btn-primary btn-ripple"
              >
                <CheckCircleIcon className="w-4 h-4 mr-2" />
                Save Changes
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Settings Navigation */}
          <div className="lg:col-span-1">
            <div className="card p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-600/20 text-blue-400 border border-blue-600/30'
                        : 'themed-text-secondary hover:themed-text-primary hover:bg-slate-700/50'
                    }`}
                  >
                    <tab.icon className="w-5 h-5" />
                    <span>{tab.name}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Settings Content */}
          <div className="lg:col-span-3">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="card p-6"
            >
              {/* Profile Settings */}
              {activeTab === 'profile' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-3 mb-6">
                    <UserIcon className="w-6 h-6 text-blue-400" />
                    <h2 className="text-xl font-semibold themed-text-primary">Profile Settings</h2>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium themed-text-secondary mb-2">
                        Display Name
                      </label>
                      <input
                        type="text"
                        className="input-primary w-full"
                        value={settings.profile.name}
                        onChange={(e) => updateSettings('profile', 'name', e.target.value)}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium themed-text-secondary mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        className="input-primary w-full"
                        value={settings.profile.email}
                        onChange={(e) => updateSettings('profile', 'email', e.target.value)}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium themed-text-secondary mb-2">
                        Team Name
                      </label>
                      <input
                        type="text"
                        className="input-primary w-full"
                        value={settings.profile.team_name}
                        onChange={(e) => updateSettings('profile', 'team_name', e.target.value)}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium themed-text-secondary mb-2">
                        Favorite NRL Team
                      </label>
                      <select
                        className="input-primary w-full"
                        value={settings.profile.favorite_team}
                        onChange={(e) => updateSettings('profile', 'favorite_team', e.target.value)}
                      >
                        {teams.map(team => (
                          <option key={team} value={team}>{team}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Notification Settings */}
              {activeTab === 'notifications' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-3 mb-6">
                    <BellIcon className="w-6 h-6 text-green-400" />
                    <h2 className="text-xl font-semibold themed-text-primary">Notification Settings</h2>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 card-hover">
                      <div>
                        <div className="font-medium themed-text-primary">Injury Alerts</div>
                        <div className="text-sm themed-text-tertiary">Get notified when players get injured</div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.notifications.injury_alerts}
                          onChange={(e) => updateSettings('notifications', 'injury_alerts', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 card-hover">
                      <div>
                        <div className="font-medium themed-text-primary">Trade Recommendations</div>
                        <div className="text-sm themed-text-tertiary">AI-powered trade suggestions</div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.notifications.trade_recommendations}
                          onChange={(e) => updateSettings('notifications', 'trade_recommendations', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 card-hover">
                      <div>
                        <div className="font-medium themed-text-primary">Price Changes</div>
                        <div className="text-sm themed-text-tertiary">Player price rise/fall notifications</div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.notifications.price_changes}
                          onChange={(e) => updateSettings('notifications', 'price_changes', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 card-hover">
                      <div>
                        <div className="font-medium themed-text-primary">Team News</div>
                        <div className="text-sm themed-text-tertiary">Latest news about your players</div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.notifications.team_news}
                          onChange={(e) => updateSettings('notifications', 'team_news', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>
                </div>
              )}

              {/* Preferences Settings */}
              {activeTab === 'preferences' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-3 mb-6">
                    <CogIcon className="w-6 h-6 text-purple-400" />
                    <h2 className="text-xl font-semibold themed-text-primary">Preferences</h2>
                  </div>

                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium themed-text-secondary mb-2">
                          Theme
                        </label>
                        <div className="flex space-x-2">
                          <button
                            type="button" onClick={() => {
                              updateSettings('preferences', 'theme', 'dark');
                              if (theme !== 'dark') toggleTheme();
                            }}
                            className={`flex-1 flex items-center justify-center space-x-2 p-3 rounded-lg border transition-colors ${
                              settings.preferences.theme === 'dark'
                                ? 'border-blue-600 bg-blue-600/20 text-blue-400'
                                : 'border-slate-600 themed-text-secondary hover:themed-text-primary'
                            }`}
                          >
                            <MoonIcon className="w-4 h-4" />
                            <span>Dark</span>
                          </button>
                          <button
                            type="button" onClick={() => {
                              updateSettings('preferences', 'theme', 'light');
                              if (theme !== 'light') toggleTheme();
                            }}
                            className={`flex-1 flex items-center justify-center space-x-2 p-3 rounded-lg border transition-colors ${
                              settings.preferences.theme === 'light'
                                ? 'border-blue-600 bg-blue-600/20 text-blue-400'
                                : 'border-slate-600 themed-text-secondary hover:themed-text-primary'
                            }`}
                          >
                            <SunIcon className="w-4 h-4" />
                            <span>Light</span>
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium themed-text-secondary mb-2">
                          Currency Format
                        </label>
                        <select
                          className="input-primary w-full"
                          value={settings.preferences.currency_format}
                          onChange={(e) => updateSettings('preferences', 'currency_format', e.target.value)}
                        >
                          <option value="short">Short ($1.2M)</option>
                          <option value="full">Full ($1,200,000)</option>
                        </select>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 card-hover">
                        <div>
                          <div className="font-medium themed-text-primary">Auto Refresh</div>
                          <div className="text-sm themed-text-tertiary">Automatically refresh data every 5 minutes</div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="sr-only peer"
                            checked={settings.preferences.auto_refresh}
                            onChange={(e) => updateSettings('preferences', 'auto_refresh', e.target.checked)}
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between p-4 card-hover">
                        <div>
                          <div className="font-medium themed-text-primary">Advanced Statistics</div>
                          <div className="text-sm themed-text-tertiary">Show detailed analytics and metrics</div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="sr-only peer"
                            checked={settings.preferences.show_advanced_stats}
                            onChange={(e) => updateSettings('preferences', 'show_advanced_stats', e.target.checked)}
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Privacy Settings */}
              {activeTab === 'privacy' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-3 mb-6">
                    <ShieldCheckIcon className="w-6 h-6 text-red-400" />
                    <h2 className="text-xl font-semibold themed-text-primary">Privacy & Security</h2>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 card-hover">
                      <div>
                        <div className="font-medium themed-text-primary">Public Profile</div>
                        <div className="text-sm themed-text-tertiary">Allow others to view your profile</div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.privacy.public_profile}
                          onChange={(e) => updateSettings('privacy', 'public_profile', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 card-hover">
                      <div>
                        <div className="font-medium themed-text-primary">Share Team</div>
                        <div className="text-sm themed-text-tertiary">Allow others to view your team composition</div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.privacy.share_team}
                          onChange={(e) => updateSettings('privacy', 'share_team', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>

                  <div className="pt-6 border-t themed-border">
                    <h3 className="text-lg font-semibold themed-text-primary mb-4">Data Management</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <button
                        type="button" onClick={exportData}
                        className="btn-secondary btn-ripple"
                      >
                        <KeyIcon className="w-4 h-4 mr-2" />
                        Export My Data
                      </button>
                      <button
                        type="button" onClick={deleteAccount}
                        className="btn-danger btn-ripple"
                      >
                        <TrashIcon className="w-4 h-4 mr-2" />
                        Delete Account
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Settings;
