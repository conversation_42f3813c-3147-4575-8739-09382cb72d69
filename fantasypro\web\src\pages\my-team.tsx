import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import { motion, AnimatePresence } from 'framer-motion';
import Layout from '../components/Layout';
import UniversalSearch from '../components/UniversalSearch';
import PredictiveSearch from '../components/PredictiveSearch';
import TradeAnalysis from '../components/TradeAnalysis';
import {
  UserGroupIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ExclamationTriangleIcon,
  TrophyIcon,
  FireIcon,
  ChartBarIcon,
  PlusIcon,
  XMarkIcon,
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  StarIcon,
  ShieldCheckIcon,
  ArrowsRightLeftIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

// Types for SuperCoach squad
interface SquadPlayer {
  id: number;
  name: string;
  team: string;
  position: string;
  current_price: number;
  current_breakeven?: number;
  season_points?: number;
  season_average?: number;
  form_rating?: number;
  ownership_percentage?: number;
  injury_status?: string;
  is_captain?: boolean;
  is_vice_captain?: boolean;
  is_playing?: boolean;
  recent_scores?: number[];
}

interface SquadData {
  total_value: number;
  salary_cap: number;
  remaining_budget: number;
  total_points: number;
  round_score: number;
  trades_remaining: number;
  players: SquadPlayer[];
  captain?: SquadPlayer;
  vice_captain?: SquadPlayer;
  bench_players: SquadPlayer[];
  playing_players: SquadPlayer[];
}

const MyTeam: NextPage = () => {
  const [squadData, setSquadData] = useState<SquadData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPlayer, setSelectedPlayer] = useState<SquadPlayer | null>(null);
  const [showAddPlayerModal, setShowAddPlayerModal] = useState(false);
  const [showEditPlayerModal, setShowEditPlayerModal] = useState(false);
  const [showTradeAnalysis, setShowTradeAnalysis] = useState(false);
  const [showCaptainModal, setShowCaptainModal] = useState(false);
  const [editingPlayer, setEditingPlayer] = useState<SquadPlayer | null>(null);
  const [tradePlayer, setTradePlayer] = useState<SquadPlayer | null>(null);
  const [newPlayer, setNewPlayer] = useState<Partial<SquadPlayer>>({
    name: '',
    team: '',
    position: '',
    current_price: 0,
    season_points: 0,
    season_average: 0,
    form_rating: 0,
    is_playing: true
  });

  // Manual squad management functions
  const addPlayer = () => {
    if (!squadData || !newPlayer.name || !newPlayer.team || !newPlayer.position) return;

    const player: SquadPlayer = {
      id: Date.now(), // Simple ID generation
      name: newPlayer.name,
      team: newPlayer.team,
      position: newPlayer.position,
      current_price: newPlayer.current_price || 0,
      season_points: newPlayer.season_points || 0,
      season_average: newPlayer.season_average || 0,
      form_rating: newPlayer.form_rating || 0,
      is_playing: newPlayer.is_playing ?? true,
      recent_scores: []
    };

    const updatedSquadData = {
      ...squadData,
      players: [...squadData.players, player],
      total_value: squadData.total_value + player.current_price,
      remaining_budget: squadData.remaining_budget - player.current_price
    };

    setSquadData(updatedSquadData);
    setNewPlayer({
      name: '',
      team: '',
      position: '',
      current_price: 0,
      season_points: 0,
      season_average: 0,
      form_rating: 0,
      is_playing: true
    });
    setShowAddPlayerModal(false);
  };

  const editPlayer = (player: SquadPlayer) => {
    setEditingPlayer(player);
    setNewPlayer(player);
    setShowEditPlayerModal(true);
  };

  const updatePlayer = () => {
    if (!squadData || !editingPlayer || !newPlayer.name) return;

    const updatedPlayers = squadData.players.map(p =>
      p.id === editingPlayer.id ? { ...p, ...newPlayer } : p
    );

    const updatedSquadData = {
      ...squadData,
      players: updatedPlayers
    };

    // Recalculate totals
    const totalValue = updatedPlayers.reduce((sum, p) => sum + p.current_price, 0);
    updatedSquadData.total_value = totalValue;
    updatedSquadData.remaining_budget = squadData.salary_cap - totalValue;

    setSquadData(updatedSquadData);
    setShowEditPlayerModal(false);
    setEditingPlayer(null);
    setNewPlayer({
      name: '',
      team: '',
      position: '',
      current_price: 0,
      season_points: 0,
      season_average: 0,
      form_rating: 0,
      is_playing: true
    });
  };

  const removePlayer = (playerId: number) => {
    if (!squadData) return;

    const playerToRemove = squadData.players.find(p => p.id === playerId);
    if (!playerToRemove) return;

    const updatedPlayers = squadData.players.filter(p => p.id !== playerId);
    const updatedSquadData = {
      ...squadData,
      players: updatedPlayers,
      total_value: squadData.total_value - playerToRemove.current_price,
      remaining_budget: squadData.remaining_budget + playerToRemove.current_price
    };

    setSquadData(updatedSquadData);
  };

  const setCaptain = (playerId: number) => {
    if (!squadData) return;

    const updatedPlayers = squadData.players.map(p => ({
      ...p,
      is_captain: p.id === playerId,
      is_vice_captain: p.is_captain && p.id !== playerId ? false : p.is_vice_captain
    }));

    setSquadData({
      ...squadData,
      players: updatedPlayers,
      captain: updatedPlayers.find(p => p.is_captain)
    });
  };

  const setViceCaptain = (playerId: number) => {
    if (!squadData) return;

    const updatedPlayers = squadData.players.map(p => ({
      ...p,
      is_vice_captain: p.id === playerId,
      is_captain: p.is_vice_captain && p.id !== playerId ? false : p.is_captain
    }));

    setSquadData({
      ...squadData,
      players: updatedPlayers,
      vice_captain: updatedPlayers.find(p => p.is_vice_captain)
    });
  };

  // Enhanced handler functions
  const handlePlayerSelect = (player: any) => {
    setSelectedPlayer(player);
    console.log('Player selected:', player);
  };

  const handleTradePlayer = (player: SquadPlayer) => {
    setTradePlayer(player);
    setShowTradeAnalysis(true);
  };

  const handleExecuteTrade = (tradeData: any) => {
    console.log('Executing trade:', tradeData);
    // Add actual trade execution logic here
    alert('Trade executed successfully!');
    setShowTradeAnalysis(false);
    setTradePlayer(null);
  };

  const handleImportFromSuperCoach = () => {
    alert('Import from SuperCoach functionality coming soon!');
  };

  const togglePlayerStatus = (playerId: number) => {
    if (!squadData) return;

    const updatedPlayers = squadData.players.map(p =>
      p.id === playerId ? { ...p, is_playing: !p.is_playing } : p
    );

    setSquadData({
      ...squadData,
      players: updatedPlayers,
      playing_players: updatedPlayers.filter(p => p.is_playing),
      bench_players: updatedPlayers.filter(p => !p.is_playing)
    });
  };

  // Initialize with sample Sousside Rattlers data
  useEffect(() => {
    const initializeSquadData = async () => {
      try {
        
        // Initialize with sample Sousside Rattlers data (users can modify manually)
        const initialSquadData: SquadData = {
          total_value: 11766600, // $11.8M - $33.4k remaining
          salary_cap: 11800000,  // $11.8M
          remaining_budget: 33400, // $33.4k
          total_points: 1079, // Round 15 score
          round_score: 1079,
          trades_remaining: 9, // Round 16 trades
          players: [
            // Starting/Bench players from screenshots
            {
              id: 1,
              name: "Herbie Farnworth",
              team: "Dolphins",
              position: "CTW",
              current_price: 815400,
              season_points: 1161,
              season_average: 82.9,
              form_rating: 8.7,
              is_captain: true,
              is_playing: true,
              recent_scores: [150, 108, 95, 87, 82]
            },
            {
              id: 2,
              name: "James Tedesco",
              team: "Sydney Roosters",
              position: "FLB",
              current_price: 817700,
              season_points: 1144,
              season_average: 88.0,
              form_rating: 8.5,
              is_vice_captain: true,
              is_playing: true,
              recent_scores: [97, 85, 78, 92, 88]
            },
            {
              id: 3,
              name: "Blayke Brailey",
              team: "Cronulla Sharks",
              position: "HOK",
              current_price: 627300,
              season_points: 813,
              season_average: 58.1,
              form_rating: 6.6,
              is_playing: true,
              recent_scores: [102, 66, 45, 58, 72]
            },
            {
              id: 4,
              name: "Briton Nikora",
              team: "Cronulla Sharks",
              position: "2RF",
              current_price: 531700,
              season_points: 842,
              season_average: 60.1,
              form_rating: 7.2,
              is_playing: true,
              recent_scores: [108, 72, 55, 68, 61]
            },
            {
              id: 5,
              name: "Reuben Garrick",
              team: "Manly Sea Eagles",
              position: "CTW",
              current_price: 621500,
              season_points: 1023,
              season_average: 73.1,
              form_rating: 8.1,
              is_playing: true,
              recent_scores: [93, 81, 76, 85, 73]
            },
            {
              id: 6,
              name: "Jayden Campbell",
              team: "Gold Coast Titans",
              position: "5/8",
              current_price: 643800,
              season_points: 713,
              season_average: 71.3,
              form_rating: 6.9,
              is_playing: true,
              recent_scores: [87, 69, 58, 75, 71]
            },
            {
              id: 7,
              name: "Keaon Koloamatangi",
              team: "South Sydney Rabbitohs",
              position: "2RF",
              current_price: 795300,
              season_points: 987,
              season_average: 70.5,
              form_rating: 10.0, // 100.3 3-round avg suggests great form
              is_playing: true,
              recent_scores: [73, 100, 95, 88, 92]
            },
            {
              id: 8,
              name: "Scott Drinkwater",
              team: "North Queensland Cowboys",
              position: "FLB",
              current_price: 708100,
              season_points: 1016,
              season_average: 78.2,
              form_rating: 6.9,
              is_playing: true,
              recent_scores: [33, 69, 82, 95, 78]
            },
            // Additional bench players
            {
              id: 9,
              name: "Terrell May",
              team: "Wests Tigers",
              position: "FRF",
              current_price: 832800,
              season_points: 1134,
              season_average: 87.2,
              form_rating: 9.4,
              is_playing: false, // DNP
              recent_scores: [0, 93, 88, 95, 87]
            },
            {
              id: 10,
              name: "Fletcher Sharpe",
              team: "Dolphins",
              position: "CTW",
              current_price: 754200,
              season_points: 994,
              season_average: 76.5,
              form_rating: 9.8,
              is_playing: false, // DNP
              recent_scores: [0, 98, 92, 85, 76]
            },
            {
              id: 11,
              name: "Dylan Lucas",
              team: "Dolphins",
              position: "CTW",
              current_price: 715600,
              season_points: 811,
              season_average: 81.1,
              form_rating: 6.8,
              is_playing: true,
              recent_scores: [82, 68, 75, 81, 85]
            },
            {
              id: 12,
              name: "Nicholas Hynes",
              team: "Cronulla Sharks",
              position: "HFB",
              current_price: 619000,
              season_points: 969,
              season_average: 69.2,
              form_rating: 5.8,
              is_playing: true,
              recent_scores: [57, 58, 72, 69, 65]
            },
            {
              id: 13,
              name: "Beau Fermor",
              team: "Gold Coast Titans",
              position: "2RF",
              current_price: 588700,
              season_points: 855,
              season_average: 65.8,
              form_rating: 6.0,
              is_playing: true,
              recent_scores: [67, 60, 68, 66, 72]
            },
            {
              id: 14,
              name: "Ryan Papenhuyzen",
              team: "Melbourne Storm",
              position: "FLB",
              current_price: 760900,
              season_points: 1045,
              season_average: 87.1,
              form_rating: 5.8,
              is_playing: false, // DNP
              recent_scores: [0, 57, 85, 92, 87]
            },
            {
              id: 15,
              name: "Lyhkan King-Togia",
              team: "St George Illawarra Dragons",
              position: "HFB",
              current_price: 329000,
              season_points: 289,
              season_average: 48.2,
              form_rating: 5.0,
              is_playing: true,
              recent_scores: [60, 50, 45, 48, 52]
            },
            {
              id: 16,
              name: "Tino Fa'asuamaleaui",
              team: "Gold Coast Titans",
              position: "2RF",
              current_price: 699900,
              season_points: 849,
              season_average: 77.2,
              form_rating: 6.3,
              is_playing: false, // DNP
              recent_scores: [0, 63, 77, 82, 75]
            },
            {
              id: 17,
              name: "Sandon Smith",
              team: "Sydney Roosters",
              position: "HOK",
              current_price: 499600,
              season_points: 706,
              season_average: 54.3,
              form_rating: 5.3,
              is_playing: false, // Bench
              recent_scores: [29, 53, 58, 54, 61]
            }
          ],
          captain: undefined,
          vice_captain: undefined,
          bench_players: [],
          playing_players: []
        };

        // Set captain and vice captain
        initialSquadData.captain = initialSquadData.players.find(p => p.is_captain);
        initialSquadData.vice_captain = initialSquadData.players.find(p => p.is_vice_captain);
        initialSquadData.playing_players = initialSquadData.players.filter(p => p.is_playing);
        initialSquadData.bench_players = initialSquadData.players.filter(p => !p.is_playing);

        setSquadData(initialSquadData);
      } catch (error) {
        console.error('Error fetching squad data:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeSquadData();
  }, []);

  const formatCurrency = (amount: number) => {
    return `$${(amount / 1000000).toFixed(2)}M`;
  };

  const getBreakevenColor = (breakeven?: number) => {
    if (!breakeven) return 'text-slate-400';
    return breakeven < 0 ? 'text-green-400' : 'text-red-400';
  };

  const getFormRatingColor = (rating?: number) => {
    if (!rating) return 'text-slate-400';
    if (rating >= 8) return 'text-green-400';
    if (rating >= 6) return 'text-yellow-400';
    return 'text-red-400';
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </Layout>
    );
  }

  if (!squadData) {
    return (
      <Layout>
        <div className="text-center py-12">
          <div className="text-red-400 text-xl">Error loading squad data</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>My Team - FantasyPro</title>
        <meta name="description" content="Manage your NRL SuperCoach team with AI-powered insights" />
      </Head>

      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">My Team</h1>
            <p className="text-slate-400 mt-1">Manage your NRL SuperCoach team with AI-powered insights</p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              type="button" className="btn-primary btn-ripple"
              onClick={() => setShowAddPlayerModal(true)}
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              Add Player
            </button>
            <button
              type="button" className="btn-secondary btn-ripple"
              onClick={handleImportFromSuperCoach}
            >
              <MagnifyingGlassIcon className="w-4 h-4 mr-2" />
              Import from SuperCoach
            </button>
            <button
              type="button" className="btn-accent btn-ripple"
              onClick={() => setShowCaptainModal(true)}
            >
              <StarIcon className="w-4 h-4 mr-2" />
              Set Captain
            </button>
          </div>
        </div>

        {/* Universal Search for Adding Players */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="card-premium p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <MagnifyingGlassIcon className="w-6 h-6 text-green-400" />
            <h2 className="text-xl font-semibold themed-text-primary">Add Players to Team</h2>
            <span className="status-live">581 Players</span>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">
                🔍 Search & Add Players
              </label>
              <PredictiveSearch
                placeholder="Type 'Na' for Nathan Cleary, 'Re' for Reece Walsh..."
                onPlayerSelect={handlePlayerSelect}
                showPlayerDetails={true}
                maxResults={8}
                minQueryLength={1}
                clearOnSelect={false}
              />
            </div>

            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">
                👑 Quick Captain Selection
              </label>
              <PredictiveSearch
                placeholder="Search for captain..."
                showCaptainOption={true}
                onCaptainSelect={(player) => setCaptain(player.id)}
                onPlayerSelect={handlePlayerSelect}
                showPlayerDetails={true}
                maxResults={6}
                minQueryLength={1}
              />
            </div>
          </div>

          {selectedPlayer && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="mt-6 pt-6 border-t themed-border"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold themed-text-primary">Selected: {selectedPlayer.name}</h3>
                  <p className="text-sm themed-text-tertiary">{selectedPlayer.position} - {selectedPlayer.team}</p>
                </div>
                <div className="flex space-x-3">
                  <button
                    type="button" onClick={() => {
                      // Add player to team logic
                      const newPlayer: SquadPlayer = {
                        id: Date.now(),
                        name: selectedPlayer.name,
                        team: selectedPlayer.team,
                        position: selectedPlayer.position,
                        current_price: selectedPlayer.price || 400000,
                        season_points: selectedPlayer.points || 0,
                        season_average: selectedPlayer.average || 0,
                        form_rating: selectedPlayer.form || 5,
                        is_playing: true,
                        recent_scores: []
                      };

                      if (squadData) {
                        setSquadData({
                          ...squadData,
                          players: [...squadData.players, newPlayer],
                          total_value: squadData.total_value + newPlayer.current_price,
                          remaining_budget: squadData.remaining_budget - newPlayer.current_price
                        });
                      }
                      setSelectedPlayer(null);
                    }}
                    className="btn-primary btn-ripple flex items-center space-x-2"
                  >
                    <PlusIcon className="w-4 h-4" />
                    <span>Add to Team</span>
                  </button>
                  <button
                    type="button" onClick={() => setCaptain(selectedPlayer.id)}
                    className="btn-secondary btn-ripple flex items-center space-x-2"
                  >
                    <StarIcon className="w-4 h-4" />
                    <span>Set Captain</span>
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* Squad Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            className="card p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <CurrencyDollarIcon className="w-5 h-5 text-green-400" />
                <span className="text-sm text-slate-400">Squad Value</span>
              </div>
              <span className="text-xs text-green-400">
                {((squadData.remaining_budget / squadData.salary_cap) * 100).toFixed(1)}% left
              </span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">
              {formatCurrency(squadData.total_value)}
            </div>
            <div className="text-sm text-slate-400">
              {formatCurrency(squadData.remaining_budget)} remaining
            </div>
          </motion.div>

          <motion.div
            className="card p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <TrophyIcon className="w-5 h-5 text-blue-400" />
                <span className="text-sm text-slate-400">Total Points</span>
              </div>
              <span className="text-xs text-blue-400">Season</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">
              {squadData.total_points.toLocaleString()}
            </div>
            <div className="text-sm text-slate-400">
              Round: {squadData.round_score} pts
            </div>
          </motion.div>

          <motion.div
            className="card p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <ArrowTrendingUpIcon className="w-5 h-5 text-orange-400" />
                <span className="text-sm text-slate-400">Trades Left</span>
              </div>
              <span className="text-xs text-orange-400">This round</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">
              {squadData.trades_remaining}
            </div>
            <div className="text-sm text-slate-400">
              Use wisely
            </div>
          </motion.div>

          <motion.div
            className="card p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <UserGroupIcon className="w-5 h-5 text-purple-400" />
                <span className="text-sm text-slate-400">Squad Size</span>
              </div>
              <span className="text-xs text-purple-400">Active</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">
              {squadData.playing_players.length}/17
            </div>
            <div className="text-sm text-slate-400">
              {squadData.bench_players.length} on bench
            </div>
          </motion.div>
        </div>

        {/* Captain & Vice Captain */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <motion.div
            className="card"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="p-6 border-b border-slate-700">
              <div className="flex items-center space-x-2">
                <FireIcon className="w-5 h-5 text-orange-400" />
                <h2 className="text-lg font-semibold text-white">Captain</h2>
                <span className="text-xs text-orange-400">2x Points</span>
              </div>
            </div>
            <div className="p-6">
              {squadData.captain ? (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-orange-500/20 rounded-full flex items-center justify-center">
                      <span className="text-orange-400 font-bold">C</span>
                    </div>
                    <div>
                      <div className="font-medium text-white">{squadData.captain.name}</div>
                      <div className="text-sm text-slate-400">{squadData.captain.team} • {squadData.captain.position}</div>
                      <div className="text-xs text-slate-500">
                        Form: <span className={getFormRatingColor(squadData.captain.form_rating)}>
                          {squadData.captain.form_rating?.toFixed(1) || 'N/A'}/10
                        </span>
                      </div>
                    </div>
                  </div>
                  <button type="button" className="btn-outline text-sm">Change</button>
                </div>
              ) : (
                <div className="text-center py-8 text-slate-400">
                  <div className="text-sm">No captain selected</div>
                  <button type="button" className="btn-primary mt-2">Select Captain</button>
                </div>
              )}
            </div>
          </motion.div>

          <motion.div
            className="card"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <div className="p-6 border-b border-slate-700">
              <div className="flex items-center space-x-2">
                <ChartBarIcon className="w-5 h-5 text-blue-400" />
                <h2 className="text-lg font-semibold text-white">Vice Captain</h2>
                <span className="text-xs text-blue-400">Backup</span>
              </div>
            </div>
            <div className="p-6">
              {squadData.vice_captain ? (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center">
                      <span className="text-blue-400 font-bold">VC</span>
                    </div>
                    <div>
                      <div className="font-medium text-white">{squadData.vice_captain.name}</div>
                      <div className="text-sm text-slate-400">{squadData.vice_captain.team} • {squadData.vice_captain.position}</div>
                      <div className="text-xs text-slate-500">
                        Form: <span className={getFormRatingColor(squadData.vice_captain.form_rating)}>
                          {squadData.vice_captain.form_rating?.toFixed(1) || 'N/A'}/10
                        </span>
                      </div>
                    </div>
                  </div>
                  <button type="button" className="btn-outline text-sm">Change</button>
                </div>
              ) : (
                <div className="text-center py-8 text-slate-400">
                  <div className="text-sm">No vice captain selected</div>
                  <button type="button" className="btn-primary mt-2">Select Vice Captain</button>
                </div>
              )}
            </div>
          </motion.div>
        </div>

        {/* Squad Players List */}
        <motion.div
          className="card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <div className="p-6 border-b border-slate-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <UserGroupIcon className="w-5 h-5 text-blue-400" />
                <h2 className="text-lg font-semibold text-white">Squad Players</h2>
                <span className="text-xs text-blue-400">
                  {squadData.players.length}/26 players
                </span>
              </div>
              <div className="text-sm text-slate-400">
                Click players to set as captain/vice captain
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="space-y-3">
              {squadData.players.map((player, index) => (
                <motion.div
                  key={player.id}
                  className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600 hover:border-slate-500 transition-colors"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex flex-col items-center space-y-1">
                      {player.is_captain && (
                        <div className="w-6 h-6 bg-orange-500/20 rounded-full flex items-center justify-center">
                          <span className="text-orange-400 text-xs font-bold">C</span>
                        </div>
                      )}
                      {player.is_vice_captain && (
                        <div className="w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center">
                          <span className="text-blue-400 text-xs font-bold">VC</span>
                        </div>
                      )}
                      {!player.is_captain && !player.is_vice_captain && (
                        <div className="w-6 h-6"></div>
                      )}
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div className="font-medium text-white">{player.name}</div>
                        <span className="px-2 py-1 rounded text-xs font-medium bg-slate-600 text-slate-300">
                          {player.position}
                        </span>
                        <span className="text-sm text-slate-400">{player.team}</span>
                      </div>
                      <div className="flex items-center space-x-4 mt-1 text-xs text-slate-500">
                        <span>Price: {formatCurrency(player.current_price)}</span>
                        <span>Avg: {player.season_average?.toFixed(1) || 'N/A'}</span>
                        <span className={getFormRatingColor(player.form_rating)}>
                          Form: {player.form_rating?.toFixed(1) || 'N/A'}/10
                        </span>
                        {!player.is_playing && (
                          <span className="text-red-400">BENCH</span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      type="button" onClick={() => setCaptain(player.id)}
                      className="px-2 py-1 text-xs bg-orange-600/20 text-orange-400 rounded hover:bg-orange-600/30 transition-colors"
                      disabled={player.is_captain}
                    >
                      Captain
                    </button>
                    <button
                      type="button" onClick={() => setViceCaptain(player.id)}
                      className="px-2 py-1 text-xs bg-blue-600/20 text-blue-400 rounded hover:bg-blue-600/30 transition-colors"
                      disabled={player.is_vice_captain}
                    >
                      Vice
                    </button>
                    <button
                      type="button" onClick={() => handleTradePlayer(player)}
                      className="px-2 py-1 text-xs bg-green-600/20 text-green-400 rounded hover:bg-green-600/30 transition-colors"
                    >
                      Trade
                    </button>
                    <button
                      type="button" onClick={() => togglePlayerStatus(player.id)}
                      className="px-2 py-1 text-xs bg-purple-600/20 text-purple-400 rounded hover:bg-purple-600/30 transition-colors"
                    >
                      {player.is_playing ? 'Bench' : 'Play'}
                    </button>
                    <button
                      type="button" onClick={() => editPlayer(player)}
                      className="p-2 text-slate-400 hover:text-white transition-colors"
                    >
                      <PencilIcon className="w-4 h-4" />
                    </button>
                    <button
                      type="button" onClick={() => removePlayer(player.id)}
                      className="p-2 text-red-400 hover:text-red-300 transition-colors"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                </motion.div>
              ))}

              {squadData.players.length === 0 && (
                <div className="text-center py-12 text-slate-400">
                  <UserGroupIcon className="w-12 h-12 mx-auto mb-4 text-slate-600" />
                  <div className="text-lg font-medium mb-2">No players in squad</div>
                  <div className="text-sm mb-4">Add players to start building your team</div>
                  <button
                    type="button" className="btn-primary"
                    onClick={() => setShowAddPlayerModal(true)}
                  >
                    <PlusIcon className="w-4 h-4 mr-2" />
                    Add Your First Player
                  </button>
                </div>
              )}
            </div>
          </div>
        </motion.div>

        {/* Add Player Modal */}
        <AnimatePresence>
          {showAddPlayerModal && (
            <motion.div
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md"
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
              >
                <div className="p-6 border-b border-slate-700">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-white">Add New Player</h3>
                    <button
                      type="button" onClick={() => setShowAddPlayerModal(false)}
                      className="text-slate-400 hover:text-white"
                    >
                      <XMarkIcon className="w-6 h-6" />
                    </button>
                  </div>
                </div>

                <div className="p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Player Name *
                    </label>
                    <PredictiveSearch
                      placeholder="Type 'Na' for Nathan Cleary, 'Re' for Reece Walsh..."
                      onPlayerSelect={(player) => {
                        setNewPlayer({
                          ...newPlayer,
                          name: player.name,
                          team: player.team,
                          position: player.position,
                          current_price: player.price || 0,
                          season_points: player.points || 0,
                          season_average: player.average || 0,
                          form_rating: player.form || 0
                        });
                      }}
                      showPlayerDetails={true}
                      maxResults={8}
                      minQueryLength={1}
                      clearOnSelect={false}
                      className="w-full"
                    />
                    {newPlayer.name && (
                      <div className="mt-2 p-2 bg-green-500/10 border border-green-500/20 rounded text-sm text-green-400">
                        ✅ Selected: {newPlayer.name} ({newPlayer.team})
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Team * {newPlayer.team && <span className="text-green-400 text-xs">(Auto-filled)</span>}
                      </label>
                      <input
                        type="text"
                        className="input-primary w-full"
                        placeholder="e.g. Brisbane Broncos"
                        value={newPlayer.team || ''}
                        onChange={(e) => setNewPlayer({ ...newPlayer, team: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Position * {newPlayer.position && <span className="text-green-400 text-xs">(Auto-filled)</span>}
                      </label>
                      <select
                        className="input-primary w-full"
                        value={newPlayer.position || ''}
                        onChange={(e) => setNewPlayer({ ...newPlayer, position: e.target.value })}
                      >
                        <option value="">Select position</option>
                        <option value="FLB">Fullback</option>
                        <option value="CTW">Centre/Wing</option>
                        <option value="HFB">Halfback</option>
                        <option value="5/8">Five-eighth</option>
                        <option value="HOK">Hooker</option>
                        <option value="FRF">Front Row Forward</option>
                        <option value="2RF">Second Row Forward</option>
                        <option value="LCK">Lock</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Current Price ($) {newPlayer.current_price && newPlayer.current_price > 0 && <span className="text-green-400 text-xs">(Auto-filled)</span>}
                      </label>
                      <input
                        type="number"
                        className="input-primary w-full"
                        placeholder="0"
                        value={newPlayer.current_price || ''}
                        onChange={(e) => setNewPlayer({ ...newPlayer, current_price: parseInt(e.target.value) || 0 })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Season Average {newPlayer.season_average && newPlayer.season_average > 0 && <span className="text-green-400 text-xs">(Auto-filled)</span>}
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        className="input-primary w-full"
                        placeholder="0.0"
                        value={newPlayer.season_average || ''}
                        onChange={(e) => setNewPlayer({ ...newPlayer, season_average: parseFloat(e.target.value) || 0 })}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Season Points {newPlayer.season_points && newPlayer.season_points > 0 && <span className="text-green-400 text-xs">(Auto-filled)</span>}
                      </label>
                      <input
                        type="number"
                        className="input-primary w-full"
                        placeholder="0"
                        value={newPlayer.season_points || ''}
                        onChange={(e) => setNewPlayer({ ...newPlayer, season_points: parseInt(e.target.value) || 0 })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Form Rating (0-10) {newPlayer.form_rating && newPlayer.form_rating > 0 && <span className="text-green-400 text-xs">(Auto-filled)</span>}
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        min="0"
                        max="10"
                        className="input-primary w-full"
                        placeholder="0.0"
                        value={newPlayer.form_rating || ''}
                        onChange={(e) => setNewPlayer({ ...newPlayer, form_rating: parseFloat(e.target.value) || 0 })}
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="is_playing"
                      className="rounded border-slate-600 bg-slate-800 text-green-600 focus:ring-green-500"
                      checked={newPlayer.is_playing ?? true}
                      onChange={(e) => setNewPlayer({ ...newPlayer, is_playing: e.target.checked })}
                    />
                    <label htmlFor="is_playing" className="text-sm text-slate-300">
                      Playing this round (uncheck for bench)
                    </label>
                  </div>
                </div>

                <div className="p-6 border-t border-slate-700 flex justify-between">
                  <button
                    type="button"
                    onClick={() => {
                      setNewPlayer({
                        name: '',
                        team: '',
                        position: '',
                        current_price: 0,
                        season_points: 0,
                        season_average: 0,
                        form_rating: 0,
                        is_playing: true
                      });
                    }}
                    className="btn-outline text-slate-400 hover:text-white"
                  >
                    Clear Form
                  </button>

                  <div className="flex space-x-3">
                    <button
                      type="button" onClick={() => setShowAddPlayerModal(false)}
                      className="btn-outline"
                    >
                      Cancel
                    </button>
                    <button
                      type="button" onClick={addPlayer}
                      className="btn-primary"
                      disabled={!newPlayer.name || !newPlayer.team || !newPlayer.position}
                    >
                      Add Player
                    </button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Edit Player Modal */}
        <AnimatePresence>
          {showEditPlayerModal && editingPlayer && (
            <motion.div
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md"
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
              >
                <div className="p-6 border-b border-slate-700">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-white">Edit Player</h3>
                    <button
                      type="button" onClick={() => setShowEditPlayerModal(false)}
                      className="text-slate-400 hover:text-white"
                    >
                      <XMarkIcon className="w-6 h-6" />
                    </button>
                  </div>
                </div>

                <div className="p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      Player Name *
                    </label>
                    <input
                      type="text"
                      className="input-primary w-full"
                      placeholder="Enter player name"
                      value={newPlayer.name || ''}
                      onChange={(e) => setNewPlayer({ ...newPlayer, name: e.target.value })}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Team *
                      </label>
                      <input
                        type="text"
                        className="input-primary w-full"
                        placeholder="e.g. Brisbane Broncos"
                        value={newPlayer.team || ''}
                        onChange={(e) => setNewPlayer({ ...newPlayer, team: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Position *
                      </label>
                      <select
                        className="input-primary w-full"
                        value={newPlayer.position || ''}
                        onChange={(e) => setNewPlayer({ ...newPlayer, position: e.target.value })}
                      >
                        <option value="">Select position</option>
                        <option value="FLB">Fullback</option>
                        <option value="CTW">Centre/Wing</option>
                        <option value="HFB">Halfback</option>
                        <option value="5/8">Five-eighth</option>
                        <option value="HOK">Hooker</option>
                        <option value="FRF">Front Row Forward</option>
                        <option value="2RF">Second Row Forward</option>
                        <option value="LCK">Lock</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Current Price ($)
                      </label>
                      <input
                        type="number"
                        className="input-primary w-full"
                        placeholder="0"
                        value={newPlayer.current_price || ''}
                        onChange={(e) => setNewPlayer({ ...newPlayer, current_price: parseInt(e.target.value) || 0 })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Season Average
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        className="input-primary w-full"
                        placeholder="0.0"
                        value={newPlayer.season_average || ''}
                        onChange={(e) => setNewPlayer({ ...newPlayer, season_average: parseFloat(e.target.value) || 0 })}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Season Points
                      </label>
                      <input
                        type="number"
                        className="input-primary w-full"
                        placeholder="0"
                        value={newPlayer.season_points || ''}
                        onChange={(e) => setNewPlayer({ ...newPlayer, season_points: parseInt(e.target.value) || 0 })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Form Rating (0-10)
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        min="0"
                        max="10"
                        className="input-primary w-full"
                        placeholder="0.0"
                        value={newPlayer.form_rating || ''}
                        onChange={(e) => setNewPlayer({ ...newPlayer, form_rating: parseFloat(e.target.value) || 0 })}
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="edit_is_playing"
                      className="rounded border-slate-600 bg-slate-800 text-green-600 focus:ring-green-500"
                      checked={newPlayer.is_playing ?? true}
                      onChange={(e) => setNewPlayer({ ...newPlayer, is_playing: e.target.checked })}
                    />
                    <label htmlFor="edit_is_playing" className="text-sm text-slate-300">
                      Playing this round (uncheck for bench)
                    </label>
                  </div>
                </div>

                <div className="p-6 border-t border-slate-700 flex justify-end space-x-3">
                  <button
                    type="button" onClick={() => setShowEditPlayerModal(false)}
                    className="btn-outline"
                  >
                    Cancel
                  </button>
                  <button
                    type="button" onClick={updatePlayer}
                    className="btn-primary"
                    disabled={!newPlayer.name || !newPlayer.team || !newPlayer.position}
                  >
                    Update Player
                  </button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Trade Analysis Modal */}
        {showTradeAnalysis && tradePlayer && (
          <TradeAnalysis
            player={tradePlayer}
            onClose={() => {
              setShowTradeAnalysis(false);
              setTradePlayer(null);
            }}
            onExecuteTrade={handleExecuteTrade}
          />
        )}
      </div>
    </Layout>
  );
};

export default MyTeam;
