import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import { motion } from 'framer-motion';
import Layout from '../components/Layout';
import PredictiveSearch from '../components/PredictiveSearch';
import { PlayerService } from '../services/supabase';
import { HydrationBoundary } from '../utils/hydration';

interface Player {
  id: string;
  name: string;
  team: string;
  position: string;
  price: number;
  points: number;
  average: number;
  form: number;
}

const TestSearchDebug: NextPage = () => {
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [searchResults, setSearchResults] = useState<Player[]>([]);
  const [manualQuery, setManualQuery] = useState('');

  useEffect(() => {
    loadPlayers();
  }, []);

  const loadPlayers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔍 Loading players from Supabase...');
      const playersData = await PlayerService.getAllPlayers();
      
      console.log(`✅ Loaded ${playersData.length} players`);
      setPlayers(playersData);
      
    } catch (err) {
      console.error('❌ Error loading players:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const handlePlayerSelect = (player: Player) => {
    console.log('🎯 Player selected:', player);
    setSelectedPlayer(player);
  };

  const handleSearchChange = (query: string, results: Player[]) => {
    console.log(`🔍 Search changed: "${query}" -> ${results.length} results`);
    setSearchResults(results);
  };

  const manualSearch = () => {
    if (!manualQuery.trim()) {
      setSearchResults([]);
      return;
    }

    const query = manualQuery.toLowerCase();
    const results = players.filter(player => 
      player.name.toLowerCase().includes(query) ||
      player.team.toLowerCase().includes(query) ||
      player.position.toLowerCase().includes(query)
    );
    
    console.log(`🔍 Manual search for "${manualQuery}": ${results.length} results`);
    setSearchResults(results);
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-white">Loading players from Supabase...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center max-w-md mx-auto p-6">
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6">
              <h1 className="text-xl font-bold text-red-400 mb-4">Error Loading Players</h1>
              <p className="text-red-200 mb-4">{error}</p>
              <button
                type="button"
                onClick={loadPlayers}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Search Debug Test - FantasyPro</title>
      </Head>

      <HydrationBoundary>
        <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <motion.h1 
            className="text-4xl font-bold themed-text-primary mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            🔍 Search Debug Test
          </motion.h1>
          <p className="text-lg themed-text-tertiary">
            Testing Supabase connection and predictive search functionality
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="card p-4 text-center">
            <div className="text-2xl font-bold text-green-400">{players.length}</div>
            <div className="text-sm themed-text-tertiary">Total Players</div>
          </div>
          <div className="card p-4 text-center">
            <div className="text-2xl font-bold text-blue-400">{searchResults.length}</div>
            <div className="text-sm themed-text-tertiary">Search Results</div>
          </div>
          <div className="card p-4 text-center">
            <div className="text-2xl font-bold text-purple-400">{selectedPlayer ? 1 : 0}</div>
            <div className="text-sm themed-text-tertiary">Selected</div>
          </div>
          <div className="card p-4 text-center">
            <div className="text-2xl font-bold themed-text-primary">
              {players.length >= 500 ? '✅' : '❌'}
            </div>
            <div className="text-sm themed-text-tertiary">Data Complete</div>
          </div>
        </div>

        {/* Predictive Search Test */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold themed-text-primary mb-4">
            🎯 Predictive Search Component Test
          </h2>
          
          <div className="max-w-md">
            <label className="block text-sm font-medium themed-text-secondary mb-2">
              Search Players (try "Nathan", "Storm", "FLB")
            </label>
            <PredictiveSearch
              placeholder="Type to search players..."
              onPlayerSelect={handlePlayerSelect}
              onSearchChange={handleSearchChange}
              showPlayerDetails={true}
              maxResults={8}
              minQueryLength={1}
              className="w-full"
            />
          </div>

          {selectedPlayer && (
            <div className="mt-4 p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
              <h3 className="font-semibold text-green-400 mb-2">Selected Player:</h3>
              <div className="text-sm space-y-1">
                <div><strong>Name:</strong> {selectedPlayer.name}</div>
                <div><strong>Team:</strong> {selectedPlayer.team}</div>
                <div><strong>Position:</strong> {selectedPlayer.position}</div>
                <div><strong>Price:</strong> ${(selectedPlayer.price / 1000000).toFixed(2)}M</div>
                <div><strong>Average:</strong> {selectedPlayer.average.toFixed(1)}</div>
              </div>
            </div>
          )}
        </div>

        {/* Manual Search Test */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold themed-text-primary mb-4">
            🔧 Manual Search Test
          </h2>
          
          <div className="flex space-x-3 mb-4">
            <input
              type="text"
              className="input-primary flex-1"
              placeholder="Enter search term..."
              value={manualQuery}
              onChange={(e) => setManualQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && manualSearch()}
            />
            <button
              type="button"
              onClick={manualSearch}
              className="btn-primary"
            >
              Search
            </button>
          </div>

          {searchResults.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {searchResults.slice(0, 12).map((player) => (
                <div
                  key={player.id}
                  className="p-3 themed-bg-secondary themed-border rounded-lg hover:themed-bg-tertiary transition-colors cursor-pointer"
                  onClick={() => handlePlayerSelect(player)}
                >
                  <div className="font-medium themed-text-primary">{player.name}</div>
                  <div className="text-sm themed-text-secondary">{player.team} • {player.position}</div>
                  <div className="text-xs themed-text-tertiary">
                    ${(player.price / 1000000).toFixed(2)}M • Avg: {player.average.toFixed(1)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Sample Players */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold themed-text-primary mb-4">
            📊 Sample Players (First 10)
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {players.slice(0, 10).map((player) => (
              <div
                key={player.id}
                className="p-3 themed-bg-secondary themed-border rounded-lg"
              >
                <div className="font-medium themed-text-primary">{player.name}</div>
                <div className="text-sm themed-text-secondary">{player.team} • {player.position}</div>
                <div className="text-xs themed-text-tertiary">
                  ${(player.price / 1000000).toFixed(2)}M • {player.points} pts • Avg: {player.average.toFixed(1)}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Debug Actions */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold themed-text-primary mb-4">
            🛠️ Debug Actions
          </h2>
          
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={loadPlayers}
              className="btn-primary"
            >
              Reload Players
            </button>
            <button
              type="button"
              onClick={() => console.log('Current players:', players)}
              className="btn-secondary"
            >
              Log Players to Console
            </button>
            <button
              type="button"
              onClick={() => {
                setSelectedPlayer(null);
                setSearchResults([]);
                setManualQuery('');
              }}
              className="btn-outline"
            >
              Clear All
            </button>
          </div>
        </div>
        </div>
      </HydrationBoundary>
    </Layout>
  );
};

export default TestSearchDebug;
