{"c": ["webpack"], "r": ["pages/settings"], "m": ["./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js", "./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js", "./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cjonah%5CDesktop%5CORIGVMI%5CGithub%5CPlayground%5COCTOAGENT%5Cfantasypro%5Cweb%5Csrc%5Cpages%5Csettings.tsx&page=%2Fsettings!", "./src/pages/settings.tsx", "__barrel_optimize__?names=BellIcon,CheckCircleIcon,CogIcon,KeyIcon,MoonIcon,ShieldCheckIcon,SunIcon,TrashIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js"]}