import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-slate-900">
          <div className="max-w-2xl mx-auto p-8">
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">!</span>
                </div>
                <h1 className="text-xl font-bold text-red-400">Something went wrong</h1>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-red-300 mb-2">Error Details:</h3>
                  <div className="bg-slate-800 rounded p-3 text-sm text-red-200 font-mono">
                    {this.state.error?.message || 'Unknown error occurred'}
                  </div>
                </div>
                
                {this.state.error?.stack && (
                  <div>
                    <h3 className="text-sm font-medium text-red-300 mb-2">Stack Trace:</h3>
                    <div className="bg-slate-800 rounded p-3 text-xs text-gray-400 font-mono max-h-40 overflow-y-auto">
                      {this.state.error.stack}
                    </div>
                  </div>
                )}
                
                {this.state.errorInfo?.componentStack && (
                  <div>
                    <h3 className="text-sm font-medium text-red-300 mb-2">Component Stack:</h3>
                    <div className="bg-slate-800 rounded p-3 text-xs text-gray-400 font-mono max-h-40 overflow-y-auto">
                      {this.state.errorInfo.componentStack}
                    </div>
                  </div>
                )}
                
                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => window.location.reload()}
                    className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-medium transition-colors"
                  >
                    Reload Page
                  </button>
                  <button
                    onClick={() => window.history.back()}
                    className="px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg text-sm font-medium transition-colors"
                  >
                    Go Back
                  </button>
                </div>
              </div>
            </div>
            
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-400">
                If this error persists, please check the browser console for more details.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
