"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/players";
exports.ids = ["pages/api/players"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fplayers&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cplayers.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fplayers&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cplayers.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_players_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\players.ts */ \"(api)/./src/pages/api/players.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_players_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_players_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/players\",\n        pathname: \"/api/players\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_players_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnBsYXllcnMmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZzcmMlNUNwYWdlcyU1Q2FwaSU1Q3BsYXllcnMudHMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ0w7QUFDMUQ7QUFDMEQ7QUFDMUQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLHNEQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQyxzREFBUTtBQUNwQztBQUNPLHdCQUF3QixnSEFBbUI7QUFDbEQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mYW50YXN5cHJvLXdlYi8/MDUyYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vc3JjXFxcXHBhZ2VzXFxcXGFwaVxcXFxwbGF5ZXJzLnRzXCI7XG4vLyBSZS1leHBvcnQgdGhlIGhhbmRsZXIgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsIFwiZGVmYXVsdFwiKTtcbi8vIFJlLWV4cG9ydCBjb25maWcuXG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsIFwiY29uZmlnXCIpO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgUGFnZXNBUElSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuUEFHRVNfQVBJLFxuICAgICAgICBwYWdlOiBcIi9hcGkvcGxheWVyc1wiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3BsYXllcnNcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIlxuICAgIH0sXG4gICAgdXNlcmxhbmRcbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYWdlcy1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fplayers&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cplayers.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/players.ts":
/*!**********************************!*\
  !*** ./src/pages/api/players.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _services_playerDataService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../services/playerDataService */ \"(api)/./src/services/playerDataService.ts\");\n/**\n * Players API Endpoint\n * Serves cached NRL player data to FantasyPro frontend\n */ \nasync function handler(req, res) {\n    const timestamp = new Date().toISOString();\n    try {\n        if (req.method === \"GET\") {\n            const { team, position, search, limit, top, stats } = req.query;\n            console.log(\"\\uD83D\\uDCE1 Players API request:\", {\n                team,\n                position,\n                search,\n                limit,\n                top,\n                stats\n            });\n            let players = [];\n            // Handle different query types\n            if (stats === \"true\") {\n                // Return cache statistics\n                const cacheStats = await _services_playerDataService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getCacheStats();\n                return res.status(200).json({\n                    success: true,\n                    data: {\n                        players: [],\n                        total: cacheStats.total_players,\n                        filters: {\n                            teams: cacheStats.teams,\n                            positions: cacheStats.positions\n                        },\n                        cache_info: {\n                            last_updated: cacheStats.last_updated,\n                            cache_age_minutes: cacheStats.cache_age_minutes\n                        }\n                    },\n                    timestamp\n                });\n            }\n            if (top === \"true\") {\n                // Get top players by average\n                const limitNum = limit ? parseInt(limit) : 20;\n                players = await _services_playerDataService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getTopPlayers(limitNum);\n            } else if (search) {\n                // Search players by name/team\n                players = await _services_playerDataService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].searchPlayers(search);\n            } else if (team) {\n                // Filter by team\n                players = await _services_playerDataService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getPlayersByTeam(team);\n            } else if (position) {\n                // Filter by position\n                players = await _services_playerDataService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getPlayersByPosition(position);\n            } else {\n                // Get all players\n                players = await _services_playerDataService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getAllPlayers();\n            }\n            // Apply limit if specified\n            if (limit && !top) {\n                const limitNum = parseInt(limit);\n                players = players.slice(0, limitNum);\n            }\n            console.log(`✅ Returning ${players.length} players`);\n            // Get cache info\n            const cacheStats = await _services_playerDataService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getCacheStats();\n            return res.status(200).json({\n                success: true,\n                data: {\n                    players,\n                    total: players.length,\n                    filters: {\n                        teams: cacheStats.teams,\n                        positions: cacheStats.positions\n                    },\n                    cache_info: {\n                        last_updated: cacheStats.last_updated,\n                        cache_age_minutes: cacheStats.cache_age_minutes\n                    }\n                },\n                timestamp\n            });\n        } else if (req.method === \"POST\") {\n            // Handle cache refresh\n            const { action } = req.body;\n            if (action === \"refresh\") {\n                _services_playerDataService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].refreshCache();\n                return res.status(200).json({\n                    success: true,\n                    data: {\n                        players: [],\n                        total: 0\n                    },\n                    timestamp\n                });\n            }\n            return res.status(400).json({\n                success: false,\n                error: \"Invalid action\",\n                timestamp\n            });\n        } else {\n            return res.status(405).json({\n                success: false,\n                error: \"Method not allowed\",\n                timestamp\n            });\n        }\n    } catch (error) {\n        console.error(\"\\uD83D\\uDCA5 Players API error:\", error);\n        return res.status(500).json({\n            success: false,\n            error: error instanceof Error ? error.message : \"Internal server error\",\n            timestamp\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/players.ts\n");

/***/ }),

/***/ "(api)/./src/services/playerDataService.ts":
/*!*******************************************!*\
  !*** ./src/services/playerDataService.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PlayerDataService: () => (/* binding */ PlayerDataService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Player Data Integration Service\n * Combines cached NRL data with live web scraping\n */ \n\n// Team mapping for NRL players\nconst TEAM_MAPPING = {\n    // Common team abbreviations to full names\n    \"BRO\": \"Brisbane Broncos\",\n    \"SYD\": \"Sydney Roosters\",\n    \"MEL\": \"Melbourne Storm\",\n    \"PEN\": \"Penrith Panthers\",\n    \"CRO\": \"Cronulla Sharks\",\n    \"MAN\": \"Manly Sea Eagles\",\n    \"RAB\": \"South Sydney Rabbitohs\",\n    \"PAR\": \"Parramatta Eels\",\n    \"NEW\": \"Newcastle Knights\",\n    \"CAN\": \"Canberra Raiders\",\n    \"GLD\": \"Gold Coast Titans\",\n    \"STI\": \"St George Illawarra Dragons\",\n    \"CNT\": \"Canterbury Bulldogs\",\n    \"TIG\": \"Wests Tigers\",\n    \"COW\": \"North Queensland Cowboys\",\n    \"NZW\": \"New Zealand Warriors\",\n    \"DOL\": \"Dolphins\"\n};\n// Player to team mapping (this would ideally come from a more comprehensive source)\nconst PLAYER_TEAM_MAPPING = {\n    \"reece walsh\": \"Brisbane Broncos\",\n    \"josiah karapani\": \"Brisbane Broncos\",\n    \"kotoni staggs\": \"Brisbane Broncos\",\n    \"herbie farnworth\": \"Dolphins\",\n    \"hamiso tabuai-fidow\": \"Dolphins\",\n    \"jahrome hughes\": \"Melbourne Storm\",\n    \"ryan papenhuyzen\": \"Melbourne Storm\",\n    \"nathan cleary\": \"Penrith Panthers\",\n    \"jarome luai\": \"Penrith Panthers\",\n    \"james tedesco\": \"Sydney Roosters\",\n    \"joseph manu\": \"Sydney Roosters\"\n};\nclass PlayerDataService {\n    static{\n        this.cachedPlayers = null;\n    }\n    static{\n        this.lastCacheUpdate = 0;\n    }\n    static{\n        this.CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n        ;\n    }\n    /**\n   * Get the path to the cached player data\n   */ static getCachedDataPath() {\n        // In production, this would be an environment variable\n        const basePath = process.cwd();\n        return path__WEBPACK_IMPORTED_MODULE_1___default().join(basePath, \"..\", \"data\", \"nrl_player_cache\", \"nrl_player_latest.json\");\n    }\n    /**\n   * Load cached player data from JSON file\n   */ static loadCachedData() {\n        try {\n            const dataPath = this.getCachedDataPath();\n            // Check if file exists\n            if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(dataPath)) {\n                console.warn(\"Cached player data file not found:\", dataPath);\n                return null;\n            }\n            const rawData = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(dataPath, \"utf-8\");\n            const data = JSON.parse(rawData);\n            console.log(`✅ Loaded cached data from ${data.collection_timestamp}`);\n            console.log(`📊 Found ${Object.keys(data.consolidated_players).length} players in cache`);\n            return data;\n        } catch (error) {\n            console.error(\"❌ Error loading cached player data:\", error);\n            return null;\n        }\n    }\n    /**\n   * Convert cached player data to FantasyPro format\n   */ static convertCachedPlayer(playerKey, playerData, index) {\n        const stats = playerData.consolidated_stats;\n        const playerName = playerData.name;\n        // Generate unique ID\n        const id = `player_${index + 1}`;\n        // Get team from mapping or default\n        const team = PLAYER_TEAM_MAPPING[playerKey.toLowerCase()] || \"Unknown Team\";\n        // Convert price from string to number (remove any formatting)\n        const price = parseInt(stats.price.replace(/[^0-9]/g, \"\")) || 0;\n        // Convert other numeric fields\n        const average = parseFloat(stats.avg) || 0;\n        const breakeven = parseInt(stats.be) || 0;\n        const gamesPlayed = parseInt(stats.played) || 0;\n        const minutesPerGame = parseInt(stats.mins) || 0;\n        // Calculate total points (average * games played)\n        const points = Math.round(average * gamesPlayed);\n        // Calculate form rating (simplified - could be more sophisticated)\n        const form = Math.min(10, Math.max(0, average / 10));\n        return {\n            id,\n            name: playerName,\n            position: stats.posn || \"Unknown\",\n            team,\n            price,\n            points,\n            average,\n            form,\n            breakeven,\n            games_played: gamesPlayed,\n            minutes_per_game: minutesPerGame,\n            source: \"cached_nrl_data\",\n            last_updated: new Date().toISOString()\n        };\n    }\n    /**\n   * Get all players from cached data\n   */ static async getAllPlayers() {\n        // Return cached data if still valid\n        const now = Date.now();\n        if (this.cachedPlayers && now - this.lastCacheUpdate < this.CACHE_DURATION) {\n            console.log(\"\\uD83D\\uDCCB Returning cached players data\");\n            return this.cachedPlayers;\n        }\n        console.log(\"\\uD83D\\uDD04 Loading fresh player data...\");\n        try {\n            // Load cached data\n            const cachedData = this.loadCachedData();\n            if (!cachedData) {\n                console.warn(\"⚠️ No cached data available, returning empty array\");\n                return [];\n            }\n            // Convert to FantasyPro format\n            const players = [];\n            const consolidatedPlayers = cachedData.consolidated_players;\n            Object.entries(consolidatedPlayers).forEach(([playerKey, playerData], index)=>{\n                try {\n                    const player = this.convertCachedPlayer(playerKey, playerData, index);\n                    players.push(player);\n                } catch (error) {\n                    console.warn(`⚠️ Error converting player ${playerKey}:`, error);\n                }\n            });\n            // Sort players by team and name\n            players.sort((a, b)=>{\n                if (a.team !== b.team) {\n                    return a.team.localeCompare(b.team);\n                }\n                return a.name.localeCompare(b.name);\n            });\n            console.log(`✅ Converted ${players.length} players from cached data`);\n            // Cache the results\n            this.cachedPlayers = players;\n            this.lastCacheUpdate = now;\n            return players;\n        } catch (error) {\n            console.error(\"❌ Error getting players:\", error);\n            return [];\n        }\n    }\n    /**\n   * Get players by team\n   */ static async getPlayersByTeam(teamName) {\n        const allPlayers = await this.getAllPlayers();\n        return allPlayers.filter((player)=>player.team.toLowerCase().includes(teamName.toLowerCase()));\n    }\n    /**\n   * Get players by position\n   */ static async getPlayersByPosition(position) {\n        const allPlayers = await this.getAllPlayers();\n        return allPlayers.filter((player)=>player.position.toLowerCase() === position.toLowerCase());\n    }\n    /**\n   * Search players by name\n   */ static async searchPlayers(query) {\n        const allPlayers = await this.getAllPlayers();\n        const searchTerm = query.toLowerCase();\n        return allPlayers.filter((player)=>player.name.toLowerCase().includes(searchTerm) || player.team.toLowerCase().includes(searchTerm));\n    }\n    /**\n   * Get player by ID\n   */ static async getPlayerById(id) {\n        const allPlayers = await this.getAllPlayers();\n        return allPlayers.find((player)=>player.id === id) || null;\n    }\n    /**\n   * Get top players by average\n   */ static async getTopPlayers(limit = 20) {\n        const allPlayers = await this.getAllPlayers();\n        return allPlayers.filter((player)=>player.games_played && player.games_played > 3) // Only players with significant games\n        .sort((a, b)=>b.average - a.average).slice(0, limit);\n    }\n    /**\n   * Get cache statistics\n   */ static async getCacheStats() {\n        const players = await this.getAllPlayers();\n        const teams = [\n            ...new Set(players.map((p)=>p.team))\n        ].sort();\n        const positions = [\n            ...new Set(players.map((p)=>p.position))\n        ].sort();\n        return {\n            total_players: players.length,\n            teams,\n            positions,\n            last_updated: players[0]?.last_updated || \"Unknown\",\n            cache_age_minutes: Math.round((Date.now() - this.lastCacheUpdate) / 60000)\n        };\n    }\n    /**\n   * Refresh cache (force reload)\n   */ static refreshCache() {\n        this.cachedPlayers = null;\n        this.lastCacheUpdate = 0;\n        console.log(\"\\uD83D\\uDD04 Player cache cleared, will reload on next request\");\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlayerDataService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/services/playerDataService.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fplayers&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cplayers.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();