/**
 * Players API Endpoint
 * Serves cached NRL player data to FantasyPro frontend
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { PlayerService, Player } from '../../services/supabase';
import { getStableTimestamp } from '../../utils/hydration-utils';

interface PlayersApiResponse {
  success: boolean;
  data?: {
    players: Player[];
    total: number;
    filters?: {
      teams: string[];
      positions: string[];
    };
    cache_info?: {
      last_updated: string;
      cache_age_minutes: number;
    };
  };
  error?: string;
  timestamp: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PlayersApiResponse>
) {
  const timestamp = getStableTimestamp();

  try {
    if (req.method === 'GET') {
      const { 
        team, 
        position, 
        search, 
        limit, 
        top,
        stats 
      } = req.query;

      console.log('📡 Players API request:', { team, position, search, limit, top, stats });

      let players: Player[] = [];

      // Handle different query types
      if (stats === 'true') {
        // Return basic statistics
        const allPlayers = await PlayerService.getAllPlayers();
        const teams = [...new Set(allPlayers.map(p => p.team))];
        const positions = [...new Set(allPlayers.map(p => p.position))];

        return res.status(200).json({
          success: true,
          data: {
            players: [],
            total: allPlayers.length,
            filters: {
              teams: teams,
              positions: positions
            },
            cache_info: {
              last_updated: getStableTimestamp(),
              cache_age_minutes: 0
            }
          },
          timestamp
        });
      }

      if (top === 'true') {
        // Get top players by average
        const limitNum = limit ? parseInt(limit as string) : 20;
        const allPlayers = await PlayerService.getAllPlayers();
        players = allPlayers
          .sort((a, b) => (b.average || 0) - (a.average || 0))
          .slice(0, limitNum);

      } else if (search) {
        // Search players by name/team
        const allPlayers = await PlayerService.getAllPlayers();
        const searchTerm = (search as string).toLowerCase();
        players = allPlayers.filter(player =>
          player.name.toLowerCase().includes(searchTerm) ||
          player.team.toLowerCase().includes(searchTerm) ||
          player.position.toLowerCase().includes(searchTerm)
        );

      } else if (team) {
        // Filter by team
        const allPlayers = await PlayerService.getAllPlayers();
        players = allPlayers.filter(player =>
          player.team.toLowerCase().includes((team as string).toLowerCase())
        );

      } else if (position) {
        // Filter by position
        const allPlayers = await PlayerService.getAllPlayers();
        players = allPlayers.filter(player =>
          player.position.toLowerCase() === (position as string).toLowerCase()
        );

      } else {
        // Get all players from Supabase
        console.log('🔄 Loading complete player dataset from Supabase...');
        players = await PlayerService.getAllPlayers();

        console.log(`📊 Loaded ${players.length} players from Supabase`);
      }

      // Apply limit if specified
      if (limit && !top) {
        const limitNum = parseInt(limit as string);
        players = players.slice(0, limitNum);
      }

      console.log(`✅ Returning ${players.length} players`);

      // Get basic stats
      const teams = [...new Set(players.map(p => p.team))];
      const positions = [...new Set(players.map(p => p.position))];

      return res.status(200).json({
        success: true,
        data: {
          players,
          total: players.length,
          filters: {
            teams: teams,
            positions: positions
          },
          cache_info: {
            last_updated: getStableTimestamp(),
            cache_age_minutes: 0
          }
        },
        timestamp
      });

    } else if (req.method === 'POST') {
      // Handle data refresh
      const { action } = req.body;

      if (action === 'refresh') {
        // For Supabase, we don't need to refresh cache, data is always fresh
        const players = await PlayerService.getAllPlayers();

        return res.status(200).json({
          success: true,
          data: {
            players: [],
            total: players.length
          },
          timestamp
        });
      }
      
      return res.status(400).json({
        success: false,
        error: 'Invalid action',
        timestamp
      });

    } else {
      return res.status(405).json({
        success: false,
        error: 'Method not allowed',
        timestamp
      });
    }

  } catch (error) {
    console.error('💥 Players API error:', error);
    
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
      timestamp
    });
  }
}
