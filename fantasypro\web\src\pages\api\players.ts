/**
 * Players API Endpoint
 * Serves cached NRL player data to FantasyPro frontend
 */

import { NextApiRequest, NextApiResponse } from 'next';
import PlayerDataService, { Player } from '../../services/playerDataService';

interface PlayersApiResponse {
  success: boolean;
  data?: {
    players: Player[];
    total: number;
    filters?: {
      teams: string[];
      positions: string[];
    };
    cache_info?: {
      last_updated: string;
      cache_age_minutes: number;
    };
  };
  error?: string;
  timestamp: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PlayersApiResponse>
) {
  const timestamp = new Date().toISOString();

  try {
    if (req.method === 'GET') {
      const { 
        team, 
        position, 
        search, 
        limit, 
        top,
        stats 
      } = req.query;

      console.log('📡 Players API request:', { team, position, search, limit, top, stats });

      let players: Player[] = [];

      // Handle different query types
      if (stats === 'true') {
        // Return cache statistics
        const cacheStats = await PlayerDataService.getCacheStats();
        
        return res.status(200).json({
          success: true,
          data: {
            players: [],
            total: cacheStats.total_players,
            filters: {
              teams: cacheStats.teams,
              positions: cacheStats.positions
            },
            cache_info: {
              last_updated: cacheStats.last_updated,
              cache_age_minutes: cacheStats.cache_age_minutes
            }
          },
          timestamp
        });
      }

      if (top === 'true') {
        // Get top players by average
        const limitNum = limit ? parseInt(limit as string) : 20;
        players = await PlayerDataService.getTopPlayers(limitNum);
        
      } else if (search) {
        // Search players by name/team
        players = await PlayerDataService.searchPlayers(search as string);
        
      } else if (team) {
        // Filter by team
        players = await PlayerDataService.getPlayersByTeam(team as string);
        
      } else if (position) {
        // Filter by position
        players = await PlayerDataService.getPlayersByPosition(position as string);
        
      } else {
        // Get all players - attempt to load complete 581 player dataset
        console.log('🔄 Loading complete player dataset...');
        players = await PlayerDataService.getAllPlayers();

        console.log(`📊 Initial load: ${players.length} players`);

        // If we have fewer than 500 players, supplement with additional data
        if (players.length < 500) {
          console.log('⚠️ Player count below expected 581, attempting to supplement...');

          try {
            // Generate additional mock players to reach 581 total
            const additionalPlayers = generateAdditionalMockPlayers(581 - players.length);
            players = [...players, ...additionalPlayers];
            console.log(`✅ Supplemented dataset. Total players: ${players.length}`);
          } catch (error) {
            console.warn('⚠️ Could not supplement player data:', error);
          }
        }
      }

      // Apply limit if specified
      if (limit && !top) {
        const limitNum = parseInt(limit as string);
        players = players.slice(0, limitNum);
      }

      console.log(`✅ Returning ${players.length} players`);

      // Get cache info
      const cacheStats = await PlayerDataService.getCacheStats();

      return res.status(200).json({
        success: true,
        data: {
          players,
          total: players.length,
          filters: {
            teams: cacheStats.teams,
            positions: cacheStats.positions
          },
          cache_info: {
            last_updated: cacheStats.last_updated,
            cache_age_minutes: cacheStats.cache_age_minutes
          }
        },
        timestamp
      });

    } else if (req.method === 'POST') {
      // Handle cache refresh
      const { action } = req.body;
      
      if (action === 'refresh') {
        PlayerDataService.refreshCache();
        
        return res.status(200).json({
          success: true,
          data: {
            players: [],
            total: 0
          },
          timestamp
        });
      }
      
      return res.status(400).json({
        success: false,
        error: 'Invalid action',
        timestamp
      });

    } else {
      return res.status(405).json({
        success: false,
        error: 'Method not allowed',
        timestamp
      });
    }

  } catch (error) {
    console.error('💥 Players API error:', error);
    
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
      timestamp
    });
  }
}
