import { PlayerService, TradeService, InjuryService, AnalyticsService, CacheService } from './supabase';
import SportRadarService from './sportradar';
import DataTransformService from './dataTransform';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8004';
const CACHE_ENABLED = true;

// Enhanced API service with Supabase integration and local fallback
export class APIService {
  // Universal player search with cloud-powered predictive search
  static async searchPlayers(query: string, options: {
    useCache?: boolean;
    fallbackToLocal?: boolean;
    limit?: number;
  } = {}) {
    const { useCache = CACHE_ENABLED, fallbackToLocal = true, limit = 10 } = options;
    const cacheKey = `search_players_${query}_${limit}`;

    // Check cache first
    if (useCache) {
      const cached = CacheService.get(cacheKey);
      if (cached) return cached;
    }

    try {
      // Try Supabase first (cloud-powered)
      const players = await PlayerService.searchPlayers(query, limit);
      
      // Cache the results
      if (useCache) {
        CacheService.set(cacheKey, players);
      }

      return players;
    } catch (error) {
      console.error('Supabase search failed, trying local API:', error);

      if (fallbackToLocal) {
        try {
          // Fallback to local API
          const response = await fetch(`${API_BASE_URL}/players/search?q=${encodeURIComponent(query)}&limit=${limit}`);
          if (response.ok) {
            const data = await response.json();
            return data.players || [];
          }
        } catch (localError) {
          console.error('Local API also failed:', localError);
        }
      }

      // Return mock data as last resort
      return this.getMockPlayers().filter(p => 
        p.name.toLowerCase().includes(query.toLowerCase()) ||
        p.team.toLowerCase().includes(query.toLowerCase()) ||
        p.position.toLowerCase().includes(query.toLowerCase())
      ).slice(0, limit);
    }
  }

  // Get all players with advanced filtering
  static async getPlayers(options: {
    search?: string;
    position?: string;
    team?: string;
    limit?: number;
    offset?: number;
    useCache?: boolean;
    useRealData?: boolean;
  } = {}) {
    const { useCache = CACHE_ENABLED, useRealData = true } = options;
    const cacheKey = `players_${JSON.stringify(options)}`;

    if (useCache) {
      const cached = CacheService.get(cacheKey);
      if (cached) return cached;
    }

    // Try our new cached data API first
    try {
      console.log('🔄 Fetching players from cached data API...');

      let url = '/api/players';
      const params = new URLSearchParams();

      if (options.search) params.append('search', options.search);
      if (options.position) params.append('position', options.position);
      if (options.team) params.append('team', options.team);
      if (options.limit) params.append('limit', options.limit.toString());

      if (params.toString()) {
        url += '?' + params.toString();
      }

      const response = await fetch(url);

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data.players) {
          console.log(`✅ Loaded ${data.data.players.length} players from cached data`);

          if (useCache) {
            CacheService.set(cacheKey, data.data.players);
          }

          return data.data.players;
        }
      }
    } catch (error) {
      console.error('❌ Cached data API failed:', error);
    }

    try {
      // Try SportRadar first if real data is requested
      if (useRealData) {
        try {
          const sportRadarPlayers = await SportRadarService.getAllNRLPlayers();
          const transformedPlayers = DataTransformService.transformPlayers(sportRadarPlayers);

          // Apply filtering
          let filteredPlayers = transformedPlayers;

          if (options.search) {
            const searchTerm = options.search.toLowerCase();
            filteredPlayers = filteredPlayers.filter(player =>
              player.name.toLowerCase().includes(searchTerm) ||
              player.team.toLowerCase().includes(searchTerm)
            );
          }

          if (options.position) {
            filteredPlayers = filteredPlayers.filter(player => player.position === options.position);
          }

          if (options.team) {
            filteredPlayers = filteredPlayers.filter(player => player.team === options.team);
          }

          // Apply pagination
          if (options.offset || options.limit) {
            const start = options.offset || 0;
            const end = options.limit ? start + options.limit : undefined;
            filteredPlayers = filteredPlayers.slice(start, end);
          }

          if (useCache) {
            CacheService.set(cacheKey, filteredPlayers);
          }

          return filteredPlayers;
        } catch (sportRadarError) {
          console.error('SportRadar API failed, falling back to Supabase:', sportRadarError);
        }
      }

      // Fallback to Supabase
      const players = await PlayerService.getPlayers(options);

      if (useCache) {
        CacheService.set(cacheKey, players);
      }

      return players;
    } catch (error) {
      console.error('Error fetching players:', error);
      return this.getMockPlayers();
    }
  }

  // Get all players - used by Players page
  static async getAllPlayers() {
    console.log('🔄 APIService.getAllPlayers() called - attempting to load ALL 581 players');

    try {
      // First try SportRadar API for complete dataset
      console.log('🔄 Trying SportRadar API for complete player dataset...');
      const sportRadarPlayers = await this.getAllNRLPlayers(true);

      if (sportRadarPlayers && sportRadarPlayers.length > 100) {
        console.log(`✅ SportRadar API loaded ${sportRadarPlayers.length} players`);
        return sportRadarPlayers;
      }

      // Fallback to cached data API
      console.log('🔄 SportRadar failed, trying cached data API...');
      const response = await fetch('/api/players');

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data.players) {
          console.log(`✅ Cached data API loaded ${data.data.players.length} players`);

          // If we have less than expected, try to supplement with SportRadar
          if (data.data.players.length < 500) {
            console.log('⚠️ Cached data has fewer players than expected, supplementing with SportRadar...');
            try {
              const additionalPlayers = await this.getAllNRLPlayers(false);
              if (additionalPlayers && additionalPlayers.length > data.data.players.length) {
                console.log(`✅ SportRadar provided ${additionalPlayers.length} players, using that instead`);
                return additionalPlayers;
              }
            } catch (sportRadarError) {
              console.warn('⚠️ SportRadar supplement failed:', sportRadarError);
            }
          }

          return data.data.players;
        }
      }

      console.warn('⚠️ All APIs failed, falling back to mock data');
      return this.getMockPlayers();

    } catch (error) {
      console.error('❌ APIService.getAllPlayers() error:', error);
      return this.getMockPlayers();
    }
  }

  // Get all NRL players from SportRadar
  static async getAllNRLPlayers(useCache: boolean = true) {
    const cacheKey = 'all_nrl_players_transformed';

    if (useCache) {
      const cached = CacheService.get(cacheKey);
      if (cached) return cached;
    }

    try {
      const sportRadarPlayers = await SportRadarService.getAllNRLPlayers();
      const transformedPlayers = DataTransformService.transformPlayers(sportRadarPlayers);

      if (useCache) {
        CacheService.set(cacheKey, transformedPlayers);
      }

      return transformedPlayers;
    } catch (error) {
      console.error('Error fetching all NRL players from SportRadar:', error);
      return this.getMockPlayers();
    }
  }

  // Get NRL teams from SportRadar
  static async getNRLTeams(useCache: boolean = true) {
    const cacheKey = 'nrl_teams_transformed';

    if (useCache) {
      const cached = CacheService.get(cacheKey);
      if (cached) return cached;
    }

    try {
      const sportRadarTeams = await SportRadarService.getNRLTeams();
      const transformedTeams = DataTransformService.transformTeams(sportRadarTeams);

      if (useCache) {
        CacheService.set(cacheKey, transformedTeams);
      }

      return transformedTeams;
    } catch (error) {
      console.error('Error fetching NRL teams from SportRadar:', error);
      return [];
    }
  }

  // Get injury reports from SportRadar
  static async getInjuryReports(useCache: boolean = true) {
    const cacheKey = 'injury_reports_transformed';

    if (useCache) {
      const cached = CacheService.get(cacheKey);
      if (cached) return cached;
    }

    try {
      const sportRadarInjuries = await SportRadarService.getInjuryReports();
      const transformedInjuries = DataTransformService.transformInjuries(sportRadarInjuries);

      if (useCache) {
        CacheService.set(cacheKey, transformedInjuries);
      }

      return transformedInjuries;
    } catch (error) {
      console.error('Error fetching injury reports from SportRadar:', error);
      // Return mock injury data as fallback
      return this.getMockInjuryReports();
    }
  }

  // Get upcoming fixtures from SportRadar
  static async getUpcomingFixtures(limit: number = 10, useCache: boolean = true) {
    const cacheKey = `upcoming_fixtures_${limit}`;

    if (useCache) {
      const cached = CacheService.get(cacheKey);
      if (cached) return cached;
    }

    try {
      const sportRadarFixtures = await SportRadarService.getUpcomingFixtures(limit);
      const transformedFixtures = DataTransformService.transformFixtures(sportRadarFixtures);

      if (useCache) {
        CacheService.set(cacheKey, transformedFixtures);
      }

      return transformedFixtures;
    } catch (error) {
      console.error('Error fetching upcoming fixtures from SportRadar:', error);
      return [];
    }
  }

  // Get dashboard statistics
  static async getDashboardStats(useCache: boolean = CACHE_ENABLED) {
    const cacheKey = 'dashboard_stats';

    if (useCache) {
      const cached = CacheService.get(cacheKey);
      if (cached) return cached;
    }

    try {
      const stats = await AnalyticsService.getDashboardStats();
      
      if (useCache) {
        CacheService.set(cacheKey, stats);
      }

      return stats;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return {
        total_players: 581,
        total_teams: 17,
        active_injuries: 12,
        last_updated: new Date().toISOString()
      };
    }
  }

  // Get trade recommendations
  static async getTradeRecommendations(userId: string, useCache: boolean = CACHE_ENABLED) {
    const cacheKey = `trade_recommendations_${userId}`;

    if (useCache) {
      const cached = CacheService.get(cacheKey);
      if (cached) return cached;
    }

    try {
      const recommendations = await TradeService.getTradeRecommendations(userId);
      
      if (useCache) {
        CacheService.set(cacheKey, recommendations);
      }

      return recommendations;
    } catch (error) {
      console.error('Error fetching trade recommendations:', error);
      return this.getMockTradeRecommendations();
    }
  }

  // Execute trade
  static async executeTrade(tradeId: string, userId: string) {
    try {
      const result = await TradeService.executeTrade(tradeId, userId);
      
      // Clear relevant caches
      CacheService.clear();
      
      return result;
    } catch (error) {
      console.error('Error executing trade:', error);
      throw error;
    }
  }

  // Get injury reports
  static async getInjuryReports(useCache: boolean = CACHE_ENABLED) {
    const cacheKey = 'injury_reports';

    if (useCache) {
      const cached = CacheService.get(cacheKey);
      if (cached) return cached;
    }

    try {
      const reports = await InjuryService.getInjuryReports();
      
      if (useCache) {
        CacheService.set(cacheKey, reports);
      }

      return reports;
    } catch (error) {
      console.error('Error fetching injury reports:', error);
      return this.getMockInjuryReports();
    }
  }

  // Get top performers
  static async getTopPerformers(limit: number = 10, useCache: boolean = CACHE_ENABLED) {
    const cacheKey = `top_performers_${limit}`;

    if (useCache) {
      const cached = CacheService.get(cacheKey);
      if (cached) return cached;
    }

    try {
      const players = await PlayerService.getTopPerformers(limit);
      
      if (useCache) {
        CacheService.set(cacheKey, players);
      }

      return players;
    } catch (error) {
      console.error('Error fetching top performers:', error);
      return this.getMockPlayers().slice(0, limit);
    }
  }

  // Get price risers
  static async getPriceRisers(limit: number = 10, useCache: boolean = CACHE_ENABLED) {
    const cacheKey = `price_risers_${limit}`;

    if (useCache) {
      const cached = CacheService.get(cacheKey);
      if (cached) return cached;
    }

    try {
      const players = await PlayerService.getPriceRisers(limit);
      
      if (useCache) {
        CacheService.set(cacheKey, players);
      }

      return players;
    } catch (error) {
      console.error('Error fetching price risers:', error);
      return this.getMockPlayers().slice(0, limit);
    }
  }

  // Mock data for fallback
  private static getMockPlayers() {
    return [
      {
        id: '1',
        name: 'Nathan Cleary',
        position: 'Halfback',
        team: 'Penrith Panthers',
        price: 750000,
        points: 1250,
        form: 8.5,
        ownership: 45.2,
        breakeven: 65,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Kalyn Ponga',
        position: 'Fullback',
        team: 'Newcastle Knights',
        price: 680000,
        points: 1180,
        form: 7.8,
        ownership: 38.7,
        breakeven: 72,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '3',
        name: 'James Tedesco',
        position: 'Fullback',
        team: 'Sydney Roosters',
        price: 720000,
        points: 1320,
        form: 9.2,
        ownership: 52.1,
        breakeven: 58,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '4',
        name: 'Daly Cherry-Evans',
        position: 'Halfback',
        team: 'Manly Sea Eagles',
        price: 650000,
        points: 1150,
        form: 7.5,
        ownership: 28.3,
        breakeven: 68,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '5',
        name: 'Cameron Munster',
        position: 'Five-eighth',
        team: 'Melbourne Storm',
        price: 700000,
        points: 1200,
        form: 8.1,
        ownership: 41.5,
        breakeven: 62,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }

  private static getMockTradeRecommendations() {
    return [
      {
        id: '1',
        user_id: 'demo',
        player_out_id: '1',
        player_in_id: '3',
        reason: 'Superior form and favorable upcoming fixtures make this an optimal trade opportunity.',
        confidence: 87,
        price_change: 50000,
        points_gain: 25,
        risk_level: 'low' as const,
        created_at: new Date().toISOString(),
        player_out: this.getMockPlayers()[0],
        player_in: this.getMockPlayers()[2]
      }
    ];
  }

  private static getMockInjuryReports() {
    return [
      {
        id: '1',
        player_id: '1',
        injury_type: 'Hamstring',
        status: 'Minor concern',
        severity: 'minor' as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        player: this.getMockPlayers()[0]
      }
    ];
  }

  // Health check for API services
  static async healthCheck() {
    const results = {
      supabase: false,
      localApi: false,
      cache: false
    };

    // Check Supabase
    try {
      await AnalyticsService.getDashboardStats();
      results.supabase = true;
    } catch (error) {
      console.error('Supabase health check failed:', error);
    }

    // Check local API
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      results.localApi = response.ok;
    } catch (error) {
      console.error('Local API health check failed:', error);
    }

    // Check cache
    try {
      CacheService.set('health_check', 'ok');
      results.cache = CacheService.get('health_check') === 'ok';
    } catch (error) {
      console.error('Cache health check failed:', error);
    }

    return results;
  }

  // Clear all caches
  static clearCache() {
    CacheService.clear();
  }
}

export default APIService;
