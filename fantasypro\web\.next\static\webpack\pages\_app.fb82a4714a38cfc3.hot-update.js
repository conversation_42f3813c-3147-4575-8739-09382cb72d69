"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/globals.css":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/globals.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_index_js_ruleSet_1_rules_7_oneOf_14_use_1_node_modules_next_dist_build_webpack_loaders_postcss_loader_src_index_js_ruleSet_1_rules_7_oneOf_14_use_2_themes_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! -!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./themes.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/themes.css\");\n// Imports\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n___CSS_LOADER_EXPORT___.i(_node_modules_next_dist_build_webpack_loaders_css_loader_src_index_js_ruleSet_1_rules_7_oneOf_14_use_1_node_modules_next_dist_build_webpack_loaders_postcss_loader_src_index_js_ruleSet_1_rules_7_oneOf_14_use_2_themes_css__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */ /* 3 */\\n  tab-size: 4; /* 3 */\\n  font-family: Inter, system-ui, sans-serif; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: JetBrains Mono, Menlo, Monaco, monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n\\n[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {\\n  appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n\\ninput::placeholder,textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\n::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n\\n::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n\\n::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n\\n::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n\\nselect {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\");\\n  background-position: right 0.5rem center;\\n  background-repeat: no-repeat;\\n  background-size: 1.5em 1.5em;\\n  padding-right: 2.5rem;\\n  print-color-adjust: exact;\\n}\\n\\n[multiple],[size]:where(select:not([size=\\\"1\\\"])) {\\n  background-image: initial;\\n  background-position: initial;\\n  background-repeat: unset;\\n  background-size: initial;\\n  padding-right: 0.75rem;\\n  print-color-adjust: unset;\\n}\\n\\n[type='checkbox'],[type='radio'] {\\n  appearance: none;\\n  padding: 0;\\n  print-color-adjust: exact;\\n  display: inline-block;\\n  vertical-align: middle;\\n  background-origin: border-box;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  flex-shrink: 0;\\n  height: 1rem;\\n  width: 1rem;\\n  color: #2563eb;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='checkbox'] {\\n  border-radius: 0px;\\n}\\n\\n[type='radio'] {\\n  border-radius: 100%;\\n}\\n\\n[type='checkbox']:focus,[type='radio']:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n}\\n\\n[type='checkbox']:checked,[type='radio']:checked {\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n[type='checkbox']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:checked {\\n    appearance: auto;\\n  }\\n}\\n\\n[type='radio']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='radio']:checked {\\n    appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='checkbox']:indeterminate {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e\\\");\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:indeterminate {\\n    appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='file'] {\\n  background: unset;\\n  border-color: inherit;\\n  border-width: 0;\\n  border-radius: 0;\\n  padding: 0;\\n  font-size: unset;\\n  line-height: inherit;\\n}\\n\\n[type='file']:focus {\\n  outline: 1px solid ButtonText;\\n  outline: 1px auto -webkit-focus-ring-color;\\n}\\n  html {\\n    font-family: 'Inter', system-ui, sans-serif;\\n    scroll-behavior: smooth;\\n  }\\n  \\n  body {\\n    background-color: var(--bg-primary);\\n    color: var(--text-primary);\\n    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';\\n    transition: var(--theme-transition);\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n\\n  /* Custom scrollbar */\\n  ::-webkit-scrollbar {\\n    width: 6px;\\n    height: 6px;\\n  }\\n\\n  ::-webkit-scrollbar-track {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n}\\n\\n  ::-webkit-scrollbar-thumb {\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\\n}\\n\\n  ::-webkit-scrollbar-thumb:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));\\n}\\n\\n  /* Firefox scrollbar */\\n  * {\\n    scrollbar-width: thin;\\n    scrollbar-color: #475569 #1e293b;\\n  }\\n.container {\\n  width: 100%;\\n}\\n@media (min-width: 640px) {\\n\\n  .container {\\n    max-width: 640px;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .container {\\n    max-width: 768px;\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .container {\\n    max-width: 1024px;\\n  }\\n}\\n@media (min-width: 1280px) {\\n\\n  .container {\\n    max-width: 1280px;\\n  }\\n}\\n@media (min-width: 1536px) {\\n\\n  .container {\\n    max-width: 1536px;\\n  }\\n}\\n/* Enhanced Card components with theme-aware immersive effects */\\n.card {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    border-radius: 0.5rem;\\n    transition: var(--theme-transition);\\n  }\\n.card-hover {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    border-radius: 0.5rem;\\n    transform: translateY(0);\\n    transition: var(--theme-transition);\\n  }\\n.card-hover:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n/* Immersive card with glow effect */\\n.card-glow {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    border-radius: 0.5rem;\\n    transform: translateY(0);\\n    transition: var(--theme-transition);\\n  }\\n.card-glow:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n.card-glow {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.card-glow::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);\\n    transition: left 0.5s;\\n  }\\n.card-glow:hover::before {\\n    left: 100%;\\n  }\\n/* Premium card with enhanced theme-aware effects */\\n.card-premium {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    border-radius: 0.5rem;\\n    transform: translateY(0);\\n    transition: var(--theme-transition);\\n  }\\n.card-premium:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n.card-premium {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.card-premium::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);\\n    transition: left 0.5s;\\n  }\\n.card-premium:hover::before {\\n    left: 100%;\\n  }\\n.card-premium {\\n  will-change: transform, box-shadow;\\n    background: var(--bg-glass-strong);\\n    backdrop-filter: blur(15px);\\n    border: 1px solid var(--border-secondary);\\n    transition: var(--theme-transition);\\n}\\n.card-premium:hover {\\n    background: var(--bg-glass-strong);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n/* Enhanced Button variants with immersive effects */\\n.btn-primary {\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.btn-primary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.btn-primary {\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  }\\n.btn-primary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 20px rgba(34, 197, 94, 0.3);\\n}\\n.btn-primary:active {\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n.btn-secondary {\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.btn-secondary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.btn-secondary {\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n.btn-secondary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 15px rgba(71, 85, 105, 0.3);\\n}\\n.btn-outline {\\n  border-radius: 0.5rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\\n}\\n.btn-outline:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.btn-outline {\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    background: transparent;\\n  }\\n.btn-outline:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 0 15px rgba(34, 197, 94, 0.2);\\n}\\n.btn-danger {\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.btn-danger:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.btn-danger {\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n.btn-danger:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 20px rgba(239, 68, 68, 0.3);\\n}\\n/* Button ripple effect */\\n.btn-ripple {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.btn-ripple::before {\\n    content: '';\\n    position: absolute;\\n    top: 50%;\\n    left: 50%;\\n    width: 0;\\n    height: 0;\\n    border-radius: 50%;\\n    background: rgba(255, 255, 255, 0.3);\\n    transform: translate(-50%, -50%);\\n    transition: width 0.3s, height 0.3s;\\n  }\\n.btn-ripple:active::before {\\n    width: 300px;\\n    height: 300px;\\n  }\\n/* Enhanced Input styles with immersive effects */\\n.input-primary {\\n  border-radius: 0.5rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.input-primary::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));\\n}\\n.input-primary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.input-primary {\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    position: relative;\\n  }\\n.input-primary:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1),\\n                0 0 20px rgba(34, 197, 94, 0.1);\\n    transform: translateY(-1px);\\n}\\n.input-primary:hover:not(:focus) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n}\\n/* Floating label input */\\n/* Enhanced Status indicators with glow effects */\\n.status-warning {\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  border-width: 1px;\\n  border-color: rgb(234 179 8 / 0.2);\\n  background-color: rgb(113 63 18 / 0.2);\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n    position: relative;\\n    overflow: hidden;\\n}\\n/* Live indicator with pulse */\\n.status-live {\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  border-width: 1px;\\n  border-color: rgb(34 197 94 / 0.2);\\n  background-color: rgb(20 83 45 / 0.2);\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n    position: relative;\\n    overflow: hidden;\\n}\\n.status-live::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background: rgba(34, 197, 94, 0.1);\\n    border-radius: inherit;\\n    animation: glowPulseGreen 2s ease-in-out infinite alternate;\\n  }\\n.status-live::after {\\n    content: '';\\n    position: absolute;\\n    top: 50%;\\n    left: 0.5rem;\\n    width: 6px;\\n    height: 6px;\\n    background: rgb(34, 197, 94);\\n    border-radius: 50%;\\n    transform: translateY(-50%);\\n    animation: pulse 1.5s ease-in-out infinite;\\n  }\\n/* Premium status indicator */\\n.status-premium {\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(79, 70, 229, 0.2));\\n    color: rgb(196, 181, 253);\\n    border: 1px solid rgba(147, 51, 234, 0.3);\\n    box-shadow: 0 0 15px rgba(147, 51, 234, 0.2);\\n}\\n/* Risk level indicators */\\n/* Loading states */\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.loading-spinner {\\n  animation: spin 1s linear infinite;\\n  border-radius: 9999px;\\n  border-width: 2px;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n  --tw-border-opacity: 1;\\n  border-top-color: rgb(74 222 128 / var(--tw-border-opacity, 1));\\n}\\n/* Data visualization */\\n/* Navigation */\\n.nav-link-active {\\n  display: flex;\\n  align-items: center;\\n  border-radius: 0.5rem;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n  border-width: 1px;\\n  border-color: rgb(34 197 94 / 0.2);\\n  background-color: rgb(20 83 45 / 0.2);\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.nav-link-inactive {\\n  display: flex;\\n  align-items: center;\\n  border-radius: 0.5rem;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n  --tw-text-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\\n}\\n.nav-link-inactive:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n/* Enhanced Tables with immersive effects */\\n/* Interactive table row with glow */\\n/* Theme-aware text classes */\\n.themed-text-primary {\\n    color: var(--text-primary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-text-secondary {\\n    color: var(--text-secondary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-text-tertiary {\\n    color: var(--text-tertiary);\\n    transition: var(--theme-transition);\\n  }\\n/* Theme-aware border classes */\\n.themed-border {\\n    border-color: var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-border-b {\\n    border-bottom: 1px solid var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-border-t {\\n    border-top: 1px solid var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n/* Theme-aware card classes */\\n.themed-card-secondary {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-secondary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-card-secondary:hover {\\n    background-color: var(--bg-tertiary);\\n    border-color: var(--border-primary);\\n  }\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\n.fixed {\\n  position: fixed;\\n}\\n.absolute {\\n  position: absolute;\\n}\\n.relative {\\n  position: relative;\\n}\\n.sticky {\\n  position: sticky;\\n}\\n.inset-0 {\\n  inset: 0px;\\n}\\n.inset-y-0 {\\n  top: 0px;\\n  bottom: 0px;\\n}\\n.left-0 {\\n  left: 0px;\\n}\\n.right-0 {\\n  right: 0px;\\n}\\n.right-8 {\\n  right: 2rem;\\n}\\n.top-0 {\\n  top: 0px;\\n}\\n.z-10 {\\n  z-index: 10;\\n}\\n.z-20 {\\n  z-index: 20;\\n}\\n.z-40 {\\n  z-index: 40;\\n}\\n.z-50 {\\n  z-index: 50;\\n}\\n.z-\\\\[9999\\\\] {\\n  z-index: 9999;\\n}\\n.-m-2\\\\.5 {\\n  margin: -0.625rem;\\n}\\n.-mx-2 {\\n  margin-left: -0.5rem;\\n  margin-right: -0.5rem;\\n}\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\n.ml-auto {\\n  margin-left: auto;\\n}\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\n.mt-auto {\\n  margin-top: auto;\\n}\\n.line-clamp-2 {\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 2;\\n}\\n.block {\\n  display: block;\\n}\\n.inline-block {\\n  display: inline-block;\\n}\\n.inline {\\n  display: inline;\\n}\\n.flex {\\n  display: flex;\\n}\\n.inline-flex {\\n  display: inline-flex;\\n}\\n.table {\\n  display: table;\\n}\\n.grid {\\n  display: grid;\\n}\\n.hidden {\\n  display: none;\\n}\\n.h-10 {\\n  height: 2.5rem;\\n}\\n.h-12 {\\n  height: 3rem;\\n}\\n.h-16 {\\n  height: 4rem;\\n}\\n.h-2 {\\n  height: 0.5rem;\\n}\\n.h-3 {\\n  height: 0.75rem;\\n}\\n.h-32 {\\n  height: 8rem;\\n}\\n.h-4 {\\n  height: 1rem;\\n}\\n.h-5 {\\n  height: 1.25rem;\\n}\\n.h-6 {\\n  height: 1.5rem;\\n}\\n.h-64 {\\n  height: 16rem;\\n}\\n.h-8 {\\n  height: 2rem;\\n}\\n.h-full {\\n  height: 100%;\\n}\\n.max-h-32 {\\n  max-height: 8rem;\\n}\\n.max-h-64 {\\n  max-height: 16rem;\\n}\\n.max-h-80 {\\n  max-height: 20rem;\\n}\\n.max-h-96 {\\n  max-height: 24rem;\\n}\\n.max-h-\\\\[90vh\\\\] {\\n  max-height: 90vh;\\n}\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\n.w-10 {\\n  width: 2.5rem;\\n}\\n.w-11 {\\n  width: 2.75rem;\\n}\\n.w-12 {\\n  width: 3rem;\\n}\\n.w-16 {\\n  width: 4rem;\\n}\\n.w-2 {\\n  width: 0.5rem;\\n}\\n.w-24 {\\n  width: 6rem;\\n}\\n.w-3 {\\n  width: 0.75rem;\\n}\\n.w-4 {\\n  width: 1rem;\\n}\\n.w-48 {\\n  width: 12rem;\\n}\\n.w-5 {\\n  width: 1.25rem;\\n}\\n.w-6 {\\n  width: 1.5rem;\\n}\\n.w-64 {\\n  width: 16rem;\\n}\\n.w-8 {\\n  width: 2rem;\\n}\\n.w-full {\\n  width: 100%;\\n}\\n.w-px {\\n  width: 1px;\\n}\\n.min-w-0 {\\n  min-width: 0px;\\n}\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\n.max-w-3xl {\\n  max-width: 48rem;\\n}\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\n.max-w-md {\\n  max-width: 28rem;\\n}\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.shrink-0 {\\n  flex-shrink: 0;\\n}\\n.grow {\\n  flex-grow: 1;\\n}\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\n.resize {\\n  resize: both;\\n}\\n.list-inside {\\n  list-style-position: inside;\\n}\\n.list-disc {\\n  list-style-type: disc;\\n}\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.flex-col {\\n  flex-direction: column;\\n}\\n.items-start {\\n  align-items: flex-start;\\n}\\n.items-end {\\n  align-items: flex-end;\\n}\\n.items-center {\\n  align-items: center;\\n}\\n.justify-end {\\n  justify-content: flex-end;\\n}\\n.justify-center {\\n  justify-content: center;\\n}\\n.justify-between {\\n  justify-content: space-between;\\n}\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\n.gap-4 {\\n  gap: 1rem;\\n}\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\n.gap-8 {\\n  gap: 2rem;\\n}\\n.gap-x-4 {\\n  column-gap: 1rem;\\n}\\n.gap-y-5 {\\n  row-gap: 1.25rem;\\n}\\n.gap-y-7 {\\n  row-gap: 1.75rem;\\n}\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-5 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.self-start {\\n  align-self: flex-start;\\n}\\n.self-stretch {\\n  align-self: stretch;\\n}\\n.overflow-auto {\\n  overflow: auto;\\n}\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.whitespace-pre-wrap {\\n  white-space: pre-wrap;\\n}\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\n.rounded-t {\\n  border-top-left-radius: 0.25rem;\\n  border-top-right-radius: 0.25rem;\\n}\\n.border {\\n  border-width: 1px;\\n}\\n.border-2 {\\n  border-width: 2px;\\n}\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\n.border-l-4 {\\n  border-left-width: 4px;\\n}\\n.border-r {\\n  border-right-width: 1px;\\n}\\n.border-t {\\n  border-top-width: 1px;\\n}\\n.border-blue-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-500\\\\/20 {\\n  border-color: rgb(59 130 246 / 0.2);\\n}\\n.border-blue-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-600\\\\/30 {\\n  border-color: rgb(37 99 235 / 0.3);\\n}\\n.border-green-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));\\n}\\n.border-green-500\\\\/20 {\\n  border-color: rgb(34 197 94 / 0.2);\\n}\\n.border-orange-500\\\\/20 {\\n  border-color: rgb(249 115 22 / 0.2);\\n}\\n.border-red-500\\\\/20 {\\n  border-color: rgb(239 68 68 / 0.2);\\n}\\n.border-slate-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n}\\n.border-slate-700 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));\\n}\\n.border-yellow-500\\\\/20 {\\n  border-color: rgb(234 179 8 / 0.2);\\n}\\n.border-yellow-500\\\\/30 {\\n  border-color: rgb(234 179 8 / 0.3);\\n}\\n.border-t-transparent {\\n  border-top-color: transparent;\\n}\\n.bg-black\\\\/50 {\\n  background-color: rgb(0 0 0 / 0.5);\\n}\\n.bg-blue-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-400\\\\/10 {\\n  background-color: rgb(96 165 250 / 0.1);\\n}\\n.bg-blue-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500\\\\/10 {\\n  background-color: rgb(59 130 246 / 0.1);\\n}\\n.bg-blue-500\\\\/20 {\\n  background-color: rgb(59 130 246 / 0.2);\\n}\\n.bg-blue-600\\\\/20 {\\n  background-color: rgb(37 99 235 / 0.2);\\n}\\n.bg-blue-900\\\\/20 {\\n  background-color: rgb(30 58 138 / 0.2);\\n}\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500\\\\/20 {\\n  background-color: rgb(107 114 128 / 0.2);\\n}\\n.bg-gray-900\\\\/20 {\\n  background-color: rgb(17 24 39 / 0.2);\\n}\\n.bg-green-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-400\\\\/10 {\\n  background-color: rgb(74 222 128 / 0.1);\\n}\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500\\\\/10 {\\n  background-color: rgb(34 197 94 / 0.1);\\n}\\n.bg-green-500\\\\/20 {\\n  background-color: rgb(34 197 94 / 0.2);\\n}\\n.bg-green-600\\\\/20 {\\n  background-color: rgb(22 163 74 / 0.2);\\n}\\n.bg-green-900\\\\/20 {\\n  background-color: rgb(20 83 45 / 0.2);\\n}\\n.bg-orange-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(251 146 60 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-500\\\\/10 {\\n  background-color: rgb(249 115 22 / 0.1);\\n}\\n.bg-orange-500\\\\/20 {\\n  background-color: rgb(249 115 22 / 0.2);\\n}\\n.bg-orange-600\\\\/20 {\\n  background-color: rgb(234 88 12 / 0.2);\\n}\\n.bg-orange-900\\\\/20 {\\n  background-color: rgb(124 45 18 / 0.2);\\n}\\n.bg-pink-500\\\\/20 {\\n  background-color: rgb(236 72 153 / 0.2);\\n}\\n.bg-purple-500\\\\/10 {\\n  background-color: rgb(168 85 247 / 0.1);\\n}\\n.bg-purple-500\\\\/20 {\\n  background-color: rgb(168 85 247 / 0.2);\\n}\\n.bg-purple-600\\\\/20 {\\n  background-color: rgb(147 51 234 / 0.2);\\n}\\n.bg-red-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-400\\\\/10 {\\n  background-color: rgb(248 113 113 / 0.1);\\n}\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500\\\\/10 {\\n  background-color: rgb(239 68 68 / 0.1);\\n}\\n.bg-red-500\\\\/20 {\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\n.bg-red-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-600\\\\/20 {\\n  background-color: rgb(220 38 38 / 0.2);\\n}\\n.bg-red-900\\\\/20 {\\n  background-color: rgb(127 29 29 / 0.2);\\n}\\n.bg-red-900\\\\/40 {\\n  background-color: rgb(127 29 29 / 0.4);\\n}\\n.bg-slate-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-600\\\\/20 {\\n  background-color: rgb(71 85 105 / 0.2);\\n}\\n.bg-slate-700 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-700\\\\/30 {\\n  background-color: rgb(51 65 85 / 0.3);\\n}\\n.bg-slate-800 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-800\\\\/50 {\\n  background-color: rgb(30 41 59 / 0.5);\\n}\\n.bg-slate-900 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-900\\\\/20 {\\n  background-color: rgb(15 23 42 / 0.2);\\n}\\n.bg-yellow-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-400\\\\/10 {\\n  background-color: rgb(250 204 21 / 0.1);\\n}\\n.bg-yellow-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500\\\\/10 {\\n  background-color: rgb(234 179 8 / 0.1);\\n}\\n.bg-yellow-500\\\\/20 {\\n  background-color: rgb(234 179 8 / 0.2);\\n}\\n.bg-yellow-900\\\\/20 {\\n  background-color: rgb(113 63 18 / 0.2);\\n}\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-yellow-500\\\\/20 {\\n  --tw-gradient-from: rgb(234 179 8 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.to-orange-500\\\\/20 {\\n  --tw-gradient-to: rgb(249 115 22 / 0.2) var(--tw-gradient-to-position);\\n}\\n.p-2 {\\n  padding: 0.5rem;\\n}\\n.p-2\\\\.5 {\\n  padding: 0.625rem;\\n}\\n.p-3 {\\n  padding: 0.75rem;\\n}\\n.p-4 {\\n  padding: 1rem;\\n}\\n.p-5 {\\n  padding: 1.25rem;\\n}\\n.p-6 {\\n  padding: 1.5rem;\\n}\\n.p-8 {\\n  padding: 2rem;\\n}\\n.px-1 {\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-5 {\\n  padding-left: 1.25rem;\\n  padding-right: 1.25rem;\\n}\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-2\\\\.5 {\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n}\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n.pb-4 {\\n  padding-bottom: 1rem;\\n}\\n.pl-10 {\\n  padding-left: 2.5rem;\\n}\\n.pl-3 {\\n  padding-left: 0.75rem;\\n}\\n.pr-10 {\\n  padding-right: 2.5rem;\\n}\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\n.pt-3 {\\n  padding-top: 0.75rem;\\n}\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\n.text-left {\\n  text-align: left;\\n}\\n.text-center {\\n  text-align: center;\\n}\\n.text-right {\\n  text-align: right;\\n}\\n.font-mono {\\n  font-family: JetBrains Mono, Menlo, Monaco, monospace;\\n}\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.text-base {\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold {\\n  font-weight: 700;\\n}\\n.font-medium {\\n  font-weight: 500;\\n}\\n.font-semibold {\\n  font-weight: 600;\\n}\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\n.text-black {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-green-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-green-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\\n}\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(129 140 248 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\\n}\\n.text-pink-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(244 114 182 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\\n}\\n.text-red-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\\n}\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\\n}\\n.opacity-0 {\\n  opacity: 0;\\n}\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\n.shadow-glow-blue {\\n  --tw-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\n  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-glow-orange {\\n  --tw-shadow: 0 0 20px rgba(251, 191, 36, 0.3);\\n  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-150 {\\n  transition-duration: 150ms;\\n}\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\n.duration-500 {\\n  transition-duration: 500ms;\\n}\\n.ease-in-out {\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n/* Glass morphism effect */\\n/* Gradient text */\\n/* Enhanced Custom shadows and glow effects */\\n.shadow-glow-green {\\n    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3),\\n                0 0 40px rgba(34, 197, 94, 0.1);\\n  }\\n.shadow-glow-blue {\\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3),\\n                0 0 40px rgba(59, 130, 246, 0.1);\\n  }\\n.shadow-glow-orange {\\n    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3),\\n                0 0 40px rgba(251, 191, 36, 0.1);\\n  }\\n.shadow-glow-red {\\n    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3),\\n                0 0 40px rgba(239, 68, 68, 0.1);\\n  }\\n.shadow-glow-purple {\\n    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3),\\n                0 0 40px rgba(147, 51, 234, 0.1);\\n  }\\n/* Pulsing glow effect */\\n.glow-pulse-green {\\n    animation: glowPulseGreen 2s ease-in-out infinite alternate;\\n  }\\n.glow-pulse-blue {\\n    animation: glowPulseBlue 2s ease-in-out infinite alternate;\\n  }\\n/* Floating effect */\\n.float {\\n    animation: float 3s ease-in-out infinite;\\n  }\\n.float-delayed {\\n    animation: float 3s ease-in-out infinite;\\n    animation-delay: 1s;\\n  }\\n/* Shimmer effect */\\n.shimmer {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.shimmer::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);\\n    animation: shimmer 2s infinite;\\n  }\\n/* Magnetic hover effect */\\n.magnetic {\\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n.magnetic:hover {\\n    transform: scale(1.02);\\n  }\\n/* Tilt effect */\\n.tilt {\\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n.tilt:hover {\\n    transform: perspective(1000px) rotateX(5deg) rotateY(5deg);\\n  }\\n/* Enhanced Animation utilities */\\n.animate-slide-in-left {\\n    animation: slideInLeft 0.4s ease-out;\\n  }\\n.animate-slide-in-right {\\n    animation: slideInRight 0.4s ease-out;\\n  }\\n/* Staggered animations */\\n.animate-stagger-1 { animation-delay: 0.1s; }\\n.animate-stagger-2 { animation-delay: 0.2s; }\\n.animate-stagger-3 { animation-delay: 0.3s; }\\n/* Hide scrollbar but keep functionality */\\n/* Custom focus styles */\\n\\n/* Import theme system */\\n\\n/* Import Inter font */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');\\n@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');\\n\\n/* Base styles */\\n\\n/* Component styles */\\n\\n/* Utility styles */\\n\\n/* Enhanced Custom animations */\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideDown {\\n  from {\\n    transform: translateY(-10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideInLeft {\\n  from {\\n    transform: translateX(-20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideInRight {\\n  from {\\n    transform: translateX(20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes bounceIn {\\n  0% {\\n    transform: scale(0.3);\\n    opacity: 0;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.8;\\n  }\\n  70% {\\n    transform: scale(0.9);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes scaleIn {\\n  from {\\n    transform: scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes rotateIn {\\n  from {\\n    transform: rotate(-10deg) scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: rotate(0deg) scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n\\n@keyframes glowPulse {\\n  0% {\\n    box-shadow: 0 0 5px rgba(255, 255, 255, 0.1);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3), 0 0 30px rgba(255, 255, 255, 0.1);\\n  }\\n}\\n\\n@keyframes glowPulseGreen {\\n  0% {\\n    box-shadow: 0 0 5px rgba(34, 197, 94, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(34, 197, 94, 0.4), 0 0 30px rgba(34, 197, 94, 0.2);\\n  }\\n}\\n\\n@keyframes glowPulseBlue {\\n  0% {\\n    box-shadow: 0 0 5px rgba(59, 130, 246, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 0 30px rgba(59, 130, 246, 0.2);\\n  }\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n  \\n  body {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n    --tw-text-opacity: 1;\\n    color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n  }\\n}\\n\\\\n\\\\n\\n/* Performance optimizations */\\n* {\\n  /* Use hardware acceleration for transforms */\\n  transform: translateZ(0);\\n  backface-visibility: hidden;\\n  perspective: 1000px;\\n}\\n\\n/* Optimize animations */\\n@media (prefers-reduced-motion: reduce) {\\n  *,\\n  *::before,\\n  *::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n    scroll-behavior: auto !important;\\n  }\\n}\\n\\n/* Optimize repaints */\\n.card-premium,\\n.card-glow,\\n.magnetic {\\n  will-change: transform, box-shadow;\\n}\\n\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline,\\n.btn-danger {\\n  will-change: transform, box-shadow, background-color;\\n}\\n\\n/* Optimize scrolling */\\n.scrollable {\\n  -webkit-overflow-scrolling: touch;\\n  overflow-scrolling: touch;\\n}\\n\\n/* Optimize text rendering */\\nbody {\\n  text-rendering: optimizeSpeed;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n/* Optimize images */\\nimg {\\n  content-visibility: auto;\\n  contain-intrinsic-size: 300px 200px;\\n}\\n\\n/* Critical CSS inlining */\\n.above-fold {\\n  contain: layout style paint;\\n}\\n.hover\\\\:themed-text-primary:hover {\\n    color: var(--text-primary);\\n    transition: var(--theme-transition);\\n  }\\n.after\\\\:absolute::after {\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\n.after\\\\:left-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 2px;\\n}\\n.after\\\\:top-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  top: 2px;\\n}\\n.after\\\\:h-5::after {\\n  content: var(--tw-content);\\n  height: 1.25rem;\\n}\\n.after\\\\:w-5::after {\\n  content: var(--tw-content);\\n  width: 1.25rem;\\n}\\n.after\\\\:rounded-full::after {\\n  content: var(--tw-content);\\n  border-radius: 9999px;\\n}\\n.after\\\\:border::after {\\n  content: var(--tw-content);\\n  border-width: 1px;\\n}\\n.after\\\\:border-gray-300::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.after\\\\:bg-white::after {\\n  content: var(--tw-content);\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.after\\\\:transition-all::after {\\n  content: var(--tw-content);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.after\\\\:content-\\\\[\\\\'\\\\'\\\\]::after {\\n  --tw-content: '';\\n  content: var(--tw-content);\\n}\\n.last\\\\:border-b-0:last-child {\\n  border-bottom-width: 0px;\\n}\\n.hover\\\\:border-slate-500:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:bg-blue-600\\\\/30:hover {\\n  background-color: rgb(37 99 235 / 0.3);\\n}\\n.hover\\\\:bg-green-600\\\\/30:hover {\\n  background-color: rgb(22 163 74 / 0.3);\\n}\\n.hover\\\\:bg-orange-600\\\\/30:hover {\\n  background-color: rgb(234 88 12 / 0.3);\\n}\\n.hover\\\\:bg-purple-600\\\\/30:hover {\\n  background-color: rgb(147 51 234 / 0.3);\\n}\\n.hover\\\\:bg-slate-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-slate-700\\\\/50:hover {\\n  background-color: rgb(51 65 85 / 0.5);\\n}\\n.hover\\\\:bg-yellow-500\\\\/30:hover {\\n  background-color: rgb(234 179 8 / 0.3);\\n}\\n.hover\\\\:bg-opacity-80:hover {\\n  --tw-bg-opacity: 0.8;\\n}\\n.hover\\\\:text-blue-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-blue-400:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-green-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-red-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-white:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-yellow-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\\n}\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.focus\\\\:ring-green-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\n.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\n.peer:checked ~ .peer-checked\\\\:bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:translate-x-full::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:border-white::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\n.peer:focus ~ .peer-focus\\\\:outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-4 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-blue-300 {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));\\n}\\n.dark\\\\:border-gray-600:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.dark\\\\:bg-gray-700:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n.peer:focus ~ .dark\\\\:peer-focus\\\\:ring-blue-800:is(.dark *) {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity, 1));\\n}\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .sm\\\\:justify-between {\\n    justify-content: space-between;\\n  }\\n\\n  .sm\\\\:gap-x-6 {\\n    column-gap: 1.5rem;\\n  }\\n\\n  .sm\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\n\\n  .sm\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-5 {\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:fixed {\\n    position: fixed;\\n  }\\n\\n  .lg\\\\:inset-y-0 {\\n    top: 0px;\\n    bottom: 0px;\\n  }\\n\\n  .lg\\\\:z-50 {\\n    z-index: 50;\\n  }\\n\\n  .lg\\\\:col-span-1 {\\n    grid-column: span 1 / span 1;\\n  }\\n\\n  .lg\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:col-span-3 {\\n    grid-column: span 3 / span 3;\\n  }\\n\\n  .lg\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .lg\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .lg\\\\:w-64 {\\n    width: 16rem;\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-6 {\\n    grid-template-columns: repeat(6, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .lg\\\\:flex-col {\\n    flex-direction: column;\\n  }\\n\\n  .lg\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .lg\\\\:justify-between {\\n    justify-content: space-between;\\n  }\\n\\n  .lg\\\\:gap-x-6 {\\n    column-gap: 1.5rem;\\n  }\\n\\n  .lg\\\\:space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n\\n  .lg\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\n\\n  .lg\\\\:px-8 {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n\\n  .lg\\\\:pl-64 {\\n    padding-left: 16rem;\\n  }\\n}\\n@media (min-width: 1280px) {\\n\\n  .xl\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc,EAAd,MAAc;EAAd,WAAc,EAAd,MAAc;EAAd,yCAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,qDAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;;AAAd;EAAA,gBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,mBAAc;EAAd,sBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,mBAAc;EAAd,sBAAc;AAAA;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd,iFAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,mPAAc;EAAd,wCAAc;EAAd,4BAAc;EAAd,4BAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,wBAAc;EAAd,sBAAc;EAAd;AAAc;;AAAd;EAAA,gBAAc;EAAd,UAAc;EAAd,yBAAc;EAAd,qBAAc;EAAd,sBAAc;EAAd,6BAAc;EAAd,yBAAc;UAAd,iBAAc;EAAd,cAAc;EAAd,YAAc;EAAd,WAAc;EAAd,cAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd;AAAc;;AAAd;EAAA,sQAAc;AAAA;;AAAd;;EAAA;IAAA;EAAc;AAAA;;AAAd;EAAA,oKAAc;AAAA;;AAAd;;EAAA;IAAA;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,uOAAc;EAAd,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,4BAAc;AAAA;;AAAd;;EAAA;IAAA;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,iBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,gBAAc;EAAd,UAAc;EAAd,gBAAc;EAAd;AAAc;;AAAd;EAAA,6BAAc;EAAd;AAAc;EAAd;IAAA,2CAAc;IAAd,uBAAc;EAAA;;EAAd;IAAA,mCAAc;IAAd,0BAAc;IAAd,qDAAc;IAAd,mCAAc;IAAd,mCAAc;IAAd,kCAAc;EAAA;;EAAd,qBAAc;EAAd;IAAA,UAAc;IAAd,WAAc;EAAA;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,qBAAc;EAAd,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd,sBAAc;EAAd;IAAA,qBAAc;IAAd,gCAAc;EAAA;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAoDlB,gEAAgE;AAChE;IACE,qCAAqC;IACrC,uCAAuC;IACvC,4BAA4B;IAC5B,qBAAiB;IACjB,mCAAmC;EACrC;AAGE;IAAA,qCAAW;IAAX,uCAAW;IAAX,4BAAW;IAAX,qBAAW;IACX,wBAAwB;IACxB,mCAAmC;EAFxB;AAKb;IACE,2BAA2B;IAC3B,iDAAiD;IACjD,kCAAkC;EACpC;AAEA,oCAAoC;AAElC;IAAA,qCAAiB;IAAjB,uCAAiB;IAAjB,4BAAiB;IAAjB,qBAAiB;IAAjB,wBAAiB;IAAjB,mCAAiB;EAAA;AAAjB;IAAA,2BAAiB;IAAjB,iDAAiB;IAAjB,kCAAiB;EAAA;AADnB;IAEE,kBAAkB;IAClB,gBAAgB;EAClB;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,MAAM;IACN,WAAW;IACX,WAAW;IACX,YAAY;IACZ,oFAAoF;IACpF,qBAAqB;EACvB;AAEA;IACE,UAAU;EACZ;AAEA,mDAAmD;AAEjD;IAAA,qCAAgB;IAAhB,uCAAgB;IAAhB,4BAAgB;IAAhB,qBAAgB;IAAhB,wBAAgB;IAAhB,mCAAgB;EAAA;AAAhB;IAAA,2BAAgB;IAAhB,iDAAgB;IAAhB,kCAAgB;EAAA;AAAhB;IAAA,kBAAgB;IAAhB,gBAAgB;EAAA;AAAhB;IAAA,WAAgB;IAAhB,kBAAgB;IAAhB,MAAgB;IAAhB,WAAgB;IAAhB,WAAgB;IAAhB,YAAgB;IAAhB,oFAAgB;IAAhB,qBAAgB;EAAA;AAAhB;IAAA,UAAgB;EAAA;AAAhB;EAAA,kCAAgB;IAChB,kCAAkC;IAClC,2BAA2B;IAC3B,yCAAyC;IACzC,mCAAmC;AAJnB;AAOlB;IACE,kCAAkC;IAClC,iDAAiD;IACjD,kCAAkC;EACpC;AAEA,oDAAoD;AAElD;EAAA,qBAAkF;EAAlF,kBAAkF;EAAlF,0DAAkF;EAAlF,kBAAkF;EAAlF,mBAAkF;EAAlF,mBAAkF;EAAlF,sBAAkF;EAAlF,gBAAkF;EAAlF,oBAAkF;EAAlF;AAAkF;AAAlF;EAAA,8BAAkF;EAAlF;AAAkF;AADpF;IAEE,kBAAkB;IAClB,gBAAgB;IAChB,iDAAiD;IACjD,wBAAwB;IACxB,iFAAiF;EACnF;AAGE;EAAA,kBAAmB;EAAnB,0DAAmB;IACnB,2BAA2B;IAC3B;;;AAFmB;AAOrB;IACE,wBAAwB;IACxB,6CAA6C;EAC/C;AAGE;EAAA,qBAAkF;EAAlF,kBAAkF;EAAlF,yDAAkF;EAAlF,kBAAkF;EAAlF,mBAAkF;EAAlF,mBAAkF;EAAlF,sBAAkF;EAAlF,gBAAkF;EAAlF,oBAAkF;EAAlF;AAAkF;AAAlF;EAAA,8BAAkF;EAAlF;AAAkF;AADpF;IAEE,kBAAkB;IAClB,gBAAgB;IAChB,iDAAiD;IACjD,wBAAwB;IACxB,6CAA6C;EAC/C;AAGE;EAAA,kBAAmB;EAAnB,0DAAmB;IACnB,2BAA2B;IAC3B;;;AAFmB;AAQnB;EAAA,qBAAiG;EAAjG,iBAAiG;EAAjG,sBAAiG;EAAjG,0DAAiG;EAAjG,kBAAiG;EAAjG,mBAAiG;EAAjG,mBAAiG;EAAjG,sBAAiG;EAAjG,gBAAiG;EAAjG,oBAAiG;EAAjG;AAAiG;AAAjG;EAAA,8BAAiG;EAAjG;AAAiG;AADnG;IAEE,kBAAkB;IAClB,gBAAgB;IAChB,iDAAiD;IACjD,wBAAwB;IACxB,uBAAuB;EACzB;AAGE;EAAA,sBAA+C;EAA/C,0DAA+C;EAA/C,kBAA+C;EAA/C,yDAA+C;EAA/C,oBAA+C;EAA/C,mDAA+C;IAC/C,2BAA2B;IAC3B;;AAF+C;AAO/C;EAAA,qBAAgF;EAAhF,kBAAgF;EAAhF,0DAAgF;EAAhF,kBAAgF;EAAhF,mBAAgF;EAAhF,mBAAgF;EAAhF,sBAAgF;EAAhF,gBAAgF;EAAhF,oBAAgF;EAAhF;AAAgF;AAAhF;EAAA,8BAAgF;EAAhF;AAAgF;AADlF;IAEE,kBAAkB;IAClB,gBAAgB;IAChB,iDAAiD;IACjD,wBAAwB;IACxB,6CAA6C;EAC/C;AAGE;EAAA,kBAAiB;EAAjB,0DAAiB;IACjB,2BAA2B;IAC3B;;;AAFiB;AAOnB,yBAAyB;AACzB;IACE,kBAAkB;IAClB,gBAAgB;EAClB;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,SAAS;IACT,kBAAkB;IAClB,oCAAoC;IACpC,gCAAgC;IAChC,mCAAmC;EACrC;AAEA;IACE,YAAY;IACZ,aAAa;EACf;AAEA,iDAAiD;AAE/C;EAAA,qBAAoH;EAApH,iBAAoH;EAApH,sBAAoH;EAApH,0DAAoH;EAApH,kBAAoH;EAApH,yDAAoH;EAApH,qBAAoH;EAApH,sBAAoH;EAApH,mBAAoH;EAApH,sBAAoH;EAApH,oBAAoH;EAApH;AAAoH;AAApH;EAAA,2BAAoH;EAApH;AAAoH;AAApH;EAAA,8BAAoH;EAApH;AAAoH;AADtH;IAEE,iDAAiD;IACjD,kBAAkB;EACpB;AAGE;EAAA,sBAAoC;EAApC,0DAAoC;EAApC,kBAAoC;EAApC,yDAAoC;IACpC;+CAC2C;IAC3C;AAHoC;AAOpC;EAAA,sBAAuB;EAAvB,4DAAuB;IACvB;AADuB;AAIzB,yBAAyB;AAwBzB,iDAAiD;AA0B/C;EAAA,oBAA2I;EAA3I,mBAA2I;EAA3I,qBAA2I;EAA3I,iBAA2I;EAA3I,kCAA2I;EAA3I,sCAA2I;EAA3I,sBAA2I;EAA3I,uBAA2I;EAA3I,qBAA2I;EAA3I,wBAA2I;EAA3I,kBAA2I;EAA3I,iBAA2I;EAA3I,gBAA2I;EAA3I,oBAA2I;EAA3I,kDAA2I;IAC3I,kBAAkB;IAClB;AAF2I;AAK7I,8BAA8B;AAE5B;EAAA,oBAAoB;EAApB,mBAAoB;EAApB,qBAAoB;EAApB,iBAAoB;EAApB,kCAAoB;EAApB,qCAAoB;EAApB,sBAAoB;EAApB,uBAAoB;EAApB,qBAAoB;EAApB,wBAAoB;EAApB,kBAAoB;EAApB,iBAAoB;EAApB,gBAAoB;EAApB,oBAAoB;EAApB,kDAAoB;IAApB,kBAAoB;IAApB;AAAoB;AAApB;IAAA,WAAoB;IAApB,kBAAoB;IAApB,MAAoB;IAApB,OAAoB;IAApB,QAAoB;IAApB,SAAoB;IAApB,kCAAoB;IAApB,sBAAoB;IAApB,2DAAoB;EAAA;AAGtB;IACE,WAAW;IACX,kBAAkB;IAClB,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,WAAW;IACX,4BAA4B;IAC5B,kBAAkB;IAClB,2BAA2B;IAC3B,0CAA0C;EAC5C;AAEA,6BAA6B;AAE3B;EAAA,oBAA0E;EAA1E,mBAA0E;EAA1E,qBAA0E;EAA1E,qBAA0E;EAA1E,sBAA0E;EAA1E,oBAA0E;EAA1E,uBAA0E;EAA1E,kBAA0E;EAA1E,iBAA0E;EAA1E,gBAA0E;IAC1E,oFAAoF;IACpF,yBAAyB;IACzB,yCAAyC;IACzC;AAJ0E;AAO5E,0BAA0B;AAa1B,mBAAmB;AAMjB;;EAAA;IAAA;EAA6E;AAAA;AAA7E;EAAA,kCAA6E;EAA7E,qBAA6E;EAA7E,iBAA6E;EAA7E,0DAA6E;EAA7E,sBAA6E;EAA7E;AAA6E;AAG/E,uBAAuB;AAKvB,eAAe;AAMb;EAAA,aAAyE;EAAzE,mBAAyE;EAAzE,qBAAyE;EAAzE,qBAAyE;EAAzE,sBAAyE;EAAzE,mBAAyE;EAAzE,sBAAyE;EAAzE,mBAAyE;EAAzE,oBAAyE;EAAzE,gBAAyE;EAAzE,+FAAyE;EAAzE,wDAAyE;EAAzE,0BAAyE;EAAzE,iBAAyE;EAAzE,kCAAyE;EAAzE,qCAAyE;EAAzE,oBAAyE;EAAzE;AAAyE;AAIzE;EAAA,aAAkE;EAAlE,mBAAkE;EAAlE,qBAAkE;EAAlE,qBAAkE;EAAlE,sBAAkE;EAAlE,mBAAkE;EAAlE,sBAAkE;EAAlE,mBAAkE;EAAlE,oBAAkE;EAAlE,gBAAkE;EAAlE,+FAAkE;EAAlE,wDAAkE;EAAlE,0BAAkE;EAAlE,oBAAkE;EAAlE;AAAkE;AAAlE;EAAA,kBAAkE;EAAlE,yDAAkE;EAAlE,oBAAkE;EAAlE;AAAkE;AAGpE,2CAA2C;AA+B3C,oCAAoC;AAepC,6BAA6B;AAC7B;IACE,0BAA0B;IAC1B,mCAAmC;EACrC;AAEA;IACE,4BAA4B;IAC5B,mCAAmC;EACrC;AAEA;IACE,2BAA2B;IAC3B,mCAAmC;EACrC;AAEA,+BAA+B;AAC/B;IACE,mCAAmC;IACnC,mCAAmC;EACrC;AAEA;IACE,8CAA8C;IAC9C,mCAAmC;EACrC;AAEA;IACE,2CAA2C;IAC3C,mCAAmC;EACrC;AAEA,6BAA6B;AAC7B;IACE,qCAAqC;IACrC,yCAAyC;IACzC,mCAAmC;EACrC;AAEA;IACE,oCAAoC;IACpC,mCAAmC;EACrC;AAnbF;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,QAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,oBAAmB;EAAnB,4BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6CAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,6CAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,wJAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAwbjB,0BAA0B;AAS1B,kBAAkB;AASlB,6CAA6C;AAC7C;IACE;+CAC2C;EAC7C;AAEA;IACE;gDAC4C;EAC9C;AAEA;IACE;gDAC4C;EAC9C;AAEA;IACE;+CAC2C;EAC7C;AAEA;IACE;gDAC4C;EAC9C;AAEA,wBAAwB;AAKxB;IACE,2DAA2D;EAC7D;AAEA;IACE,0DAA0D;EAC5D;AAEA,oBAAoB;AACpB;IACE,wCAAwC;EAC1C;AAEA;IACE,wCAAwC;IACxC,mBAAmB;EACrB;AAEA,mBAAmB;AACnB;IACE,kBAAkB;IAClB,gBAAgB;EAClB;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,MAAM;IACN,WAAW;IACX,WAAW;IACX,YAAY;IACZ,sFAAsF;IACtF,8BAA8B;EAChC;AAEA,0BAA0B;AAC1B;IACE,uDAAuD;EACzD;AAEA;IACE,sBAAsB;EACxB;AAEA,gBAAgB;AAChB;IACE,uDAAuD;EACzD;AAEA;IACE,0DAA0D;EAC5D;AAEA,iCAAiC;AAajC;IACE,oCAAoC;EACtC;AAEA;IACE,qCAAqC;EACvC;AAcA,yBAAyB;AACzB,qBAAqB,qBAAqB,EAAE;AAC5C,qBAAqB,qBAAqB,EAAE;AAC5C,qBAAqB,qBAAqB,EAAE;AAI5C,0CAA0C;AAU1C,wBAAwB;;AA9kB1B,wBAAwB;;AAGxB,sBAAsB;AACtB,mHAAmH;AACnH,wHAAwH;;AAExH,gBAAgB;;AAwChB,qBAAqB;;AAqYrB,mBAAmB;;AAwKnB,+BAA+B;AAC/B;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,4BAA4B;IAC5B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,4BAA4B;IAC5B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,UAAU;EACZ;EACA;IACE,sBAAsB;IACtB,YAAY;EACd;EACA;IACE,qBAAqB;IACrB,UAAU;EACZ;EACA;IACE,mBAAmB;IACnB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,UAAU;EACZ;EACA;IACE,mBAAmB;IACnB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,oCAAoC;IACpC,UAAU;EACZ;EACA;IACE,gCAAgC;IAChC,UAAU;EACZ;AACF;;AAEA;EACE;IACE,0BAA0B;EAC5B;EACA;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE;IACE,4CAA4C;EAC9C;EACA;IACE,gFAAgF;EAClF;AACF;;AAEA;EACE;IACE,0CAA0C;EAC5C;EACA;IACE,4EAA4E;EAC9E;AACF;;AAEA;EACE;IACE,2CAA2C;EAC7C;EACA;IACE,8EAA8E;EAChF;AACF;;AAEA;EACE;IACE,WAAW;EACb;EACA;IACE,UAAU;EACZ;AACF;;AAEA,iBAAiB;AACjB;EACE;IACE,wBAAwB;EAC1B;;EAGE;IAAA,kBAA0B;IAA1B,4DAA0B;IAA1B,oBAA0B;IAA1B;EAA0B;AAE9B;AACA;;;EAGE,6CAA6C;EAC7C,wBAAwB;EACxB,2BAA2B;EAC3B,mBAAmB;AACrB;;AAEA,wBAAwB;AACxB;EACE;;;IAGE,qCAAqC;IACrC,uCAAuC;IACvC,sCAAsC;IACtC,gCAAgC;EAClC;AACF;;AAEA,sBAAsB;AACtB;;;EAGE,kCAAkC;AACpC;;AAEA;;;;EAIE,oDAAoD;AACtD;;AAEA,uBAAuB;AACvB;EACE,iCAAiC;EACjC,yBAAyB;AAC3B;;AAEA,4BAA4B;AAC5B;EACE,6BAA6B;EAC7B,mCAAmC;EACnC,kCAAkC;AACpC;;AAEA,oBAAoB;AACpB;EACE,wBAAwB;EACxB,mCAAmC;AACrC;;AAEA,0BAA0B;AAC1B;EACE,2BAA2B;AAC7B;AAnaE;IACE,0BAA0B;IAC1B,mCAAmC;EACrC;AA/YF;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,wBAgzBA;EAhzBA,wDAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,gBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,8BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,2GAgzBA;EAhzBA,yGAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,8BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,2GAgzBA;EAhzBA,yGAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;;EAAA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA,uBAgzBA;IAhzBA,2DAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA,oBAgzBA;IAhzBA;EAgzBA;AAAA;AAhzBA;;EAAA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;AAAA;AAhzBA;;EAAA;IAAA;EAgzBA;;EAhzBA;IAAA,QAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA,uBAgzBA;IAhzBA,oDAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA,uBAgzBA;IAhzBA,2DAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA,kBAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA;EAgzBA;AAAA;AAhzBA;;EAAA;IAAA;EAgzBA;AAAA\",\"sourcesContent\":[\"@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* Import theme system */\\n@import './themes.css';\\n\\n/* Import Inter font */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');\\n@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');\\n\\n/* Base styles */\\n@layer base {\\n  html {\\n    font-family: 'Inter', system-ui, sans-serif;\\n    scroll-behavior: smooth;\\n  }\\n  \\n  body {\\n    background-color: var(--bg-primary);\\n    color: var(--text-primary);\\n    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';\\n    transition: var(--theme-transition);\\n    @apply antialiased;\\n  }\\n\\n  /* Custom scrollbar */\\n  ::-webkit-scrollbar {\\n    width: 6px;\\n    height: 6px;\\n  }\\n\\n  ::-webkit-scrollbar-track {\\n    @apply bg-slate-800;\\n  }\\n\\n  ::-webkit-scrollbar-thumb {\\n    @apply bg-slate-600 rounded-full;\\n  }\\n\\n  ::-webkit-scrollbar-thumb:hover {\\n    @apply bg-slate-500;\\n  }\\n\\n  /* Firefox scrollbar */\\n  * {\\n    scrollbar-width: thin;\\n    scrollbar-color: #475569 #1e293b;\\n  }\\n}\\n\\n/* Component styles */\\n@layer components {\\n  /* Enhanced Card components with theme-aware immersive effects */\\n  .card {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    @apply rounded-lg;\\n    transition: var(--theme-transition);\\n  }\\n\\n  .card-hover {\\n    @apply card;\\n    transform: translateY(0);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .card-hover:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n\\n  /* Immersive card with glow effect */\\n  .card-glow {\\n    @apply card-hover;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .card-glow::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);\\n    transition: left 0.5s;\\n  }\\n\\n  .card-glow:hover::before {\\n    left: 100%;\\n  }\\n\\n  /* Premium card with enhanced theme-aware effects */\\n  .card-premium {\\n    @apply card-glow;\\n    background: var(--bg-glass-strong);\\n    backdrop-filter: blur(15px);\\n    border: 1px solid var(--border-secondary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .card-premium:hover {\\n    background: var(--bg-glass-strong);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n\\n  /* Enhanced Button variants with immersive effects */\\n  .btn-primary {\\n    @apply bg-green-600 text-white font-medium px-4 py-2 rounded-lg focus:outline-none;\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  }\\n\\n  .btn-primary:hover {\\n    @apply bg-green-700;\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 20px rgba(34, 197, 94, 0.3);\\n  }\\n\\n  .btn-primary:active {\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .btn-secondary {\\n    @apply bg-slate-700 text-white font-medium px-4 py-2 rounded-lg focus:outline-none;\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .btn-secondary:hover {\\n    @apply bg-slate-600;\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 15px rgba(71, 85, 105, 0.3);\\n  }\\n\\n  .btn-outline {\\n    @apply border border-slate-600 text-slate-300 font-medium px-4 py-2 rounded-lg focus:outline-none;\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    background: transparent;\\n  }\\n\\n  .btn-outline:hover {\\n    @apply border-green-500 text-white bg-slate-800;\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 0 15px rgba(34, 197, 94, 0.2);\\n  }\\n\\n  .btn-danger {\\n    @apply bg-red-600 text-white font-medium px-4 py-2 rounded-lg focus:outline-none;\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .btn-danger:hover {\\n    @apply bg-red-700;\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 20px rgba(239, 68, 68, 0.3);\\n  }\\n\\n  /* Button ripple effect */\\n  .btn-ripple {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .btn-ripple::before {\\n    content: '';\\n    position: absolute;\\n    top: 50%;\\n    left: 50%;\\n    width: 0;\\n    height: 0;\\n    border-radius: 50%;\\n    background: rgba(255, 255, 255, 0.3);\\n    transform: translate(-50%, -50%);\\n    transition: width 0.3s, height 0.3s;\\n  }\\n\\n  .btn-ripple:active::before {\\n    width: 300px;\\n    height: 300px;\\n  }\\n\\n  /* Enhanced Input styles with immersive effects */\\n  .input-primary {\\n    @apply bg-slate-800 border border-slate-600 text-white placeholder-slate-400 rounded-lg px-3 py-2 focus:outline-none;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    position: relative;\\n  }\\n\\n  .input-primary:focus {\\n    @apply border-green-500 bg-slate-750;\\n    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1),\\n                0 0 20px rgba(34, 197, 94, 0.1);\\n    transform: translateY(-1px);\\n  }\\n\\n  .input-primary:hover:not(:focus) {\\n    @apply border-slate-500;\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  /* Floating label input */\\n  .input-floating {\\n    @apply input-primary;\\n    padding-top: 1.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .input-floating + label {\\n    position: absolute;\\n    left: 0.75rem;\\n    top: 0.75rem;\\n    color: rgb(148, 163, 184);\\n    font-size: 0.875rem;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    pointer-events: none;\\n    transform-origin: left top;\\n  }\\n\\n  .input-floating:focus + label,\\n  .input-floating:not(:placeholder-shown) + label {\\n    transform: translateY(-0.5rem) scale(0.75);\\n    color: rgb(34, 197, 94);\\n  }\\n\\n  /* Enhanced Status indicators with glow effects */\\n  .status-online {\\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/20 text-green-400 border border-green-500/20;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .status-online::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background: rgba(34, 197, 94, 0.1);\\n    border-radius: inherit;\\n    animation: glowPulseGreen 2s ease-in-out infinite alternate;\\n  }\\n\\n  .status-offline {\\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900/20 text-red-400 border border-red-500/20;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .status-warning {\\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/20 text-yellow-400 border border-yellow-500/20;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  /* Live indicator with pulse */\\n  .status-live {\\n    @apply status-online;\\n  }\\n\\n  .status-live::after {\\n    content: '';\\n    position: absolute;\\n    top: 50%;\\n    left: 0.5rem;\\n    width: 6px;\\n    height: 6px;\\n    background: rgb(34, 197, 94);\\n    border-radius: 50%;\\n    transform: translateY(-50%);\\n    animation: pulse 1.5s ease-in-out infinite;\\n  }\\n\\n  /* Premium status indicator */\\n  .status-premium {\\n    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;\\n    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(79, 70, 229, 0.2));\\n    color: rgb(196, 181, 253);\\n    border: 1px solid rgba(147, 51, 234, 0.3);\\n    box-shadow: 0 0 15px rgba(147, 51, 234, 0.2);\\n  }\\n\\n  /* Risk level indicators */\\n  .risk-low {\\n    @apply text-green-400 bg-green-900/20 border border-green-500/20;\\n  }\\n\\n  .risk-medium {\\n    @apply text-yellow-400 bg-yellow-900/20 border border-yellow-500/20;\\n  }\\n\\n  .risk-high {\\n    @apply text-red-400 bg-red-900/20 border border-red-500/20;\\n  }\\n\\n  /* Loading states */\\n  .loading-skeleton {\\n    @apply animate-pulse bg-slate-700 rounded;\\n  }\\n\\n  .loading-spinner {\\n    @apply animate-spin rounded-full border-2 border-slate-600 border-t-green-400;\\n  }\\n\\n  /* Data visualization */\\n  .chart-container {\\n    @apply bg-slate-800/50 rounded-lg p-4 border border-slate-700;\\n  }\\n\\n  /* Navigation */\\n  .nav-link {\\n    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;\\n  }\\n\\n  .nav-link-active {\\n    @apply nav-link bg-green-900/20 text-green-400 border border-green-500/20;\\n  }\\n\\n  .nav-link-inactive {\\n    @apply nav-link text-slate-400 hover:text-white hover:bg-slate-800;\\n  }\\n\\n  /* Enhanced Tables with immersive effects */\\n  .table-container {\\n    @apply overflow-hidden rounded-lg border border-slate-700;\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .table-header {\\n    @apply bg-slate-800 px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider border-b border-slate-700;\\n    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(51, 65, 85, 0.9));\\n  }\\n\\n  .table-cell {\\n    @apply px-6 py-4 whitespace-nowrap text-sm text-slate-300 border-b border-slate-700/50;\\n    transition: all 0.2s ease-in-out;\\n  }\\n\\n  .table-row {\\n    @apply bg-slate-900 transition-all duration-300;\\n    position: relative;\\n  }\\n\\n  .table-row:hover {\\n    @apply bg-slate-800/70;\\n    transform: translateX(2px);\\n    box-shadow: 4px 0 8px rgba(34, 197, 94, 0.1);\\n  }\\n\\n  .table-row:hover .table-cell {\\n    @apply text-white;\\n  }\\n\\n  /* Interactive table row with glow */\\n  .table-row-interactive {\\n    @apply table-row cursor-pointer;\\n  }\\n\\n  .table-row-interactive:hover {\\n    background: linear-gradient(90deg, rgba(30, 41, 59, 0.8), rgba(34, 197, 94, 0.05), rgba(30, 41, 59, 0.8));\\n    border-left: 3px solid rgb(34, 197, 94);\\n  }\\n\\n  .table-row-interactive:active {\\n    transform: translateX(1px);\\n    box-shadow: 2px 0 4px rgba(34, 197, 94, 0.2);\\n  }\\n\\n  /* Theme-aware text classes */\\n  .themed-text-primary {\\n    color: var(--text-primary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-text-secondary {\\n    color: var(--text-secondary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-text-tertiary {\\n    color: var(--text-tertiary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  /* Theme-aware border classes */\\n  .themed-border {\\n    border-color: var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-border-b {\\n    border-bottom: 1px solid var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-border-t {\\n    border-top: 1px solid var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  /* Theme-aware card classes */\\n  .themed-card-secondary {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-secondary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-card-secondary:hover {\\n    background-color: var(--bg-tertiary);\\n    border-color: var(--border-primary);\\n  }\\n}\\n\\n/* Utility styles */\\n@layer utilities {\\n  /* Glass morphism effect */\\n  .glass {\\n    @apply bg-white/5 backdrop-blur-md border border-white/10;\\n  }\\n\\n  .glass-dark {\\n    @apply bg-black/20 backdrop-blur-md border border-white/5;\\n  }\\n\\n  /* Gradient text */\\n  .gradient-text {\\n    @apply bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent;\\n  }\\n\\n  .gradient-text-warm {\\n    @apply bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent;\\n  }\\n\\n  /* Enhanced Custom shadows and glow effects */\\n  .shadow-glow-green {\\n    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3),\\n                0 0 40px rgba(34, 197, 94, 0.1);\\n  }\\n\\n  .shadow-glow-blue {\\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3),\\n                0 0 40px rgba(59, 130, 246, 0.1);\\n  }\\n\\n  .shadow-glow-orange {\\n    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3),\\n                0 0 40px rgba(251, 191, 36, 0.1);\\n  }\\n\\n  .shadow-glow-red {\\n    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3),\\n                0 0 40px rgba(239, 68, 68, 0.1);\\n  }\\n\\n  .shadow-glow-purple {\\n    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3),\\n                0 0 40px rgba(147, 51, 234, 0.1);\\n  }\\n\\n  /* Pulsing glow effect */\\n  .glow-pulse {\\n    animation: glowPulse 2s ease-in-out infinite alternate;\\n  }\\n\\n  .glow-pulse-green {\\n    animation: glowPulseGreen 2s ease-in-out infinite alternate;\\n  }\\n\\n  .glow-pulse-blue {\\n    animation: glowPulseBlue 2s ease-in-out infinite alternate;\\n  }\\n\\n  /* Floating effect */\\n  .float {\\n    animation: float 3s ease-in-out infinite;\\n  }\\n\\n  .float-delayed {\\n    animation: float 3s ease-in-out infinite;\\n    animation-delay: 1s;\\n  }\\n\\n  /* Shimmer effect */\\n  .shimmer {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .shimmer::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);\\n    animation: shimmer 2s infinite;\\n  }\\n\\n  /* Magnetic hover effect */\\n  .magnetic {\\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n\\n  .magnetic:hover {\\n    transform: scale(1.02);\\n  }\\n\\n  /* Tilt effect */\\n  .tilt {\\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n\\n  .tilt:hover {\\n    transform: perspective(1000px) rotateX(5deg) rotateY(5deg);\\n  }\\n\\n  /* Enhanced Animation utilities */\\n  .animate-fade-in {\\n    animation: fadeIn 0.5s ease-in-out;\\n  }\\n\\n  .animate-slide-up {\\n    animation: slideUp 0.3s ease-out;\\n  }\\n\\n  .animate-slide-down {\\n    animation: slideDown 0.3s ease-out;\\n  }\\n\\n  .animate-slide-in-left {\\n    animation: slideInLeft 0.4s ease-out;\\n  }\\n\\n  .animate-slide-in-right {\\n    animation: slideInRight 0.4s ease-out;\\n  }\\n\\n  .animate-bounce-in {\\n    animation: bounceIn 0.6s ease-out;\\n  }\\n\\n  .animate-scale-in {\\n    animation: scaleIn 0.3s ease-out;\\n  }\\n\\n  .animate-rotate-in {\\n    animation: rotateIn 0.5s ease-out;\\n  }\\n\\n  /* Staggered animations */\\n  .animate-stagger-1 { animation-delay: 0.1s; }\\n  .animate-stagger-2 { animation-delay: 0.2s; }\\n  .animate-stagger-3 { animation-delay: 0.3s; }\\n  .animate-stagger-4 { animation-delay: 0.4s; }\\n  .animate-stagger-5 { animation-delay: 0.5s; }\\n\\n  /* Hide scrollbar but keep functionality */\\n  .scrollbar-hide {\\n    -ms-overflow-style: none;\\n    scrollbar-width: none;\\n  }\\n\\n  .scrollbar-hide::-webkit-scrollbar {\\n    display: none;\\n  }\\n\\n  /* Custom focus styles */\\n  .focus-ring {\\n    @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-slate-900;\\n  }\\n\\n  .focus-ring-blue {\\n    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900;\\n  }\\n\\n  .focus-ring-orange {\\n    @apply focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-slate-900;\\n  }\\n}\\n\\n/* Enhanced Custom animations */\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideDown {\\n  from {\\n    transform: translateY(-10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideInLeft {\\n  from {\\n    transform: translateX(-20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideInRight {\\n  from {\\n    transform: translateX(20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes bounceIn {\\n  0% {\\n    transform: scale(0.3);\\n    opacity: 0;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.8;\\n  }\\n  70% {\\n    transform: scale(0.9);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes scaleIn {\\n  from {\\n    transform: scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes rotateIn {\\n  from {\\n    transform: rotate(-10deg) scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: rotate(0deg) scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n\\n@keyframes glowPulse {\\n  0% {\\n    box-shadow: 0 0 5px rgba(255, 255, 255, 0.1);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3), 0 0 30px rgba(255, 255, 255, 0.1);\\n  }\\n}\\n\\n@keyframes glowPulseGreen {\\n  0% {\\n    box-shadow: 0 0 5px rgba(34, 197, 94, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(34, 197, 94, 0.4), 0 0 30px rgba(34, 197, 94, 0.2);\\n  }\\n}\\n\\n@keyframes glowPulseBlue {\\n  0% {\\n    box-shadow: 0 0 5px rgba(59, 130, 246, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 0 30px rgba(59, 130, 246, 0.2);\\n  }\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n  \\n  body {\\n    @apply bg-white text-black;\\n  }\\n}\\n\\\\n\\\\n\\n/* Performance optimizations */\\n* {\\n  /* Use hardware acceleration for transforms */\\n  transform: translateZ(0);\\n  backface-visibility: hidden;\\n  perspective: 1000px;\\n}\\n\\n/* Optimize animations */\\n@media (prefers-reduced-motion: reduce) {\\n  *,\\n  *::before,\\n  *::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n    scroll-behavior: auto !important;\\n  }\\n}\\n\\n/* Optimize repaints */\\n.card-premium,\\n.card-glow,\\n.magnetic {\\n  will-change: transform, box-shadow;\\n}\\n\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline,\\n.btn-danger {\\n  will-change: transform, box-shadow, background-color;\\n}\\n\\n/* Optimize scrolling */\\n.scrollable {\\n  -webkit-overflow-scrolling: touch;\\n  overflow-scrolling: touch;\\n}\\n\\n/* Optimize text rendering */\\nbody {\\n  text-rendering: optimizeSpeed;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n/* Optimize images */\\nimg {\\n  content-visibility: auto;\\n  contain-intrinsic-size: 300px 200px;\\n}\\n\\n/* Critical CSS inlining */\\n.above-fold {\\n  contain: layout style paint;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/globals.css\n"));

/***/ })

});