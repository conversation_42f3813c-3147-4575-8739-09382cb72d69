import React, { useState } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import Layout from '../components/Layout';
import ErrorBoundary from '../components/ErrorBoundary';

const ErrorTestPage: NextPage = () => {
  const [shouldError, setShouldError] = useState(false);

  const ThrowError = () => {
    if (shouldError) {
      throw new Error('Test error thrown intentionally!');
    }
    return <div>No error - component working fine!</div>;
  };

  return (
    <>
      <Head>
        <title>Error Boundary Test - FantasyPro</title>
        <meta name="description" content="Test error boundary functionality" />
      </Head>

      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold themed-text-primary mb-8">
              🧪 Error Boundary Test
            </h1>

            <div className="space-y-6">
              {/* Error Boundary Test */}
              <div className="card p-6">
                <h2 className="text-xl font-semibold themed-text-primary mb-4">
                  Error Boundary Component Test
                </h2>
                
                <div className="space-y-4">
                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={() => setShouldError(false)}
                      className="btn-primary"
                    >
                      Normal Component
                    </button>
                    <button
                      type="button"
                      onClick={() => setShouldError(true)}
                      className="btn-danger"
                    >
                      Trigger Error
                    </button>
                  </div>

                  <ErrorBoundary
                    fallback={
                      <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                        <h3 className="font-semibold text-yellow-400 mb-2">⚠️ Custom Fallback</h3>
                        <p className="text-sm text-yellow-300">
                          This is a custom fallback component for the error boundary test.
                        </p>
                      </div>
                    }
                  >
                    <div className="p-4 themed-bg-secondary rounded-lg">
                      <ThrowError />
                    </div>
                  </ErrorBoundary>
                </div>
              </div>

              {/* Component Status */}
              <div className="card p-6">
                <h2 className="text-xl font-semibold themed-text-primary mb-4">
                  Component Status
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 themed-bg-secondary rounded-lg">
                    <h3 className="font-semibold themed-text-primary mb-2">Error State</h3>
                    <p className="text-sm themed-text-secondary">
                      {shouldError ? '❌ Error will be thrown' : '✅ Component is safe'}
                    </p>
                  </div>
                  
                  <div className="p-4 themed-bg-secondary rounded-lg">
                    <h3 className="font-semibold themed-text-primary mb-2">Error Boundary</h3>
                    <p className="text-sm themed-text-secondary">
                      ✅ Active and ready to catch errors
                    </p>
                  </div>
                </div>
              </div>

              {/* Instructions */}
              <div className="card p-6">
                <h2 className="text-xl font-semibold themed-text-primary mb-4">
                  📋 Test Instructions
                </h2>
                
                <div className="space-y-3 text-sm themed-text-secondary">
                  <p>1. Click "Normal Component" to see the component working normally</p>
                  <p>2. Click "Trigger Error" to test the error boundary</p>
                  <p>3. The error boundary should catch the error and show a fallback UI</p>
                  <p>4. Check the browser console for error logs</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default ErrorTestPage;
