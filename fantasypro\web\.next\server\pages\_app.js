/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   ThemeTransition: () => (/* binding */ ThemeTransition),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useThemeTransition: () => (/* binding */ useThemeTransition)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\nconst ThemeProvider = ({ children, defaultTheme = \"system\", storageKey = \"fantasypro-theme\" })=>{\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dark\");\n    // Get system preference\n    const getSystemTheme = ()=>{\n        if (false) {}\n        return \"dark\";\n    };\n    // Resolve theme based on current setting\n    const resolveTheme = (currentTheme)=>{\n        if (currentTheme === \"system\") {\n            return getSystemTheme();\n        }\n        return currentTheme;\n    };\n    // Apply theme to document\n    const applyTheme = (resolvedTheme)=>{\n        if (false) {}\n    };\n    // Set theme with persistence\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        if (false) {}\n        const resolved = resolveTheme(newTheme);\n        setResolvedTheme(resolved);\n        applyTheme(resolved);\n    };\n    // Toggle between dark and light (skips system)\n    const toggleTheme = ()=>{\n        const newTheme = resolvedTheme === \"dark\" ? \"light\" : \"dark\";\n        setTheme(newTheme);\n    };\n    // Initialize theme on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        defaultTheme,\n        storageKey\n    ]);\n    // Listen for system theme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        theme\n    ]);\n    const value = {\n        theme,\n        resolvedTheme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, undefined);\n};\n// Theme transition component for smooth animations\nconst ThemeTransition = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"theme-transition\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook for theme-aware animations\nconst useThemeTransition = ()=>{\n    const { resolvedTheme } = useTheme();\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const triggerTransition = ()=>{\n        setIsTransitioning(true);\n        setTimeout(()=>setIsTransitioning(false), 300);\n    };\n    return {\n        isTransitioning,\n        triggerTransition,\n        resolvedTheme\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ThemeProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_dropdown_fix_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/dropdown-fix.css */ \"./src/styles/dropdown-fix.css\");\n/* harmony import */ var _styles_dropdown_fix_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_dropdown_fix_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"./src/contexts/ThemeContext.tsx\");\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n        defaultTheme: \"dark\",\n        storageKey: \"fantasypro-theme\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQytCO0FBQ0s7QUFDcUI7QUFFMUMsU0FBU0MsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBWTtJQUM1RCxxQkFDRSw4REFBQ0gsaUVBQWFBO1FBQUNJLGNBQWE7UUFBT0MsWUFBVztrQkFDNUMsNEVBQUNIO1lBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7QUFHOUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mYW50YXN5cHJvLXdlYi8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgJy4uL3N0eWxlcy9kcm9wZG93bi1maXguY3NzJztcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICcuLi9jb250ZXh0cy9UaGVtZUNvbnRleHQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIGRlZmF1bHRUaGVtZT1cImRhcmtcIiBzdG9yYWdlS2V5PVwiZmFudGFzeXByby10aGVtZVwiPlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvVGhlbWVQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJUaGVtZVByb3ZpZGVyIiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiZGVmYXVsdFRoZW1lIiwic3RvcmFnZUtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/styles/dropdown-fix.css":
/*!*************************************!*\
  !*** ./src/styles/dropdown-fix.css ***!
  \*************************************/
/***/ (() => {



/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./src/pages/_app.tsx"));
module.exports = __webpack_exports__;

})();