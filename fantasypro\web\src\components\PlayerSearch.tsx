import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';

// Player interface matching our API
interface Player {
  id: number;
  name: string;
  team: string;
  position: string;
  current_price?: number | null;
  current_breakeven?: number;
  season_points?: number;
}

interface PlayerSearchProps {
  onPlayerSelect: (player: Player) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  selectedPlayer?: Player | null;
  excludePlayerIds?: number[];
  filterByPosition?: string[];
  showTeam?: boolean;
  showPrice?: boolean;
  showPosition?: boolean;
  maxResults?: number;
}

const PlayerSearch: React.FC<PlayerSearchProps> = ({
  onPlayerSelect,
  placeholder = "Search for a player...",
  className = "",
  disabled = false,
  selectedPlayer = null,
  excludePlayerIds = [],
  filterByPosition = [],
  showTeam = true,
  showPrice = false,
  showPosition = true,
  maxResults = 10
}) => {
  const [query, setQuery] = useState('');
  const [players, setPlayers] = useState<Player[]>([]);
  const [filteredPlayers, setFilteredPlayers] = useState<Player[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Load players from Supabase on component mount
  useEffect(() => {
    const fetchPlayers = async () => {
      try {
        setLoading(true);
        console.log('🔍 PlayerSearch loading players from Supabase...');

        // Import PlayerService dynamically to avoid SSR issues
        const { PlayerService } = await import('../services/supabase');
        const playersData = await PlayerService.getAllPlayers();

        // Transform to match the expected interface
        const transformedPlayers = playersData.map(player => ({
          id: parseInt(player.id),
          name: player.name,
          team: player.team,
          position: player.position,
          current_price: player.price,
          current_breakeven: player.breakeven,
          season_points: player.points
        }));

        console.log(`✅ PlayerSearch loaded ${transformedPlayers.length} players from Supabase`);
        setPlayers(transformedPlayers);

      } catch (error) {
        console.error('❌ Error loading players from Supabase:', error);

        // Fallback: try API endpoint
        try {
          console.log('🔄 Trying fallback API endpoint...');
          const response = await fetch('/api/players');
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.data.players) {
              const transformedPlayers = data.data.players.map(player => ({
                id: parseInt(player.id),
                name: player.name,
                team: player.team,
                position: player.position,
                current_price: player.price,
                current_breakeven: player.breakeven,
                season_points: player.points
              }));
              console.log(`✅ PlayerSearch loaded ${transformedPlayers.length} players from API fallback`);
              setPlayers(transformedPlayers);
            }
          }
        } catch (fallbackError) {
          console.error('❌ Fallback API also failed:', fallbackError);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchPlayers();
  }, []);

  // Filter players based on search query and filters
  const filterPlayers = useCallback((searchQuery: string) => {
    if (!searchQuery.trim()) {
      setFilteredPlayers([]);
      return;
    }

    let filtered = players.filter(player => {
      // Exclude specific player IDs
      if (excludePlayerIds.includes(player.id)) return false;
      
      // Filter by position if specified
      if (filterByPosition.length > 0 && !filterByPosition.includes(player.position)) return false;
      
      // Search by name (case insensitive, partial match)
      const nameMatch = player.name.toLowerCase().includes(searchQuery.toLowerCase());
      const teamMatch = player.team.toLowerCase().includes(searchQuery.toLowerCase());
      
      return nameMatch || teamMatch;
    });

    // Sort by relevance (exact name matches first, then partial matches)
    filtered.sort((a, b) => {
      const aNameExact = a.name.toLowerCase().startsWith(searchQuery.toLowerCase());
      const bNameExact = b.name.toLowerCase().startsWith(searchQuery.toLowerCase());
      
      if (aNameExact && !bNameExact) return -1;
      if (!aNameExact && bNameExact) return 1;
      
      return a.name.localeCompare(b.name);
    });

    setFilteredPlayers(filtered.slice(0, maxResults));
    setSelectedIndex(-1);
  }, [players, excludePlayerIds, filterByPosition, maxResults]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    filterPlayers(value);
    setIsOpen(true);
  };

  // Handle player selection
  const handlePlayerSelect = (player: Player) => {
    setQuery(player.name);
    setIsOpen(false);
    setSelectedIndex(-1);
    onPlayerSelect(player);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || filteredPlayers.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < filteredPlayers.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < filteredPlayers.length) {
          handlePlayerSelect(filteredPlayers[selectedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        break;
    }
  };

  // Clear search
  const clearSearch = () => {
    setQuery('');
    setFilteredPlayers([]);
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  // Format currency
  const formatCurrency = (amount: number | null | undefined) => {
    if (!amount) return 'N/A';
    return `$${(amount / 1000).toFixed(0)}k`;
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Set initial value if selectedPlayer is provided
  useEffect(() => {
    if (selectedPlayer) {
      setQuery(selectedPlayer.name);
    }
  }, [selectedPlayer]);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-slate-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => query && setIsOpen(true)}
          placeholder={placeholder}
          disabled={disabled}
          className={`
            input-primary w-full pl-10 pr-10
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        />
        
        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-white"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        )}
      </div>

      <AnimatePresence>
        {isOpen && (filteredPlayers.length > 0 || loading) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute z-50 w-full mt-1 bg-slate-800 border border-slate-600 rounded-lg shadow-lg max-h-64 overflow-y-auto"
          >
            {loading ? (
              <div className="p-4 text-center text-slate-400">
                <div className="loading-spinner w-5 h-5 mx-auto mb-2"></div>
                Loading players...
              </div>
            ) : filteredPlayers.length > 0 ? (
              filteredPlayers.map((player, index) => (
                <motion.div
                  key={player.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.1, delay: index * 0.02 }}
                  onClick={() => handlePlayerSelect(player)}
                  className={`
                    p-3 cursor-pointer border-b border-slate-700 last:border-b-0
                    ${selectedIndex === index ? 'bg-slate-700' : 'hover:bg-slate-700/50'}
                    transition-colors duration-150
                  `}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-white">{player.name}</div>
                      <div className="flex items-center space-x-2 text-sm text-slate-400">
                        {showTeam && <span>{player.team}</span>}
                        {showPosition && player.position !== 'Unknown' && (
                          <>
                            {showTeam && <span>•</span>}
                            <span>{player.position}</span>
                          </>
                        )}
                      </div>
                    </div>
                    {showPrice && (
                      <div className="text-sm text-slate-300">
                        {formatCurrency(player.current_price)}
                      </div>
                    )}
                  </div>
                </motion.div>
              ))
            ) : (
              <div className="p-4 text-center text-slate-400">
                No players found matching "{query}"
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PlayerSearch;
