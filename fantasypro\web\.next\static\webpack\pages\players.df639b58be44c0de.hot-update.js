"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/players",{

/***/ "./src/services/supabase.ts":
/*!**********************************!*\
  !*** ./src/services/supabase.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: function() { return /* binding */ AnalyticsService; },\n/* harmony export */   CacheService: function() { return /* binding */ CacheService; },\n/* harmony export */   InjuryService: function() { return /* binding */ InjuryService; },\n/* harmony export */   PlayerService: function() { return /* binding */ PlayerService; },\n/* harmony export */   SquadService: function() { return /* binding */ SquadService; },\n/* harmony export */   TradeService: function() { return /* binding */ TradeService; },\n/* harmony export */   supabase: function() { return /* binding */ supabase; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"./node_modules/next/node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @swc/helpers/_/_class_call_check */ \"./node_modules/next/node_modules/@swc/helpers/esm/_class_call_check.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"./node_modules/next/node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"./node_modules/next/node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"./node_modules/next/node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"./node_modules/next/node_modules/@swc/helpers/esm/_ts_generator.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n\n\n\n\n\n// Supabase configuration\nvar supabaseUrl = \"https://fuxpdgsixnbbsdspusmp.supabase.co\" || 0;\nvar supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ3NzE5NzQsImV4cCI6MjA1MDM0Nzk3NH0.Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5\" || 0;\n// Create Supabase client\nvar supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Player service functions\nvar PlayerService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function PlayerService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, PlayerService);\n    }\n    // Get all players with search and filtering\n    PlayerService.getPlayers = function getPlayers() {\n        var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var query, _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        query = supabase.from(\"players\").select(\"*\");\n                        if (options.search) {\n                            query = query.or(\"name.ilike.%\".concat(options.search, \"%,team.ilike.%\").concat(options.search, \"%,position.ilike.%\").concat(options.search, \"%\"));\n                        }\n                        if (options.position) {\n                            query = query.eq(\"position\", options.position);\n                        }\n                        if (options.team) {\n                            query = query.eq(\"team\", options.team);\n                        }\n                        if (options.limit) {\n                            query = query.limit(options.limit);\n                        }\n                        if (options.offset) {\n                            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n                        }\n                        return [\n                            4,\n                            query.order(\"points\", {\n                                ascending: false\n                            })\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching players:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Get ALL players (for Players page - should return 581 players)\n    PlayerService.getAllPlayers = function getAllPlayers() {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        console.log(\"\\uD83D\\uDD04 PlayerService.getAllPlayers() - fetching complete dataset from Supabase\");\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").order(\"points\", {\n                                ascending: false\n                            })\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"❌ Error fetching all players from Supabase:\", error);\n                            throw error;\n                        }\n                        console.log(\"✅ Supabase returned \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" players\"));\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Get player by ID\n    PlayerService.getPlayer = function getPlayer(id) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").eq(\"id\", id).single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching player:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Search players with predictive algorithm\n    PlayerService.searchPlayers = function searchPlayers(query) {\n        var limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!query || query.length < 2) return [\n                            2,\n                            []\n                        ];\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").or(\"name.ilike.%\".concat(query, \"%,team.ilike.%\").concat(query, \"%,position.ilike.%\").concat(query, \"%\")).order(\"points\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error searching players:\", error);\n                            return [\n                                2,\n                                []\n                            ];\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Get top performers\n    PlayerService.getTopPerformers = function getTopPerformers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").order(\"points\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching top performers:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Get price risers\n    PlayerService.getPriceRisers = function getPriceRisers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").order(\"price\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching price risers:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Update player data\n    PlayerService.updatePlayer = function updatePlayer(id, updates) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").update((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, updates), {\n                                updated_at: new Date().toISOString()\n                            })).eq(\"id\", id).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error updating player:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return PlayerService;\n}();\n// Trade recommendation service\nvar TradeService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function TradeService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, TradeService);\n    }\n    // Get trade recommendations for user\n    TradeService.getTradeRecommendations = function getTradeRecommendations(userId) {\n        var limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"trade_recommendations\").select(\"\\n        *,\\n        player_out:players!player_out_id(*),\\n        player_in:players!player_in_id(*)\\n      \").eq(\"user_id\", userId).order(\"confidence\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching trade recommendations:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Create trade recommendation\n    TradeService.createTradeRecommendation = function createTradeRecommendation(recommendation) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"trade_recommendations\").insert((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, recommendation), {\n                                created_at: new Date().toISOString()\n                            })).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error creating trade recommendation:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Execute trade (log the trade)\n    TradeService.executeTrade = function executeTrade(tradeId, userId) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"executed_trades\").insert({\n                                trade_recommendation_id: tradeId,\n                                user_id: userId,\n                                executed_at: new Date().toISOString()\n                            }).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error executing trade:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return TradeService;\n}();\n// Injury service\nvar InjuryService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function InjuryService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, InjuryService);\n    }\n    // Get current injury reports\n    InjuryService.getInjuryReports = function getInjuryReports() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"injury_reports\").select(\"\\n        *,\\n        player:players(*)\\n      \").order(\"created_at\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching injury reports:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Create injury report\n    InjuryService.createInjuryReport = function createInjuryReport(report) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"injury_reports\").insert((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, report), {\n                                created_at: new Date().toISOString(),\n                                updated_at: new Date().toISOString()\n                            })).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error creating injury report:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return InjuryService;\n}();\n// User squad service\nvar SquadService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function SquadService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, SquadService);\n    }\n    // Get user's squad\n    SquadService.getUserSquad = function getUserSquad(userId) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"user_squads\").select(\"\\n        *,\\n        player:players(*)\\n      \").eq(\"user_id\", userId).order(\"position_in_squad\")\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching user squad:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Add player to squad\n    SquadService.addPlayerToSquad = function addPlayerToSquad(squadEntry) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"user_squads\").insert((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, squadEntry), {\n                                created_at: new Date().toISOString()\n                            })).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error adding player to squad:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Set captain\n    SquadService.setCaptain = function setCaptain(userId, playerId) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        // First, remove captain status from all players\n                        return [\n                            4,\n                            supabase.from(\"user_squads\").update({\n                                is_captain: false,\n                                is_vice_captain: false\n                            }).eq(\"user_id\", userId)\n                        ];\n                    case 1:\n                        _state.sent();\n                        return [\n                            4,\n                            supabase.from(\"user_squads\").update({\n                                is_captain: true\n                            }).eq(\"user_id\", userId).eq(\"player_id\", playerId).select().single()\n                        ];\n                    case 2:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error setting captain:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return SquadService;\n}();\n// Analytics service\nvar AnalyticsService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function AnalyticsService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, AnalyticsService);\n    }\n    // Get dashboard stats\n    AnalyticsService.getDashboardStats = function getDashboardStats() {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, playersCount, teamsCount, injuriesCount;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            Promise.all([\n                                supabase.from(\"players\").select(\"id\", {\n                                    count: \"exact\",\n                                    head: true\n                                }),\n                                supabase.from(\"players\").select(\"team\", {\n                                    count: \"exact\",\n                                    head: true\n                                }).distinct(),\n                                supabase.from(\"injury_reports\").select(\"id\", {\n                                    count: \"exact\",\n                                    head: true\n                                })\n                            ])\n                        ];\n                    case 1:\n                        _ref = _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._.apply(void 0, [\n                            _state.sent(),\n                            3\n                        ]), playersCount = _ref[0], teamsCount = _ref[1], injuriesCount = _ref[2];\n                        return [\n                            2,\n                            {\n                                total_players: playersCount.count || 0,\n                                total_teams: 17,\n                                active_injuries: injuriesCount.count || 0,\n                                last_updated: new Date().toISOString()\n                            }\n                        ];\n                }\n            });\n        })();\n    };\n    // Get trending players\n    AnalyticsService.getTrendingPlayers = function getTrendingPlayers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").order(\"form\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching trending players:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return AnalyticsService;\n}();\n// Cache service for offline support\nvar CacheService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function CacheService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, CacheService);\n    }\n    CacheService.set = function set(key, data) {\n        var cacheData = {\n            data: data,\n            timestamp: Date.now()\n        };\n        localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheData));\n    };\n    CacheService.get = function get(key) {\n        var cached = localStorage.getItem(this.CACHE_PREFIX + key);\n        if (!cached) return null;\n        var _JSON_parse = JSON.parse(cached), data = _JSON_parse.data, timestamp = _JSON_parse.timestamp;\n        if (Date.now() - timestamp > this.CACHE_DURATION) {\n            localStorage.removeItem(this.CACHE_PREFIX + key);\n            return null;\n        }\n        return data;\n    };\n    CacheService.clear = function clear() {\n        var _this = this;\n        Object.keys(localStorage).forEach(function(key) {\n            if (key.startsWith(_this.CACHE_PREFIX)) {\n                localStorage.removeItem(key);\n            }\n        });\n    };\n    return CacheService;\n}();\nCacheService.CACHE_PREFIX = \"fantasypro_cache_\";\nCacheService.CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n;\n/* harmony default export */ __webpack_exports__[\"default\"] = (supabase);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/services/supabase.ts\n"));

/***/ })

});