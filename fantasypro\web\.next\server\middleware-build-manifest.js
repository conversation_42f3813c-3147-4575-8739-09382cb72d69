self.__BUILD_MANIFEST = {
  "polyfillFiles": [
    "static/chunks/polyfills.js"
  ],
  "devFiles": [
    "static/chunks/react-refresh.js"
  ],
  "ampDevFiles": [],
  "lowPriorityFiles": [],
  "rootMainFiles": [],
  "pages": {
    "/": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/index.js"
    ],
    "/_app": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/_app.js"
    ],
    "/_error": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/_error.js"
    ],
    "/analytics": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/analytics.js"
    ],
    "/dashboard": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/dashboard.js"
    ],
    "/injuries": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/injuries.js"
    ],
    "/my-team": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/my-team.js"
    ],
    "/players": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/players.js"
    ],
    "/settings": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/settings.js"
    ],
    "/test-all-functionality": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/test-all-functionality.js"
    ],
    "/trades": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/trades.js"
    ]
  },
  "ampFirstPages": []
};
self.__BUILD_MANIFEST.lowPriorityFiles = [
"/static/" + process.env.__NEXT_BUILD_ID + "/_buildManifest.js",
,"/static/" + process.env.__NEXT_BUILD_ID + "/_ssgManifest.js",

];