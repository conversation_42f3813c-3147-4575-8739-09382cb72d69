import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowRightIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface Player {
  id: string;
  name: string;
  position: string;
  team: string;
  price: number;
  points: number;
  form: number;
  ownership: number;
  breakeven: number;
}

interface TradeRecommendation {
  id: string;
  playerOut: Player;
  playerIn: Player;
  reason: string;
  confidence: number;
  priceChange: number;
  pointsGain: number;
  riskLevel: 'low' | 'medium' | 'high';
}

interface TradeAnalysisProps {
  recommendation: TradeRecommendation;
  onExecute: (tradeId: string) => void;
  onDismiss: (tradeId: string) => void;
}

const TradeAnalysis: React.FC<TradeAnalysisProps> = ({
  recommendation,
  onExecute,
  onDismiss
}) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);

  const handleAnalyze = async () => {
    setIsAnalyzing(true);
    
    // Simulate AI analysis
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsAnalyzing(false);
    setAnalysisComplete(true);
    setShowDetails(true);
  };

  const handleExecuteTrade = () => {
    onExecute(recommendation.id);
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getRiskIcon = (risk: string) => {
    switch (risk) {
      case 'low': return CheckCircleIcon;
      case 'medium': return ExclamationTriangleIcon;
      case 'high': return XMarkIcon;
      default: return ExclamationTriangleIcon;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="card-premium p-6 space-y-4"
    >
      {/* Trade Overview */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold themed-text-primary">Trade Analysis</h3>
        <div className="flex items-center space-x-2">
          <span className={`text-sm font-medium ${getRiskColor(recommendation.riskLevel)}`}>
            {recommendation.riskLevel.toUpperCase()} RISK
          </span>
          {React.createElement(getRiskIcon(recommendation.riskLevel), {
            className: `w-4 h-4 ${getRiskColor(recommendation.riskLevel)}`
          })}
        </div>
      </div>

      {/* Players Comparison */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
        {/* Player Out */}
        <div className="themed-bg-tertiary p-4 rounded-lg">
          <div className="text-center">
            <div className="text-sm themed-text-tertiary mb-1">Trading Out</div>
            <div className="font-semibold themed-text-primary">{recommendation.playerOut.name}</div>
            <div className="text-sm themed-text-secondary">{recommendation.playerOut.position} - {recommendation.playerOut.team}</div>
            <div className="mt-2 space-y-1">
              <div className="text-xs themed-text-tertiary">Price: ${recommendation.playerOut.price.toLocaleString()}</div>
              <div className="text-xs themed-text-tertiary">Points: {recommendation.playerOut.points}</div>
              <div className="text-xs themed-text-tertiary">Form: {recommendation.playerOut.form}/10</div>
            </div>
          </div>
        </div>

        {/* Arrow */}
        <div className="flex justify-center">
          <ArrowRightIcon className="w-8 h-8 text-green-400" />
        </div>

        {/* Player In */}
        <div className="themed-bg-tertiary p-4 rounded-lg border-2 border-green-500/20">
          <div className="text-center">
            <div className="text-sm text-green-400 mb-1">Trading In</div>
            <div className="font-semibold themed-text-primary">{recommendation.playerIn.name}</div>
            <div className="text-sm themed-text-secondary">{recommendation.playerIn.position} - {recommendation.playerIn.team}</div>
            <div className="mt-2 space-y-1">
              <div className="text-xs themed-text-tertiary">Price: ${recommendation.playerIn.price.toLocaleString()}</div>
              <div className="text-xs themed-text-tertiary">Points: {recommendation.playerIn.points}</div>
              <div className="text-xs themed-text-tertiary">Form: {recommendation.playerIn.form}/10</div>
            </div>
          </div>
        </div>
      </div>

      {/* Trade Impact */}
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center">
          <div className="text-sm themed-text-tertiary">Price Change</div>
          <div className={`text-lg font-semibold ${recommendation.priceChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {recommendation.priceChange >= 0 ? '+' : ''}${recommendation.priceChange.toLocaleString()}
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm themed-text-tertiary">Expected Points Gain</div>
          <div className={`text-lg font-semibold ${recommendation.pointsGain >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {recommendation.pointsGain >= 0 ? '+' : ''}{recommendation.pointsGain}
          </div>
        </div>
      </div>

      {/* AI Reasoning */}
      <div className="themed-bg-secondary p-4 rounded-lg">
        <div className="flex items-start space-x-2">
          <ChartBarIcon className="w-5 h-5 text-blue-400 mt-0.5" />
          <div>
            <div className="text-sm font-medium themed-text-primary mb-1">AI Analysis</div>
            <div className="text-sm themed-text-tertiary">{recommendation.reason}</div>
          </div>
        </div>
      </div>

      {/* Confidence Score */}
      <div className="flex items-center justify-between">
        <span className="text-sm themed-text-secondary">Confidence Score</span>
        <div className="flex items-center space-x-2">
          <div className="w-24 h-2 themed-bg-tertiary rounded-full overflow-hidden">
            <div 
              className="h-full bg-green-400 transition-all duration-500"
              style={{ width: `${recommendation.confidence}%` }}
            />
          </div>
          <span className="text-sm font-medium text-green-400">{recommendation.confidence}%</span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3 pt-4">
        <button
          type="button" onClick={handleAnalyze}
          disabled={isAnalyzing || analysisComplete}
          className="flex-1 btn-secondary btn-ripple flex items-center justify-center space-x-2"
        >
          {isAnalyzing ? (
            <>
              <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
              <span>Analyzing...</span>
            </>
          ) : analysisComplete ? (
            <>
              <CheckCircleIcon className="w-4 h-4" />
              <span>Analysis Complete</span>
            </>
          ) : (
            <>
              <ChartBarIcon className="w-4 h-4" />
              <span>Deep Analysis</span>
            </>
          )}
        </button>

        <button
          type="button" onClick={handleExecuteTrade}
          className="flex-1 btn-primary btn-ripple flex items-center justify-center space-x-2"
        >
          <ArrowTrendingUpIcon className="w-4 h-4" />
          <span>Execute Trade</span>
        </button>

        <button
          type="button" onClick={() => onDismiss(recommendation.id)}
          className="px-4 py-2 themed-border rounded-lg themed-text-tertiary hover:themed-text-primary transition-colors"
        >
          <XMarkIcon className="w-4 h-4" />
        </button>
      </div>

      {/* Detailed Analysis */}
      <AnimatePresence>
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-4 pt-4 border-t themed-border"
          >
            <h4 className="font-semibold themed-text-primary">Detailed Analysis</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="text-sm font-medium themed-text-secondary">Performance Metrics</div>
                <div className="space-y-1 text-xs themed-text-tertiary">
                  <div>• Recent form trending upward</div>
                  <div>• Favorable upcoming fixtures</div>
                  <div>• Low ownership percentage (differential)</div>
                  <div>• Strong breakeven position</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium themed-text-secondary">Risk Factors</div>
                <div className="space-y-1 text-xs themed-text-tertiary">
                  <div>• Injury history consideration</div>
                  <div>• Team rotation risk</div>
                  <div>• Price volatility assessment</div>
                  <div>• Competition for position</div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default TradeAnalysis;
