/**
 * Direct Supabase Connection Test
 * Test the Supabase connection and check what player data we have
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://fuxpdgsixnbbsdspusmp.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1MDgwNTYsImV4cCI6MjA2NjA4NDA1Nn0.ifF15ZorqCzGZvL-buLhm7t51T0MWtV-9EPSNotzqaA';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDUwODA1NiwiZXhwIjoyMDY2MDg0MDU2fQ.f5qZOHQlAKz_sbIwoGmY4Gub8bGHyx_jxlRYZk-1VPg';

// Create clients
const supabase = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function testSupabaseConnection() {
  console.log('🔍 TESTING SUPABASE CONNECTION');
  console.log('==============================');
  console.log(`URL: ${supabaseUrl}`);
  console.log(`Using updated API keys...`);
  
  try {
    // Test different possible table names
    const tablesToTest = [
      'players',
      'nrl_players', 
      'nrl_player_stats',
      'nrl_teams',
      'supercoach_players',
      'player_data'
    ];
    
    const results = {};
    
    for (const tableName of tablesToTest) {
      console.log(`\n🔍 Testing table: ${tableName}`);
      
      try {
        // Test with anon key first
        const { count, error: countError } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true });
        
        if (countError) {
          console.log(`   ❌ Anon access failed: ${countError.message}`);
          
          // Try with service key
          const { count: adminCount, error: adminCountError } = await supabaseAdmin
            .from(tableName)
            .select('*', { count: 'exact', head: true });
          
          if (adminCountError) {
            console.log(`   ❌ Admin access failed: ${adminCountError.message}`);
            results[tableName] = { count: 0, error: adminCountError.message, exists: false };
          } else {
            console.log(`   ✅ Admin access: ${adminCount} records`);
            results[tableName] = { count: adminCount, error: null, exists: true, access: 'admin_only' };
          }
        } else {
          console.log(`   ✅ Anon access: ${count} records`);
          results[tableName] = { count, error: null, exists: true, access: 'public' };
        }
        
        // Get sample data if table exists and has data
        if (results[tableName].exists && results[tableName].count > 0) {
          const client = results[tableName].access === 'admin_only' ? supabaseAdmin : supabase;
          
          const { data: sampleData, error: sampleError } = await client
            .from(tableName)
            .select('*')
            .limit(3);
          
          if (!sampleError && sampleData && sampleData.length > 0) {
            console.log(`   📋 Sample record:`, JSON.stringify(sampleData[0], null, 2));
            results[tableName].sample = sampleData[0];
            results[tableName].columns = Object.keys(sampleData[0]);
          }
        }
        
      } catch (error) {
        console.log(`   ❌ Table test failed: ${error.message}`);
        results[tableName] = { count: 0, error: error.message, exists: false };
      }
    }
    
    console.log('\n📊 SUPABASE DATA ANALYSIS');
    console.log('=========================');
    
    let largestTable = null;
    let largestCount = 0;
    let totalPlayers = 0;
    
    Object.entries(results).forEach(([tableName, result]) => {
      if (result.exists) {
        console.log(`✅ ${tableName}: ${result.count} records (${result.access || 'unknown'} access)`);
        totalPlayers += result.count;
        
        if (result.count > largestCount) {
          largestCount = result.count;
          largestTable = tableName;
        }
      } else {
        console.log(`❌ ${tableName}: Not found or no access`);
      }
    });
    
    console.log('\n🎯 ANALYSIS RESULTS');
    console.log('==================');
    console.log(`Largest table: ${largestTable} (${largestCount} records)`);
    console.log(`Total player records found: ${totalPlayers}`);
    console.log(`Expected: 581 NRL players`);
    console.log(`Data completeness: ${largestCount >= 500 ? '✅ Complete' : '⚠️ Incomplete'}`);
    
    if (largestTable && results[largestTable].sample) {
      console.log('\n📋 RECOMMENDED DATA SOURCE');
      console.log('==========================');
      console.log(`Table: ${largestTable}`);
      console.log(`Columns:`, results[largestTable].columns.join(', '));
      console.log(`Sample data structure:`, JSON.stringify(results[largestTable].sample, null, 2));
    }
    
    console.log('\n🚀 INTEGRATION RECOMMENDATIONS');
    console.log('==============================');
    
    if (largestCount >= 500) {
      console.log(`✅ Use '${largestTable}' table as primary data source`);
      console.log(`✅ Update PlayerService.getAllPlayers() to query '${largestTable}'`);
      console.log(`✅ This should resolve the 114 vs 581 player count issue`);
    } else if (totalPlayers >= 500) {
      console.log(`⚠️ Data is spread across multiple tables`);
      console.log(`⚠️ Consider combining data from multiple sources`);
    } else {
      console.log(`❌ Insufficient player data found`);
      console.log(`❌ May need to import/populate Supabase with complete dataset`);
    }
    
    return {
      success: true,
      tables: results,
      largestTable,
      largestCount,
      totalPlayers,
      dataComplete: largestCount >= 500
    };
    
  } catch (error) {
    console.error('❌ Supabase connection test failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run the test
if (require.main === module) {
  testSupabaseConnection()
    .then(result => {
      if (result.success && result.dataComplete) {
        console.log('\n🎉 SUCCESS! Ready to integrate Supabase data');
        console.log(`   - Update code to use '${result.largestTable}' table`);
        console.log(`   - Players page will show ${result.largestCount} players`);
        console.log(`   - Search will work across complete dataset`);
      } else if (result.success) {
        console.log('\n⚠️ PARTIAL SUCCESS - Data needs attention');
        console.log(`   - Found ${result.totalPlayers} total players`);
        console.log(`   - Expected 581 players`);
        console.log(`   - May need data import or table consolidation`);
      } else {
        console.log('\n❌ FAILED - Connection or access issues');
        console.log(`   - Error: ${result.error}`);
        console.log(`   - Check credentials and table permissions`);
      }
    })
    .catch(console.error);
}

module.exports = { testSupabaseConnection };
