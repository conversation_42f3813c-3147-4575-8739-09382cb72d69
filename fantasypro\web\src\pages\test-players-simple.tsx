import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';

interface Player {
  id: string;
  name: string;
  team: string;
  position: string;
  price: number;
  points: number;
  average: number;
  form: number;
}

const TestPlayersSimple: NextPage = () => {
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPlayers = async () => {
      try {
        console.log('🔄 Fetching players...');
        
        // Try the API endpoint
        const response = await fetch('/api/test-player-count');
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('📊 API Response:', data);
        
        if (data.success && data.integration_test) {
          // Create mock players for testing
          const mockPlayers: Player[] = Array.from({ length: 10 }, (_, i) => ({
            id: (i + 1).toString(),
            name: `Test Player ${i + 1}`,
            team: `Team ${i % 4 + 1}`,
            position: ['FLB', 'HFB', 'CTW', 'FRF'][i % 4],
            price: 300000 + (i * 50000),
            points: 800 + (i * 100),
            average: 50 + (i * 5),
            form: 5 + (i % 5)
          }));
          
          setPlayers(mockPlayers);
          console.log(`✅ Loaded ${mockPlayers.length} test players`);
        } else {
          throw new Error('Invalid API response structure');
        }
        
      } catch (err) {
        console.error('❌ Error fetching players:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchPlayers();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p className="text-white">Loading players...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6">
            <h1 className="text-xl font-bold text-red-400 mb-4">Error Loading Players</h1>
            <p className="text-red-200 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Head>
        <title>Simple Players Test - FantasyPro</title>
      </Head>

      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-4">Simple Players Test</h1>
          <p className="text-gray-300">Testing basic player data loading without complex components</p>
        </div>

        <div className="bg-slate-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-white">Players Loaded</h2>
            <span className="bg-green-600 text-white px-3 py-1 rounded-full text-sm">
              {players.length} players
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {players.map((player) => (
              <div
                key={player.id}
                className="bg-slate-700 rounded-lg p-4 hover:bg-slate-600 transition-colors"
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-white">{player.name}</h3>
                  <span className="text-xs bg-blue-600 text-white px-2 py-1 rounded">
                    {player.position}
                  </span>
                </div>
                
                <div className="text-sm text-gray-300 space-y-1">
                  <div>Team: {player.team}</div>
                  <div>Price: ${(player.price / 1000000).toFixed(2)}M</div>
                  <div>Points: {player.points}</div>
                  <div>Average: {player.average.toFixed(1)}</div>
                  <div>Form: {player.form.toFixed(1)}</div>
                </div>
              </div>
            ))}
          </div>

          {players.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-400">No players loaded</p>
            </div>
          )}
        </div>

        <div className="mt-8 bg-slate-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Debug Information</h3>
          <div className="text-sm text-gray-300 space-y-2">
            <div>Players count: {players.length}</div>
            <div>Loading state: {loading ? 'true' : 'false'}</div>
            <div>Error state: {error || 'none'}</div>
            <div>Timestamp: {new Date().toISOString()}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestPlayersSimple;
