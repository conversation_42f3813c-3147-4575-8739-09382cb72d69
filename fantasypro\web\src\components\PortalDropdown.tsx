import React, { useEffect, useState, useRef } from 'react';
import { createPortal } from 'react-dom';

interface PortalDropdownProps {
  isOpen: boolean;
  children: React.ReactNode;
  targetRef: React.RefObject<HTMLElement>;
  className?: string;
  style?: React.CSSProperties;
}

const PortalDropdown: React.FC<PortalDropdownProps> = ({
  isOpen,
  children,
  targetRef,
  className = '',
  style = {}
}) => {
  const [portalElement, setPortalElement] = useState<HTMLElement | null>(null);
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Create portal element
  useEffect(() => {
    const element = document.createElement('div');
    element.id = 'dropdown-portal';
    element.style.position = 'absolute';
    element.style.top = '0';
    element.style.left = '0';
    element.style.zIndex = '9999';
    element.style.pointerEvents = 'none';
    
    document.body.appendChild(element);
    setPortalElement(element);

    return () => {
      if (document.body.contains(element)) {
        document.body.removeChild(element);
      }
    };
  }, []);

  // Update position when dropdown opens or window resizes
  useEffect(() => {
    if (!isOpen || !targetRef.current || !portalElement) return;

    const updatePosition = () => {
      const targetRect = targetRef.current!.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      setPosition({
        top: targetRect.bottom + scrollTop + 8, // 8px gap
        left: targetRect.left + scrollLeft,
        width: targetRect.width
      });
    };

    updatePosition();

    // Update position on scroll and resize
    const handleScroll = () => updatePosition();
    const handleResize = () => updatePosition();

    window.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen, targetRef, portalElement]);

  // Enable pointer events when open
  useEffect(() => {
    if (portalElement) {
      portalElement.style.pointerEvents = isOpen ? 'auto' : 'none';
    }
  }, [isOpen, portalElement]);

  if (!portalElement || !isOpen) return null;

  return createPortal(
    <div
      ref={dropdownRef}
      className={className}
      style={{
        position: 'absolute',
        top: position.top,
        left: position.left,
        width: position.width,
        zIndex: 9999,
        ...style
      }}
    >
      {children}
    </div>,
    portalElement
  );
};

export default PortalDropdown;
