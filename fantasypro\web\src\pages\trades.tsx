import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import { motion } from 'framer-motion';
import Layout from '../components/Layout';
import TradeAnalysis from '../components/TradeAnalysis';
import UniversalSearch from '../components/UniversalSearch';
import {
  ArrowsRightLeftIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
  XMarkIcon,
  FireIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  StarIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

interface Trade {
  id: string;
  player_out: {
    name: string;
    team: string;
    position: string;
    price: number;
    points: number;
  };
  player_in: {
    name: string;
    team: string;
    position: string;
    price: number;
    points: number;
  };
  expected_gain: number;
  confidence: number;
  risk_level: 'low' | 'medium' | 'high';
  reasoning: string[];
  created_at: string;
  status: 'pending' | 'executed' | 'cancelled';
}

const Trades: NextPage = () => {
  const [trades, setTrades] = useState<Trade[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTrade, setSelectedTrade] = useState<Trade | null>(null);
  const [showTradeAnalysis, setShowTradeAnalysis] = useState(false);
  const [selectedPlayerOut, setSelectedPlayerOut] = useState<any>(null);
  const [selectedPlayerIn, setSelectedPlayerIn] = useState<any>(null);
  const [filter, setFilter] = useState('all');

  useEffect(() => {
    const fetchTrades = async () => {
      try {
        // Simulate API call - replace with real data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockTrades: Trade[] = [
          {
            id: '1',
            player_out: {
              name: 'Ryan Papenhuyzen',
              team: 'Melbourne Storm',
              position: 'FLB',
              price: 760900,
              points: 456
            },
            player_in: {
              name: 'Scott Drinkwater',
              team: 'North Queensland Cowboys',
              position: 'FLB',
              price: 708100,
              points: 1016
            },
            expected_gain: 45.2,
            confidence: 82,
            risk_level: 'low',
            reasoning: [
              'Drinkwater has better recent form (78.2 avg vs 45.6)',
              'Saves $52.8k in salary cap space',
              'Cowboys have easier upcoming fixtures',
              'Papenhuyzen injury concerns'
            ],
            created_at: '2024-06-22T10:30:00Z',
            status: 'pending'
          },
          {
            id: '2',
            player_out: {
              name: 'Tino Fa\'asuamaleaui',
              team: 'Gold Coast Titans',
              position: '2RF',
              price: 699900,
              points: 623
            },
            player_in: {
              name: 'Terrell May',
              team: 'Wests Tigers',
              position: 'FRF',
              price: 832800,
              points: 1134
            },
            expected_gain: 38.7,
            confidence: 76,
            risk_level: 'medium',
            reasoning: [
              'May has exceptional form (87.2 avg)',
              'Tigers forwards getting more opportunities',
              'Tino has been inconsistent this season',
              'Position flexibility with May'
            ],
            created_at: '2024-06-22T09:15:00Z',
            status: 'pending'
          },
          {
            id: '3',
            player_out: {
              name: 'Nicholas Hynes',
              team: 'Cronulla Sharks',
              position: 'HFB',
              price: 619000,
              points: 969
            },
            player_in: {
              name: 'Jayden Campbell',
              team: 'Gold Coast Titans',
              position: '5/8',
              price: 643800,
              points: 713
            },
            expected_gain: -12.3,
            confidence: 45,
            risk_level: 'high',
            reasoning: [
              'Campbell has higher ceiling potential',
              'Hynes form has been declining',
              'Titans attacking structure improving',
              'Risk: Campbell injury prone'
            ],
            created_at: '2024-06-21T16:45:00Z',
            status: 'cancelled'
          }
        ];

        setTrades(mockTrades);
      } catch (error) {
        console.error('Error fetching trades:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTrades();
  }, []);

  const filteredTrades = trades.filter(trade => {
    if (filter === 'all') return true;
    return trade.status === filter;
  });

  const formatCurrency = (amount: number) => {
    return `$${(amount / 1000000).toFixed(2)}M`;
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low':
        return 'text-green-400 bg-green-900/20';
      case 'medium':
        return 'text-yellow-400 bg-yellow-900/20';
      case 'high':
        return 'text-red-400 bg-red-900/20';
      default:
        return 'text-slate-400 bg-slate-900/20';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-blue-400 bg-blue-900/20';
      case 'executed':
        return 'text-green-400 bg-green-900/20';
      case 'cancelled':
        return 'text-red-400 bg-red-900/20';
      default:
        return 'text-slate-400 bg-slate-900/20';
    }
  };

  const handleExecuteTrade = (trade: Trade) => {
    setTrades(trades.map(t => 
      t.id === trade.id ? { ...t, status: 'executed' as const } : t
    ));
    alert(`Trade executed: ${trade.player_out.name} → ${trade.player_in.name}`);
  };

  const handleCancelTrade = (trade: Trade) => {
    setTrades(trades.map(t => 
      t.id === trade.id ? { ...t, status: 'cancelled' as const } : t
    ));
  };

  const handleCreateCustomTrade = () => {
    if (selectedPlayerOut && selectedPlayerIn) {
      setShowTradeAnalysis(true);
    } else {
      alert('Please select both players to trade');
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Trades - FantasyPro</title>
        <meta name="description" content="AI-powered trade recommendations and analysis for NRL SuperCoach" />
      </Head>

      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold themed-text-primary">Trades</h1>
            <p className="themed-text-tertiary mt-1">AI-powered trade recommendations and analysis</p>
          </div>
          <div className="flex items-center space-x-4">
            <select
              className="input-primary"
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
            >
              <option value="all">All Trades</option>
              <option value="pending">Pending</option>
              <option value="executed">Executed</option>
              <option value="cancelled">Cancelled</option>
            </select>
            <span className="status-live">{filteredTrades.length} Trades</span>
          </div>
        </div>

        {/* Custom Trade Builder */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="card-premium p-6"
        >
          <div className="flex items-center space-x-3 mb-6">
            <ArrowsRightLeftIcon className="w-6 h-6 text-blue-400" />
            <h2 className="text-xl font-semibold themed-text-primary">Create Custom Trade</h2>
            <span className="status-live">AI Analysis</span>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">
                Player to Trade Out
              </label>
              <UniversalSearch
                placeholder="Search player to trade out..."
                onPlayerSelect={setSelectedPlayerOut}
                maxResults={6}
              />
              {selectedPlayerOut && (
                <div className="mt-3 p-3 bg-red-500/10 rounded-lg border border-red-500/20">
                  <div className="font-medium themed-text-primary">{selectedPlayerOut.name}</div>
                  <div className="text-sm themed-text-tertiary">{selectedPlayerOut.team} • {selectedPlayerOut.position}</div>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">
                Player to Trade In
              </label>
              <UniversalSearch
                placeholder="Search player to trade in..."
                onPlayerSelect={setSelectedPlayerIn}
                maxResults={6}
              />
              {selectedPlayerIn && (
                <div className="mt-3 p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                  <div className="font-medium themed-text-primary">{selectedPlayerIn.name}</div>
                  <div className="text-sm themed-text-tertiary">{selectedPlayerIn.team} • {selectedPlayerIn.position}</div>
                </div>
              )}
            </div>
          </div>

          <div className="mt-6 text-center">
            <button
              type="button" onClick={handleCreateCustomTrade}
              className="btn-primary btn-ripple"
              disabled={!selectedPlayerOut || !selectedPlayerIn}
            >
              <ArrowsRightLeftIcon className="w-4 h-4 mr-2" />
              Analyze Trade
            </button>
          </div>
        </motion.div>

        {/* Trade Recommendations */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="card"
        >
          <div className="p-6 border-b themed-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <FireIcon className="w-5 h-5 text-orange-400" />
                <h2 className="text-lg font-semibold themed-text-primary">AI Trade Recommendations</h2>
                <span className="text-xs text-orange-400">
                  {filteredTrades.length} recommendations
                </span>
              </div>
              <div className="text-sm themed-text-tertiary">
                Click trades to view detailed analysis
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              {filteredTrades.map((trade, index) => (
                <motion.div
                  key={trade.id}
                  className="card-hover p-6 cursor-pointer"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  onClick={() => setSelectedTrade(trade)}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="text-center">
                        <div className="font-semibold themed-text-primary text-sm">{trade.player_out.name}</div>
                        <div className="text-xs themed-text-tertiary">{trade.player_out.team}</div>
                        <div className="text-xs text-red-400 mt-1">OUT</div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                          <ArrowsRightLeftIcon className="w-5 h-5 text-blue-400" />
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold themed-text-primary text-sm">{trade.player_in.name}</div>
                        <div className="text-xs themed-text-tertiary">{trade.player_in.team}</div>
                        <div className="text-xs text-green-400 mt-1">IN</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-bold ${trade.expected_gain >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {trade.expected_gain >= 0 ? '+' : ''}{trade.expected_gain.toFixed(1)}
                      </div>
                      <div className="text-xs themed-text-tertiary">Expected gain</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-sm themed-text-secondary">Confidence</div>
                      <div className="text-lg font-semibold themed-text-primary">{trade.confidence}%</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm themed-text-secondary">Risk Level</div>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getRiskColor(trade.risk_level)}`}>
                        {trade.risk_level.toUpperCase()}
                      </span>
                    </div>
                    <div className="text-center">
                      <div className="text-sm themed-text-secondary">Price Diff</div>
                      <div className={`text-sm font-medium ${trade.player_in.price > trade.player_out.price ? 'text-red-400' : 'text-green-400'}`}>
                        {trade.player_in.price > trade.player_out.price ? '+' : ''}
                        {formatCurrency(trade.player_in.price - trade.player_out.price)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm themed-text-secondary">Status</div>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(trade.status)}`}>
                        {trade.status.toUpperCase()}
                      </span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <div className="text-sm themed-text-secondary mb-2">Key Reasoning:</div>
                    <div className="text-xs themed-text-tertiary">
                      {trade.reasoning.slice(0, 2).join(' • ')}
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t themed-border">
                    <div className="text-xs themed-text-tertiary">
                      Created {new Date(trade.created_at).toLocaleDateString()}
                    </div>
                    <div className="flex space-x-2">
                      {trade.status === 'pending' && (
                        <>
                          <button
                            type="button" onClick={(e) => {
                              e.stopPropagation();
                              handleExecuteTrade(trade);
                            }}
                            className="btn-primary btn-ripple text-xs py-1 px-3"
                          >
                            <CheckCircleIcon className="w-3 h-3 mr-1" />
                            Execute
                          </button>
                          <button
                            type="button" onClick={(e) => {
                              e.stopPropagation();
                              handleCancelTrade(trade);
                            }}
                            className="btn-outline text-xs py-1 px-3"
                          >
                            <XMarkIcon className="w-3 h-3 mr-1" />
                            Cancel
                          </button>
                        </>
                      )}
                      {trade.status === 'executed' && (
                        <span className="text-xs text-green-400 flex items-center">
                          <CheckCircleIcon className="w-3 h-3 mr-1" />
                          Executed
                        </span>
                      )}
                      {trade.status === 'cancelled' && (
                        <span className="text-xs text-red-400 flex items-center">
                          <XMarkIcon className="w-3 h-3 mr-1" />
                          Cancelled
                        </span>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {filteredTrades.length === 0 && (
              <div className="text-center py-12 themed-text-tertiary">
                <ArrowsRightLeftIcon className="w-12 h-12 mx-auto mb-4 text-slate-600" />
                <div className="text-lg font-medium mb-2">No trades found</div>
                <div className="text-sm mb-4">Try adjusting your filter or create a custom trade</div>
                <button
                  type="button" onClick={() => setFilter('all')}
                  className="btn-primary"
                >
                  Show All Trades
                </button>
              </div>
            )}
          </div>
        </motion.div>

        {/* Selected Trade Details */}
        {selectedTrade && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="card-premium p-6"
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <StarIcon className="w-6 h-6 text-yellow-400" />
                <h2 className="text-xl font-semibold themed-text-primary">Trade Analysis</h2>
              </div>
              <button
                type="button" onClick={() => setSelectedTrade(null)}
                className="btn-outline"
              >
                Close
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold themed-text-primary mb-4">Trade Details</h3>
                <div className="space-y-4">
                  <div className="p-4 bg-red-500/10 rounded-lg border border-red-500/20">
                    <div className="font-semibold themed-text-primary mb-2">Trading Out</div>
                    <div className="text-sm space-y-1">
                      <div><strong>Player:</strong> {selectedTrade.player_out.name}</div>
                      <div><strong>Team:</strong> {selectedTrade.player_out.team}</div>
                      <div><strong>Position:</strong> {selectedTrade.player_out.position}</div>
                      <div><strong>Price:</strong> {formatCurrency(selectedTrade.player_out.price)}</div>
                      <div><strong>Points:</strong> {selectedTrade.player_out.points.toLocaleString()}</div>
                    </div>
                  </div>

                  <div className="p-4 bg-green-500/10 rounded-lg border border-green-500/20">
                    <div className="font-semibold themed-text-primary mb-2">Trading In</div>
                    <div className="text-sm space-y-1">
                      <div><strong>Player:</strong> {selectedTrade.player_in.name}</div>
                      <div><strong>Team:</strong> {selectedTrade.player_in.team}</div>
                      <div><strong>Position:</strong> {selectedTrade.player_in.position}</div>
                      <div><strong>Price:</strong> {formatCurrency(selectedTrade.player_in.price)}</div>
                      <div><strong>Points:</strong> {selectedTrade.player_in.points.toLocaleString()}</div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold themed-text-primary mb-4">AI Analysis</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 card-hover">
                      <div className="text-sm themed-text-secondary">Expected Gain</div>
                      <div className={`text-xl font-bold ${selectedTrade.expected_gain >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {selectedTrade.expected_gain >= 0 ? '+' : ''}{selectedTrade.expected_gain.toFixed(1)}
                      </div>
                    </div>
                    <div className="text-center p-3 card-hover">
                      <div className="text-sm themed-text-secondary">Confidence</div>
                      <div className="text-xl font-bold themed-text-primary">{selectedTrade.confidence}%</div>
                    </div>
                  </div>

                  <div>
                    <div className="text-sm themed-text-secondary mb-2">Reasoning:</div>
                    <ul className="space-y-2">
                      {selectedTrade.reasoning.map((reason, index) => (
                        <li key={index} className="text-sm themed-text-tertiary flex items-start">
                          <span className="text-green-400 mr-2">•</span>
                          {reason}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {selectedTrade.status === 'pending' && (
                    <div className="flex space-x-3 pt-4">
                      <button
                        type="button" onClick={() => handleExecuteTrade(selectedTrade)}
                        className="btn-primary btn-ripple flex-1"
                      >
                        <CheckCircleIcon className="w-4 h-4 mr-2" />
                        Execute Trade
                      </button>
                      <button
                        type="button" onClick={() => handleCancelTrade(selectedTrade)}
                        className="btn-outline flex-1"
                      >
                        <XMarkIcon className="w-4 h-4 mr-2" />
                        Cancel Trade
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Trade Analysis Modal */}
        {showTradeAnalysis && selectedPlayerOut && selectedPlayerIn && (
          <TradeAnalysis
            player={selectedPlayerOut}
            onClose={() => {
              setShowTradeAnalysis(false);
              setSelectedPlayerOut(null);
              setSelectedPlayerIn(null);
            }}
            onExecuteTrade={(tradeData) => {
              console.log('Custom trade executed:', tradeData);
              alert('Custom trade analysis complete!');
              setShowTradeAnalysis(false);
              setSelectedPlayerOut(null);
              setSelectedPlayerIn(null);
            }}
          />
        )}
      </div>
    </Layout>
  );
};

export default Trades;
