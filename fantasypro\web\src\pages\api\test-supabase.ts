import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../services/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Testing Supabase connection and data...');
    
    // Test connection
    const { data: connectionTest, error: connectionError } = await supabase
      .from('players')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      console.error('❌ Supabase connection failed:', connectionError);
      return res.status(500).json({
        success: false,
        error: 'Supabase connection failed',
        details: connectionError.message
      });
    }
    
    console.log('✅ Supabase connection successful');
    
    // Get player count
    const { count: playerCount, error: countError } = await supabase
      .from('players')
      .select('*', { count: 'exact', head: true });
    
    // Get sample players
    const { data: samplePlayers, error: sampleError } = await supabase
      .from('players')
      .select('*')
      .limit(10)
      .order('points', { ascending: false });
    
    // Check for other possible table names
    const tableTests = [];
    
    // Test nrl_players table
    const { count: nrlPlayersCount, error: nrlPlayersError } = await supabase
      .from('nrl_players')
      .select('*', { count: 'exact', head: true });
    
    tableTests.push({
      table: 'nrl_players',
      count: nrlPlayersCount,
      error: nrlPlayersError?.message
    });
    
    // Test nrl_player_stats table
    const { count: nrlStatsCount, error: nrlStatsError } = await supabase
      .from('nrl_player_stats')
      .select('*', { count: 'exact', head: true });
    
    tableTests.push({
      table: 'nrl_player_stats',
      count: nrlStatsCount,
      error: nrlStatsError?.message
    });
    
    // Test nrl_teams table
    const { count: nrlTeamsCount, error: nrlTeamsError } = await supabase
      .from('nrl_teams')
      .select('*', { count: 'exact', head: true });
    
    tableTests.push({
      table: 'nrl_teams',
      count: nrlTeamsCount,
      error: nrlTeamsError?.message
    });
    
    // Get sample from the largest table
    let largestTable = 'players';
    let largestCount = playerCount || 0;
    
    tableTests.forEach(test => {
      if (test.count && test.count > largestCount) {
        largestTable = test.table;
        largestCount = test.count;
      }
    });
    
    let sampleFromLargest = [];
    if (largestTable !== 'players') {
      const { data: largestSample, error: largestError } = await supabase
        .from(largestTable)
        .select('*')
        .limit(5);
      
      if (!largestError) {
        sampleFromLargest = largestSample || [];
      }
    }
    
    const result = {
      success: true,
      connection: 'successful',
      tables: {
        players: {
          count: playerCount,
          error: countError?.message,
          sample: samplePlayers?.slice(0, 3) || []
        },
        ...tableTests.reduce((acc, test) => {
          acc[test.table] = {
            count: test.count,
            error: test.error
          };
          return acc;
        }, {})
      },
      largest_table: {
        name: largestTable,
        count: largestCount,
        sample: largestTable !== 'players' ? sampleFromLargest.slice(0, 3) : []
      },
      analysis: {
        total_players_found: Math.max(playerCount || 0, largestCount),
        expected_players: 581,
        data_complete: largestCount >= 500,
        recommended_table: largestCount >= 500 ? largestTable : 'Need data import',
        issues: []
      },
      timestamp: new Date().toISOString()
    };
    
    // Add analysis issues
    if (largestCount < 500) {
      result.analysis.issues.push('Player count below expected 581 - may need data import');
    }
    
    if (largestCount === 0) {
      result.analysis.issues.push('No player data found in any table - database may be empty');
    }
    
    if (largestTable !== 'players') {
      result.analysis.issues.push(`Main data is in '${largestTable}' table, not 'players' table`);
    }
    
    console.log(`📊 Supabase Analysis Complete:`);
    console.log(`   - Largest table: ${largestTable} (${largestCount} records)`);
    console.log(`   - Expected: 581 players`);
    console.log(`   - Data complete: ${result.analysis.data_complete}`);
    
    return res.status(200).json(result);
    
  } catch (error) {
    console.error('❌ Supabase test error:', error);
    return res.status(500).json({
      success: false,
      error: 'Test failed',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
