"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/globals.css":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/globals.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_index_js_ruleSet_1_rules_7_oneOf_14_use_1_node_modules_next_dist_build_webpack_loaders_postcss_loader_src_index_js_ruleSet_1_rules_7_oneOf_14_use_2_themes_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! -!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./themes.css */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/themes.css\");\n// Imports\n\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n___CSS_LOADER_EXPORT___.i(_node_modules_next_dist_build_webpack_loaders_css_loader_src_index_js_ruleSet_1_rules_7_oneOf_14_use_1_node_modules_next_dist_build_webpack_loaders_postcss_loader_src_index_js_ruleSet_1_rules_7_oneOf_14_use_2_themes_css__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */ /* 3 */\\n  tab-size: 4; /* 3 */\\n  font-family: Inter, system-ui, sans-serif; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: JetBrains Mono, Menlo, Monaco, monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n\\n[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {\\n  appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n\\ninput::placeholder,textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\n::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n\\n::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n\\n::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n\\n::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n\\nselect {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\");\\n  background-position: right 0.5rem center;\\n  background-repeat: no-repeat;\\n  background-size: 1.5em 1.5em;\\n  padding-right: 2.5rem;\\n  print-color-adjust: exact;\\n}\\n\\n[multiple],[size]:where(select:not([size=\\\"1\\\"])) {\\n  background-image: initial;\\n  background-position: initial;\\n  background-repeat: unset;\\n  background-size: initial;\\n  padding-right: 0.75rem;\\n  print-color-adjust: unset;\\n}\\n\\n[type='checkbox'],[type='radio'] {\\n  appearance: none;\\n  padding: 0;\\n  print-color-adjust: exact;\\n  display: inline-block;\\n  vertical-align: middle;\\n  background-origin: border-box;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  flex-shrink: 0;\\n  height: 1rem;\\n  width: 1rem;\\n  color: #2563eb;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='checkbox'] {\\n  border-radius: 0px;\\n}\\n\\n[type='radio'] {\\n  border-radius: 100%;\\n}\\n\\n[type='checkbox']:focus,[type='radio']:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n}\\n\\n[type='checkbox']:checked,[type='radio']:checked {\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n[type='checkbox']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:checked {\\n    appearance: auto;\\n  }\\n}\\n\\n[type='radio']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='radio']:checked {\\n    appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='checkbox']:indeterminate {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e\\\");\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:indeterminate {\\n    appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='file'] {\\n  background: unset;\\n  border-color: inherit;\\n  border-width: 0;\\n  border-radius: 0;\\n  padding: 0;\\n  font-size: unset;\\n  line-height: inherit;\\n}\\n\\n[type='file']:focus {\\n  outline: 1px solid ButtonText;\\n  outline: 1px auto -webkit-focus-ring-color;\\n}\\n  html {\\n    font-family: 'Inter', system-ui, sans-serif;\\n    scroll-behavior: smooth;\\n  }\\n  \\n  body {\\n    background-color: var(--bg-primary);\\n    color: var(--text-primary);\\n    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';\\n    transition: var(--theme-transition);\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n\\n  /* Custom scrollbar */\\n  ::-webkit-scrollbar {\\n    width: 6px;\\n    height: 6px;\\n  }\\n\\n  ::-webkit-scrollbar-track {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n}\\n\\n  ::-webkit-scrollbar-thumb {\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\\n}\\n\\n  ::-webkit-scrollbar-thumb:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));\\n}\\n\\n  /* Firefox scrollbar */\\n  * {\\n    scrollbar-width: thin;\\n    scrollbar-color: #475569 #1e293b;\\n  }\\n.container {\\n  width: 100%;\\n}\\n@media (min-width: 640px) {\\n\\n  .container {\\n    max-width: 640px;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .container {\\n    max-width: 768px;\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .container {\\n    max-width: 1024px;\\n  }\\n}\\n@media (min-width: 1280px) {\\n\\n  .container {\\n    max-width: 1280px;\\n  }\\n}\\n@media (min-width: 1536px) {\\n\\n  .container {\\n    max-width: 1536px;\\n  }\\n}\\n/* Enhanced Card components with theme-aware immersive effects */\\n.card {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    border-radius: 0.5rem;\\n    transition: var(--theme-transition);\\n  }\\n.card-hover {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    border-radius: 0.5rem;\\n    transform: translateY(0);\\n    transition: var(--theme-transition);\\n  }\\n.card-hover:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n/* Immersive card with glow effect */\\n.card-glow {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    border-radius: 0.5rem;\\n    transform: translateY(0);\\n    transition: var(--theme-transition);\\n  }\\n.card-glow:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n.card-glow {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.card-glow::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);\\n    transition: left 0.5s;\\n  }\\n.card-glow:hover::before {\\n    left: 100%;\\n  }\\n/* Premium card with enhanced theme-aware effects */\\n.card-premium {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    border-radius: 0.5rem;\\n    transform: translateY(0);\\n    transition: var(--theme-transition);\\n  }\\n.card-premium:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n.card-premium {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.card-premium::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);\\n    transition: left 0.5s;\\n  }\\n.card-premium:hover::before {\\n    left: 100%;\\n  }\\n.card-premium {\\n  will-change: transform, box-shadow;\\n    background: var(--bg-glass-strong);\\n    backdrop-filter: blur(15px);\\n    border: 1px solid var(--border-secondary);\\n    transition: var(--theme-transition);\\n}\\n.card-premium:hover {\\n    background: var(--bg-glass-strong);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n/* Enhanced Button variants with immersive effects */\\n.btn-primary {\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.btn-primary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.btn-primary {\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  }\\n.btn-primary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 20px rgba(34, 197, 94, 0.3);\\n}\\n.btn-primary:active {\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n.btn-secondary {\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.btn-secondary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.btn-secondary {\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n.btn-secondary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 15px rgba(71, 85, 105, 0.3);\\n}\\n.btn-outline {\\n  border-radius: 0.5rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\\n}\\n.btn-outline:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.btn-outline {\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    background: transparent;\\n  }\\n.btn-outline:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 0 15px rgba(34, 197, 94, 0.2);\\n}\\n.btn-danger {\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.btn-danger:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.btn-danger {\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n.btn-danger:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 20px rgba(239, 68, 68, 0.3);\\n}\\n/* Button ripple effect */\\n.btn-ripple {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.btn-ripple::before {\\n    content: '';\\n    position: absolute;\\n    top: 50%;\\n    left: 50%;\\n    width: 0;\\n    height: 0;\\n    border-radius: 50%;\\n    background: rgba(255, 255, 255, 0.3);\\n    transform: translate(-50%, -50%);\\n    transition: width 0.3s, height 0.3s;\\n  }\\n.btn-ripple:active::before {\\n    width: 300px;\\n    height: 300px;\\n  }\\n/* Enhanced Input styles with immersive effects */\\n.input-primary {\\n  border-radius: 0.5rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.input-primary::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));\\n}\\n.input-primary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.input-primary {\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    position: relative;\\n  }\\n.input-primary:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1),\\n                0 0 20px rgba(34, 197, 94, 0.1);\\n    transform: translateY(-1px);\\n}\\n.input-primary:hover:not(:focus) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n}\\n/* Floating label input */\\n/* Enhanced Status indicators with glow effects */\\n.status-warning {\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  border-width: 1px;\\n  border-color: rgb(234 179 8 / 0.2);\\n  background-color: rgb(113 63 18 / 0.2);\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n    position: relative;\\n    overflow: hidden;\\n}\\n/* Live indicator with pulse */\\n.status-live {\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  border-width: 1px;\\n  border-color: rgb(34 197 94 / 0.2);\\n  background-color: rgb(20 83 45 / 0.2);\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n    position: relative;\\n    overflow: hidden;\\n}\\n.status-live::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background: rgba(34, 197, 94, 0.1);\\n    border-radius: inherit;\\n    animation: glowPulseGreen 2s ease-in-out infinite alternate;\\n  }\\n.status-live::after {\\n    content: '';\\n    position: absolute;\\n    top: 50%;\\n    left: 0.5rem;\\n    width: 6px;\\n    height: 6px;\\n    background: rgb(34, 197, 94);\\n    border-radius: 50%;\\n    transform: translateY(-50%);\\n    animation: pulse 1.5s ease-in-out infinite;\\n  }\\n/* Premium status indicator */\\n.status-premium {\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(79, 70, 229, 0.2));\\n    color: rgb(196, 181, 253);\\n    border: 1px solid rgba(147, 51, 234, 0.3);\\n    box-shadow: 0 0 15px rgba(147, 51, 234, 0.2);\\n}\\n/* Risk level indicators */\\n/* Loading states */\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.loading-spinner {\\n  animation: spin 1s linear infinite;\\n  border-radius: 9999px;\\n  border-width: 2px;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n  --tw-border-opacity: 1;\\n  border-top-color: rgb(74 222 128 / var(--tw-border-opacity, 1));\\n}\\n/* Data visualization */\\n/* Navigation */\\n.nav-link-active {\\n  display: flex;\\n  align-items: center;\\n  border-radius: 0.5rem;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n  border-width: 1px;\\n  border-color: rgb(34 197 94 / 0.2);\\n  background-color: rgb(20 83 45 / 0.2);\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.nav-link-inactive {\\n  display: flex;\\n  align-items: center;\\n  border-radius: 0.5rem;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n  --tw-text-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\\n}\\n.nav-link-inactive:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n/* Enhanced Tables with immersive effects */\\n/* Interactive table row with glow */\\n/* Theme-aware text classes */\\n.themed-text-primary {\\n    color: var(--text-primary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-text-secondary {\\n    color: var(--text-secondary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-text-tertiary {\\n    color: var(--text-tertiary);\\n    transition: var(--theme-transition);\\n  }\\n/* Theme-aware border classes */\\n.themed-border {\\n    border-color: var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-border-b {\\n    border-bottom: 1px solid var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-border-t {\\n    border-top: 1px solid var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n/* Theme-aware card classes */\\n.themed-card-secondary {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-secondary);\\n    transition: var(--theme-transition);\\n  }\\n.themed-card-secondary:hover {\\n    background-color: var(--bg-tertiary);\\n    border-color: var(--border-primary);\\n  }\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\n.fixed {\\n  position: fixed;\\n}\\n.absolute {\\n  position: absolute;\\n}\\n.relative {\\n  position: relative;\\n}\\n.sticky {\\n  position: sticky;\\n}\\n.inset-0 {\\n  inset: 0px;\\n}\\n.inset-y-0 {\\n  top: 0px;\\n  bottom: 0px;\\n}\\n.left-0 {\\n  left: 0px;\\n}\\n.right-0 {\\n  right: 0px;\\n}\\n.right-8 {\\n  right: 2rem;\\n}\\n.top-0 {\\n  top: 0px;\\n}\\n.z-10 {\\n  z-index: 10;\\n}\\n.z-20 {\\n  z-index: 20;\\n}\\n.z-40 {\\n  z-index: 40;\\n}\\n.z-50 {\\n  z-index: 50;\\n}\\n.z-\\\\[9999\\\\] {\\n  z-index: 9999;\\n}\\n.-m-2\\\\.5 {\\n  margin: -0.625rem;\\n}\\n.-mx-2 {\\n  margin-left: -0.5rem;\\n  margin-right: -0.5rem;\\n}\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\n.ml-auto {\\n  margin-left: auto;\\n}\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\n.mt-auto {\\n  margin-top: auto;\\n}\\n.line-clamp-2 {\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 2;\\n}\\n.block {\\n  display: block;\\n}\\n.inline-block {\\n  display: inline-block;\\n}\\n.inline {\\n  display: inline;\\n}\\n.flex {\\n  display: flex;\\n}\\n.inline-flex {\\n  display: inline-flex;\\n}\\n.table {\\n  display: table;\\n}\\n.grid {\\n  display: grid;\\n}\\n.hidden {\\n  display: none;\\n}\\n.h-10 {\\n  height: 2.5rem;\\n}\\n.h-12 {\\n  height: 3rem;\\n}\\n.h-16 {\\n  height: 4rem;\\n}\\n.h-2 {\\n  height: 0.5rem;\\n}\\n.h-3 {\\n  height: 0.75rem;\\n}\\n.h-32 {\\n  height: 8rem;\\n}\\n.h-4 {\\n  height: 1rem;\\n}\\n.h-5 {\\n  height: 1.25rem;\\n}\\n.h-6 {\\n  height: 1.5rem;\\n}\\n.h-64 {\\n  height: 16rem;\\n}\\n.h-8 {\\n  height: 2rem;\\n}\\n.h-full {\\n  height: 100%;\\n}\\n.max-h-32 {\\n  max-height: 8rem;\\n}\\n.max-h-64 {\\n  max-height: 16rem;\\n}\\n.max-h-80 {\\n  max-height: 20rem;\\n}\\n.max-h-96 {\\n  max-height: 24rem;\\n}\\n.max-h-\\\\[90vh\\\\] {\\n  max-height: 90vh;\\n}\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\n.w-10 {\\n  width: 2.5rem;\\n}\\n.w-11 {\\n  width: 2.75rem;\\n}\\n.w-12 {\\n  width: 3rem;\\n}\\n.w-16 {\\n  width: 4rem;\\n}\\n.w-2 {\\n  width: 0.5rem;\\n}\\n.w-24 {\\n  width: 6rem;\\n}\\n.w-3 {\\n  width: 0.75rem;\\n}\\n.w-4 {\\n  width: 1rem;\\n}\\n.w-48 {\\n  width: 12rem;\\n}\\n.w-5 {\\n  width: 1.25rem;\\n}\\n.w-6 {\\n  width: 1.5rem;\\n}\\n.w-64 {\\n  width: 16rem;\\n}\\n.w-8 {\\n  width: 2rem;\\n}\\n.w-full {\\n  width: 100%;\\n}\\n.w-px {\\n  width: 1px;\\n}\\n.min-w-0 {\\n  min-width: 0px;\\n}\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\n.max-w-3xl {\\n  max-width: 48rem;\\n}\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\n.max-w-md {\\n  max-width: 28rem;\\n}\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.shrink-0 {\\n  flex-shrink: 0;\\n}\\n.grow {\\n  flex-grow: 1;\\n}\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\n.list-inside {\\n  list-style-position: inside;\\n}\\n.list-disc {\\n  list-style-type: disc;\\n}\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.flex-col {\\n  flex-direction: column;\\n}\\n.items-start {\\n  align-items: flex-start;\\n}\\n.items-end {\\n  align-items: flex-end;\\n}\\n.items-center {\\n  align-items: center;\\n}\\n.justify-end {\\n  justify-content: flex-end;\\n}\\n.justify-center {\\n  justify-content: center;\\n}\\n.justify-between {\\n  justify-content: space-between;\\n}\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\n.gap-4 {\\n  gap: 1rem;\\n}\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\n.gap-8 {\\n  gap: 2rem;\\n}\\n.gap-x-4 {\\n  column-gap: 1rem;\\n}\\n.gap-y-5 {\\n  row-gap: 1.25rem;\\n}\\n.gap-y-7 {\\n  row-gap: 1.75rem;\\n}\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-5 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.self-start {\\n  align-self: flex-start;\\n}\\n.self-stretch {\\n  align-self: stretch;\\n}\\n.overflow-auto {\\n  overflow: auto;\\n}\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.whitespace-pre-wrap {\\n  white-space: pre-wrap;\\n}\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\n.rounded-t {\\n  border-top-left-radius: 0.25rem;\\n  border-top-right-radius: 0.25rem;\\n}\\n.border {\\n  border-width: 1px;\\n}\\n.border-2 {\\n  border-width: 2px;\\n}\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\n.border-l-4 {\\n  border-left-width: 4px;\\n}\\n.border-r {\\n  border-right-width: 1px;\\n}\\n.border-t {\\n  border-top-width: 1px;\\n}\\n.border-blue-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-500\\\\/20 {\\n  border-color: rgb(59 130 246 / 0.2);\\n}\\n.border-blue-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-600\\\\/30 {\\n  border-color: rgb(37 99 235 / 0.3);\\n}\\n.border-green-400 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));\\n}\\n.border-green-500\\\\/20 {\\n  border-color: rgb(34 197 94 / 0.2);\\n}\\n.border-orange-500\\\\/20 {\\n  border-color: rgb(249 115 22 / 0.2);\\n}\\n.border-red-500\\\\/20 {\\n  border-color: rgb(239 68 68 / 0.2);\\n}\\n.border-slate-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));\\n}\\n.border-slate-700 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));\\n}\\n.border-yellow-500\\\\/20 {\\n  border-color: rgb(234 179 8 / 0.2);\\n}\\n.border-yellow-500\\\\/30 {\\n  border-color: rgb(234 179 8 / 0.3);\\n}\\n.border-t-transparent {\\n  border-top-color: transparent;\\n}\\n.bg-black\\\\/50 {\\n  background-color: rgb(0 0 0 / 0.5);\\n}\\n.bg-blue-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-400\\\\/10 {\\n  background-color: rgb(96 165 250 / 0.1);\\n}\\n.bg-blue-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500\\\\/10 {\\n  background-color: rgb(59 130 246 / 0.1);\\n}\\n.bg-blue-500\\\\/20 {\\n  background-color: rgb(59 130 246 / 0.2);\\n}\\n.bg-blue-600\\\\/20 {\\n  background-color: rgb(37 99 235 / 0.2);\\n}\\n.bg-blue-900\\\\/20 {\\n  background-color: rgb(30 58 138 / 0.2);\\n}\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500\\\\/20 {\\n  background-color: rgb(107 114 128 / 0.2);\\n}\\n.bg-gray-900\\\\/20 {\\n  background-color: rgb(17 24 39 / 0.2);\\n}\\n.bg-green-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-400\\\\/10 {\\n  background-color: rgb(74 222 128 / 0.1);\\n}\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500\\\\/10 {\\n  background-color: rgb(34 197 94 / 0.1);\\n}\\n.bg-green-500\\\\/20 {\\n  background-color: rgb(34 197 94 / 0.2);\\n}\\n.bg-green-600\\\\/20 {\\n  background-color: rgb(22 163 74 / 0.2);\\n}\\n.bg-green-900\\\\/20 {\\n  background-color: rgb(20 83 45 / 0.2);\\n}\\n.bg-orange-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(251 146 60 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-500\\\\/10 {\\n  background-color: rgb(249 115 22 / 0.1);\\n}\\n.bg-orange-500\\\\/20 {\\n  background-color: rgb(249 115 22 / 0.2);\\n}\\n.bg-orange-600\\\\/20 {\\n  background-color: rgb(234 88 12 / 0.2);\\n}\\n.bg-orange-900\\\\/20 {\\n  background-color: rgb(124 45 18 / 0.2);\\n}\\n.bg-pink-500\\\\/20 {\\n  background-color: rgb(236 72 153 / 0.2);\\n}\\n.bg-purple-500\\\\/10 {\\n  background-color: rgb(168 85 247 / 0.1);\\n}\\n.bg-purple-500\\\\/20 {\\n  background-color: rgb(168 85 247 / 0.2);\\n}\\n.bg-purple-600\\\\/20 {\\n  background-color: rgb(147 51 234 / 0.2);\\n}\\n.bg-red-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-400\\\\/10 {\\n  background-color: rgb(248 113 113 / 0.1);\\n}\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500\\\\/10 {\\n  background-color: rgb(239 68 68 / 0.1);\\n}\\n.bg-red-500\\\\/20 {\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\n.bg-red-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-600\\\\/20 {\\n  background-color: rgb(220 38 38 / 0.2);\\n}\\n.bg-red-900\\\\/20 {\\n  background-color: rgb(127 29 29 / 0.2);\\n}\\n.bg-red-900\\\\/40 {\\n  background-color: rgb(127 29 29 / 0.4);\\n}\\n.bg-slate-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-600\\\\/20 {\\n  background-color: rgb(71 85 105 / 0.2);\\n}\\n.bg-slate-700 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-700\\\\/30 {\\n  background-color: rgb(51 65 85 / 0.3);\\n}\\n.bg-slate-800 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-800\\\\/50 {\\n  background-color: rgb(30 41 59 / 0.5);\\n}\\n.bg-slate-900 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\\n}\\n.bg-slate-900\\\\/20 {\\n  background-color: rgb(15 23 42 / 0.2);\\n}\\n.bg-yellow-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-400\\\\/10 {\\n  background-color: rgb(250 204 21 / 0.1);\\n}\\n.bg-yellow-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500\\\\/10 {\\n  background-color: rgb(234 179 8 / 0.1);\\n}\\n.bg-yellow-500\\\\/20 {\\n  background-color: rgb(234 179 8 / 0.2);\\n}\\n.bg-yellow-900\\\\/20 {\\n  background-color: rgb(113 63 18 / 0.2);\\n}\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.from-yellow-500\\\\/20 {\\n  --tw-gradient-from: rgb(234 179 8 / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.to-orange-500\\\\/20 {\\n  --tw-gradient-to: rgb(249 115 22 / 0.2) var(--tw-gradient-to-position);\\n}\\n.p-2 {\\n  padding: 0.5rem;\\n}\\n.p-2\\\\.5 {\\n  padding: 0.625rem;\\n}\\n.p-3 {\\n  padding: 0.75rem;\\n}\\n.p-4 {\\n  padding: 1rem;\\n}\\n.p-5 {\\n  padding: 1.25rem;\\n}\\n.p-6 {\\n  padding: 1.5rem;\\n}\\n.p-8 {\\n  padding: 2rem;\\n}\\n.px-1 {\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-5 {\\n  padding-left: 1.25rem;\\n  padding-right: 1.25rem;\\n}\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-2\\\\.5 {\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n}\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n.pb-4 {\\n  padding-bottom: 1rem;\\n}\\n.pl-10 {\\n  padding-left: 2.5rem;\\n}\\n.pl-3 {\\n  padding-left: 0.75rem;\\n}\\n.pr-10 {\\n  padding-right: 2.5rem;\\n}\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\n.pt-3 {\\n  padding-top: 0.75rem;\\n}\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\n.text-left {\\n  text-align: left;\\n}\\n.text-center {\\n  text-align: center;\\n}\\n.text-right {\\n  text-align: right;\\n}\\n.font-mono {\\n  font-family: JetBrains Mono, Menlo, Monaco, monospace;\\n}\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.text-base {\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold {\\n  font-weight: 700;\\n}\\n.font-medium {\\n  font-weight: 500;\\n}\\n.font-semibold {\\n  font-weight: 600;\\n}\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\n.text-black {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-cyan-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-green-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-green-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\\n}\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(129 140 248 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\\n}\\n.text-pink-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(244 114 182 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\\n}\\n.text-red-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(100 116 139 / var(--tw-text-opacity, 1));\\n}\\n.text-slate-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\\n}\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\\n}\\n.opacity-0 {\\n  opacity: 0;\\n}\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\n.shadow-glow-blue {\\n  --tw-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\\n  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-glow-orange {\\n  --tw-shadow: 0 0 20px rgba(251, 191, 36, 0.3);\\n  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-150 {\\n  transition-duration: 150ms;\\n}\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\n.duration-500 {\\n  transition-duration: 500ms;\\n}\\n.ease-in-out {\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n/* Glass morphism effect */\\n/* Gradient text */\\n/* Enhanced Custom shadows and glow effects */\\n.shadow-glow-green {\\n    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3),\\n                0 0 40px rgba(34, 197, 94, 0.1);\\n  }\\n.shadow-glow-blue {\\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3),\\n                0 0 40px rgba(59, 130, 246, 0.1);\\n  }\\n.shadow-glow-orange {\\n    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3),\\n                0 0 40px rgba(251, 191, 36, 0.1);\\n  }\\n.shadow-glow-red {\\n    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3),\\n                0 0 40px rgba(239, 68, 68, 0.1);\\n  }\\n.shadow-glow-purple {\\n    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3),\\n                0 0 40px rgba(147, 51, 234, 0.1);\\n  }\\n/* Pulsing glow effect */\\n.glow-pulse-green {\\n    animation: glowPulseGreen 2s ease-in-out infinite alternate;\\n  }\\n.glow-pulse-blue {\\n    animation: glowPulseBlue 2s ease-in-out infinite alternate;\\n  }\\n/* Floating effect */\\n.float {\\n    animation: float 3s ease-in-out infinite;\\n  }\\n.float-delayed {\\n    animation: float 3s ease-in-out infinite;\\n    animation-delay: 1s;\\n  }\\n/* Shimmer effect */\\n.shimmer {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n.shimmer::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);\\n    animation: shimmer 2s infinite;\\n  }\\n/* Magnetic hover effect */\\n.magnetic {\\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n.magnetic:hover {\\n    transform: scale(1.02);\\n  }\\n/* Tilt effect */\\n.tilt {\\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n.tilt:hover {\\n    transform: perspective(1000px) rotateX(5deg) rotateY(5deg);\\n  }\\n/* Enhanced Animation utilities */\\n.animate-slide-in-left {\\n    animation: slideInLeft 0.4s ease-out;\\n  }\\n.animate-slide-in-right {\\n    animation: slideInRight 0.4s ease-out;\\n  }\\n/* Staggered animations */\\n.animate-stagger-1 { animation-delay: 0.1s; }\\n.animate-stagger-2 { animation-delay: 0.2s; }\\n.animate-stagger-3 { animation-delay: 0.3s; }\\n/* Hide scrollbar but keep functionality */\\n/* Custom focus styles */\\n\\n/* Import theme system */\\n\\n/* Import Inter font */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');\\n@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');\\n\\n/* Base styles */\\n\\n/* Component styles */\\n\\n/* Utility styles */\\n\\n/* Enhanced Custom animations */\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideDown {\\n  from {\\n    transform: translateY(-10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideInLeft {\\n  from {\\n    transform: translateX(-20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideInRight {\\n  from {\\n    transform: translateX(20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes bounceIn {\\n  0% {\\n    transform: scale(0.3);\\n    opacity: 0;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.8;\\n  }\\n  70% {\\n    transform: scale(0.9);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes scaleIn {\\n  from {\\n    transform: scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes rotateIn {\\n  from {\\n    transform: rotate(-10deg) scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: rotate(0deg) scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n\\n@keyframes glowPulse {\\n  0% {\\n    box-shadow: 0 0 5px rgba(255, 255, 255, 0.1);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3), 0 0 30px rgba(255, 255, 255, 0.1);\\n  }\\n}\\n\\n@keyframes glowPulseGreen {\\n  0% {\\n    box-shadow: 0 0 5px rgba(34, 197, 94, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(34, 197, 94, 0.4), 0 0 30px rgba(34, 197, 94, 0.2);\\n  }\\n}\\n\\n@keyframes glowPulseBlue {\\n  0% {\\n    box-shadow: 0 0 5px rgba(59, 130, 246, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 0 30px rgba(59, 130, 246, 0.2);\\n  }\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n  \\n  body {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n    --tw-text-opacity: 1;\\n    color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n  }\\n}\\n\\\\n\\\\n\\n/* Performance optimizations */\\n* {\\n  /* Use hardware acceleration for transforms */\\n  transform: translateZ(0);\\n  backface-visibility: hidden;\\n  perspective: 1000px;\\n}\\n\\n/* Optimize animations */\\n@media (prefers-reduced-motion: reduce) {\\n  *,\\n  *::before,\\n  *::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n    scroll-behavior: auto !important;\\n  }\\n}\\n\\n/* Optimize repaints */\\n.card-premium,\\n.card-glow,\\n.magnetic {\\n  will-change: transform, box-shadow;\\n}\\n\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline,\\n.btn-danger {\\n  will-change: transform, box-shadow, background-color;\\n}\\n\\n/* Optimize scrolling */\\n.scrollable {\\n  -webkit-overflow-scrolling: touch;\\n  overflow-scrolling: touch;\\n}\\n\\n/* Optimize text rendering */\\nbody {\\n  text-rendering: optimizeSpeed;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n/* Optimize images */\\nimg {\\n  content-visibility: auto;\\n  contain-intrinsic-size: 300px 200px;\\n}\\n\\n/* Critical CSS inlining */\\n.above-fold {\\n  contain: layout style paint;\\n}\\n.hover\\\\:themed-text-primary:hover {\\n    color: var(--text-primary);\\n    transition: var(--theme-transition);\\n  }\\n.after\\\\:absolute::after {\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\n.after\\\\:left-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 2px;\\n}\\n.after\\\\:top-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  top: 2px;\\n}\\n.after\\\\:h-5::after {\\n  content: var(--tw-content);\\n  height: 1.25rem;\\n}\\n.after\\\\:w-5::after {\\n  content: var(--tw-content);\\n  width: 1.25rem;\\n}\\n.after\\\\:rounded-full::after {\\n  content: var(--tw-content);\\n  border-radius: 9999px;\\n}\\n.after\\\\:border::after {\\n  content: var(--tw-content);\\n  border-width: 1px;\\n}\\n.after\\\\:border-gray-300::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.after\\\\:bg-white::after {\\n  content: var(--tw-content);\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.after\\\\:transition-all::after {\\n  content: var(--tw-content);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.after\\\\:content-\\\\[\\\\'\\\\'\\\\]::after {\\n  --tw-content: '';\\n  content: var(--tw-content);\\n}\\n.last\\\\:border-b-0:last-child {\\n  border-bottom-width: 0px;\\n}\\n.hover\\\\:border-slate-500:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:bg-blue-600\\\\/30:hover {\\n  background-color: rgb(37 99 235 / 0.3);\\n}\\n.hover\\\\:bg-green-600\\\\/30:hover {\\n  background-color: rgb(22 163 74 / 0.3);\\n}\\n.hover\\\\:bg-orange-600\\\\/30:hover {\\n  background-color: rgb(234 88 12 / 0.3);\\n}\\n.hover\\\\:bg-purple-600\\\\/30:hover {\\n  background-color: rgb(147 51 234 / 0.3);\\n}\\n.hover\\\\:bg-slate-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-slate-700\\\\/50:hover {\\n  background-color: rgb(51 65 85 / 0.5);\\n}\\n.hover\\\\:bg-yellow-500\\\\/30:hover {\\n  background-color: rgb(234 179 8 / 0.3);\\n}\\n.hover\\\\:bg-opacity-80:hover {\\n  --tw-bg-opacity: 0.8;\\n}\\n.hover\\\\:text-blue-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-blue-400:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-green-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-red-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-white:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-yellow-300:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\\n}\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.focus\\\\:ring-green-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\n.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\n.peer:checked ~ .peer-checked\\\\:bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:translate-x-full::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:border-white::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\n.peer:focus ~ .peer-focus\\\\:outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-4 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-blue-300 {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));\\n}\\n.dark\\\\:border-gray-600:is(.dark *) {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n.dark\\\\:bg-gray-700:is(.dark *) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n.peer:focus ~ .dark\\\\:peer-focus\\\\:ring-blue-800:is(.dark *) {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity, 1));\\n}\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .sm\\\\:justify-between {\\n    justify-content: space-between;\\n  }\\n\\n  .sm\\\\:gap-x-6 {\\n    column-gap: 1.5rem;\\n  }\\n\\n  .sm\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\n\\n  .sm\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-5 {\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:fixed {\\n    position: fixed;\\n  }\\n\\n  .lg\\\\:inset-y-0 {\\n    top: 0px;\\n    bottom: 0px;\\n  }\\n\\n  .lg\\\\:z-50 {\\n    z-index: 50;\\n  }\\n\\n  .lg\\\\:col-span-1 {\\n    grid-column: span 1 / span 1;\\n  }\\n\\n  .lg\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:col-span-3 {\\n    grid-column: span 3 / span 3;\\n  }\\n\\n  .lg\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .lg\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .lg\\\\:w-64 {\\n    width: 16rem;\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-6 {\\n    grid-template-columns: repeat(6, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .lg\\\\:flex-col {\\n    flex-direction: column;\\n  }\\n\\n  .lg\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .lg\\\\:justify-between {\\n    justify-content: space-between;\\n  }\\n\\n  .lg\\\\:gap-x-6 {\\n    column-gap: 1.5rem;\\n  }\\n\\n  .lg\\\\:space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n\\n  .lg\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\n\\n  .lg\\\\:px-8 {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n\\n  .lg\\\\:pl-64 {\\n    padding-left: 16rem;\\n  }\\n}\\n@media (min-width: 1280px) {\\n\\n  .xl\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc,EAAd,MAAc;EAAd,WAAc,EAAd,MAAc;EAAd,yCAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,qDAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;;AAAd;EAAA,gBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,mBAAc;EAAd,sBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,mBAAc;EAAd,sBAAc;AAAA;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd,iFAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,mPAAc;EAAd,wCAAc;EAAd,4BAAc;EAAd,4BAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,wBAAc;EAAd,sBAAc;EAAd;AAAc;;AAAd;EAAA,gBAAc;EAAd,UAAc;EAAd,yBAAc;EAAd,qBAAc;EAAd,sBAAc;EAAd,6BAAc;EAAd,yBAAc;UAAd,iBAAc;EAAd,cAAc;EAAd,YAAc;EAAd,WAAc;EAAd,cAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd;AAAc;;AAAd;EAAA,sQAAc;AAAA;;AAAd;;EAAA;IAAA;EAAc;AAAA;;AAAd;EAAA,oKAAc;AAAA;;AAAd;;EAAA;IAAA;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,uOAAc;EAAd,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,4BAAc;AAAA;;AAAd;;EAAA;IAAA;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,iBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,gBAAc;EAAd,UAAc;EAAd,gBAAc;EAAd;AAAc;;AAAd;EAAA,6BAAc;EAAd;AAAc;EAAd;IAAA,2CAAc;IAAd,uBAAc;EAAA;;EAAd;IAAA,mCAAc;IAAd,0BAAc;IAAd,qDAAc;IAAd,mCAAc;IAAd,mCAAc;IAAd,kCAAc;EAAA;;EAAd,qBAAc;EAAd;IAAA,UAAc;IAAd,WAAc;EAAA;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd;EAAA,qBAAc;EAAd,kBAAc;EAAd;AAAc;;EAAd;EAAA,kBAAc;EAAd;AAAc;;EAAd,sBAAc;EAAd;IAAA,qBAAc;IAAd,gCAAc;EAAA;AACd;EAAA;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAApB;;EAAA;IAAA;EAAoB;AAAA;AAoDlB,gEAAgE;AAChE;IACE,qCAAqC;IACrC,uCAAuC;IACvC,4BAA4B;IAC5B,qBAAiB;IACjB,mCAAmC;EACrC;AAGE;IAAA,qCAAW;IAAX,uCAAW;IAAX,4BAAW;IAAX,qBAAW;IACX,wBAAwB;IACxB,mCAAmC;EAFxB;AAKb;IACE,2BAA2B;IAC3B,iDAAiD;IACjD,kCAAkC;EACpC;AAEA,oCAAoC;AAElC;IAAA,qCAAiB;IAAjB,uCAAiB;IAAjB,4BAAiB;IAAjB,qBAAiB;IAAjB,wBAAiB;IAAjB,mCAAiB;EAAA;AAAjB;IAAA,2BAAiB;IAAjB,iDAAiB;IAAjB,kCAAiB;EAAA;AADnB;IAEE,kBAAkB;IAClB,gBAAgB;EAClB;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,MAAM;IACN,WAAW;IACX,WAAW;IACX,YAAY;IACZ,oFAAoF;IACpF,qBAAqB;EACvB;AAEA;IACE,UAAU;EACZ;AAEA,mDAAmD;AAEjD;IAAA,qCAAgB;IAAhB,uCAAgB;IAAhB,4BAAgB;IAAhB,qBAAgB;IAAhB,wBAAgB;IAAhB,mCAAgB;EAAA;AAAhB;IAAA,2BAAgB;IAAhB,iDAAgB;IAAhB,kCAAgB;EAAA;AAAhB;IAAA,kBAAgB;IAAhB,gBAAgB;EAAA;AAAhB;IAAA,WAAgB;IAAhB,kBAAgB;IAAhB,MAAgB;IAAhB,WAAgB;IAAhB,WAAgB;IAAhB,YAAgB;IAAhB,oFAAgB;IAAhB,qBAAgB;EAAA;AAAhB;IAAA,UAAgB;EAAA;AAAhB;EAAA,kCAAgB;IAChB,kCAAkC;IAClC,2BAA2B;IAC3B,yCAAyC;IACzC,mCAAmC;AAJnB;AAOlB;IACE,kCAAkC;IAClC,iDAAiD;IACjD,kCAAkC;EACpC;AAEA,oDAAoD;AAElD;EAAA,qBAAkF;EAAlF,kBAAkF;EAAlF,0DAAkF;EAAlF,kBAAkF;EAAlF,mBAAkF;EAAlF,mBAAkF;EAAlF,sBAAkF;EAAlF,gBAAkF;EAAlF,oBAAkF;EAAlF;AAAkF;AAAlF;EAAA,8BAAkF;EAAlF;AAAkF;AADpF;IAEE,kBAAkB;IAClB,gBAAgB;IAChB,iDAAiD;IACjD,wBAAwB;IACxB,iFAAiF;EACnF;AAGE;EAAA,kBAAmB;EAAnB,0DAAmB;IACnB,2BAA2B;IAC3B;;;AAFmB;AAOrB;IACE,wBAAwB;IACxB,6CAA6C;EAC/C;AAGE;EAAA,qBAAkF;EAAlF,kBAAkF;EAAlF,yDAAkF;EAAlF,kBAAkF;EAAlF,mBAAkF;EAAlF,mBAAkF;EAAlF,sBAAkF;EAAlF,gBAAkF;EAAlF,oBAAkF;EAAlF;AAAkF;AAAlF;EAAA,8BAAkF;EAAlF;AAAkF;AADpF;IAEE,kBAAkB;IAClB,gBAAgB;IAChB,iDAAiD;IACjD,wBAAwB;IACxB,6CAA6C;EAC/C;AAGE;EAAA,kBAAmB;EAAnB,0DAAmB;IACnB,2BAA2B;IAC3B;;;AAFmB;AAQnB;EAAA,qBAAiG;EAAjG,iBAAiG;EAAjG,sBAAiG;EAAjG,0DAAiG;EAAjG,kBAAiG;EAAjG,mBAAiG;EAAjG,mBAAiG;EAAjG,sBAAiG;EAAjG,gBAAiG;EAAjG,oBAAiG;EAAjG;AAAiG;AAAjG;EAAA,8BAAiG;EAAjG;AAAiG;AADnG;IAEE,kBAAkB;IAClB,gBAAgB;IAChB,iDAAiD;IACjD,wBAAwB;IACxB,uBAAuB;EACzB;AAGE;EAAA,sBAA+C;EAA/C,0DAA+C;EAA/C,kBAA+C;EAA/C,yDAA+C;EAA/C,oBAA+C;EAA/C,mDAA+C;IAC/C,2BAA2B;IAC3B;;AAF+C;AAO/C;EAAA,qBAAgF;EAAhF,kBAAgF;EAAhF,0DAAgF;EAAhF,kBAAgF;EAAhF,mBAAgF;EAAhF,mBAAgF;EAAhF,sBAAgF;EAAhF,gBAAgF;EAAhF,oBAAgF;EAAhF;AAAgF;AAAhF;EAAA,8BAAgF;EAAhF;AAAgF;AADlF;IAEE,kBAAkB;IAClB,gBAAgB;IAChB,iDAAiD;IACjD,wBAAwB;IACxB,6CAA6C;EAC/C;AAGE;EAAA,kBAAiB;EAAjB,0DAAiB;IACjB,2BAA2B;IAC3B;;;AAFiB;AAOnB,yBAAyB;AACzB;IACE,kBAAkB;IAClB,gBAAgB;EAClB;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,SAAS;IACT,kBAAkB;IAClB,oCAAoC;IACpC,gCAAgC;IAChC,mCAAmC;EACrC;AAEA;IACE,YAAY;IACZ,aAAa;EACf;AAEA,iDAAiD;AAE/C;EAAA,qBAAoH;EAApH,iBAAoH;EAApH,sBAAoH;EAApH,0DAAoH;EAApH,kBAAoH;EAApH,yDAAoH;EAApH,qBAAoH;EAApH,sBAAoH;EAApH,mBAAoH;EAApH,sBAAoH;EAApH,oBAAoH;EAApH;AAAoH;AAApH;EAAA,2BAAoH;EAApH;AAAoH;AAApH;EAAA,8BAAoH;EAApH;AAAoH;AADtH;IAEE,iDAAiD;IACjD,kBAAkB;EACpB;AAGE;EAAA,sBAAoC;EAApC,0DAAoC;EAApC,kBAAoC;EAApC,yDAAoC;IACpC;+CAC2C;IAC3C;AAHoC;AAOpC;EAAA,sBAAuB;EAAvB,4DAAuB;IACvB;AADuB;AAIzB,yBAAyB;AAwBzB,iDAAiD;AA0B/C;EAAA,oBAA2I;EAA3I,mBAA2I;EAA3I,qBAA2I;EAA3I,iBAA2I;EAA3I,kCAA2I;EAA3I,sCAA2I;EAA3I,sBAA2I;EAA3I,uBAA2I;EAA3I,qBAA2I;EAA3I,wBAA2I;EAA3I,kBAA2I;EAA3I,iBAA2I;EAA3I,gBAA2I;EAA3I,oBAA2I;EAA3I,kDAA2I;IAC3I,kBAAkB;IAClB;AAF2I;AAK7I,8BAA8B;AAE5B;EAAA,oBAAoB;EAApB,mBAAoB;EAApB,qBAAoB;EAApB,iBAAoB;EAApB,kCAAoB;EAApB,qCAAoB;EAApB,sBAAoB;EAApB,uBAAoB;EAApB,qBAAoB;EAApB,wBAAoB;EAApB,kBAAoB;EAApB,iBAAoB;EAApB,gBAAoB;EAApB,oBAAoB;EAApB,kDAAoB;IAApB,kBAAoB;IAApB;AAAoB;AAApB;IAAA,WAAoB;IAApB,kBAAoB;IAApB,MAAoB;IAApB,OAAoB;IAApB,QAAoB;IAApB,SAAoB;IAApB,kCAAoB;IAApB,sBAAoB;IAApB,2DAAoB;EAAA;AAGtB;IACE,WAAW;IACX,kBAAkB;IAClB,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,WAAW;IACX,4BAA4B;IAC5B,kBAAkB;IAClB,2BAA2B;IAC3B,0CAA0C;EAC5C;AAEA,6BAA6B;AAE3B;EAAA,oBAA0E;EAA1E,mBAA0E;EAA1E,qBAA0E;EAA1E,qBAA0E;EAA1E,sBAA0E;EAA1E,oBAA0E;EAA1E,uBAA0E;EAA1E,kBAA0E;EAA1E,iBAA0E;EAA1E,gBAA0E;IAC1E,oFAAoF;IACpF,yBAAyB;IACzB,yCAAyC;IACzC;AAJ0E;AAO5E,0BAA0B;AAa1B,mBAAmB;AAMjB;;EAAA;IAAA;EAA6E;AAAA;AAA7E;EAAA,kCAA6E;EAA7E,qBAA6E;EAA7E,iBAA6E;EAA7E,0DAA6E;EAA7E,sBAA6E;EAA7E;AAA6E;AAG/E,uBAAuB;AAKvB,eAAe;AAMb;EAAA,aAAyE;EAAzE,mBAAyE;EAAzE,qBAAyE;EAAzE,qBAAyE;EAAzE,sBAAyE;EAAzE,mBAAyE;EAAzE,sBAAyE;EAAzE,mBAAyE;EAAzE,oBAAyE;EAAzE,gBAAyE;EAAzE,+FAAyE;EAAzE,wDAAyE;EAAzE,0BAAyE;EAAzE,iBAAyE;EAAzE,kCAAyE;EAAzE,qCAAyE;EAAzE,oBAAyE;EAAzE;AAAyE;AAIzE;EAAA,aAAkE;EAAlE,mBAAkE;EAAlE,qBAAkE;EAAlE,qBAAkE;EAAlE,sBAAkE;EAAlE,mBAAkE;EAAlE,sBAAkE;EAAlE,mBAAkE;EAAlE,oBAAkE;EAAlE,gBAAkE;EAAlE,+FAAkE;EAAlE,wDAAkE;EAAlE,0BAAkE;EAAlE,oBAAkE;EAAlE;AAAkE;AAAlE;EAAA,kBAAkE;EAAlE,yDAAkE;EAAlE,oBAAkE;EAAlE;AAAkE;AAGpE,2CAA2C;AA+B3C,oCAAoC;AAepC,6BAA6B;AAC7B;IACE,0BAA0B;IAC1B,mCAAmC;EACrC;AAEA;IACE,4BAA4B;IAC5B,mCAAmC;EACrC;AAEA;IACE,2BAA2B;IAC3B,mCAAmC;EACrC;AAEA,+BAA+B;AAC/B;IACE,mCAAmC;IACnC,mCAAmC;EACrC;AAEA;IACE,8CAA8C;IAC9C,mCAAmC;EACrC;AAEA;IACE,2CAA2C;IAC3C,mCAAmC;EACrC;AAEA,6BAA6B;AAC7B;IACE,qCAAqC;IACrC,yCAAyC;IACzC,mCAAmC;EACrC;AAEA;IACE,oCAAoC;IACpC,mCAAmC;EACrC;AAnbF;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,QAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,oBAAmB;EAAnB,4BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yEAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,eAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6CAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,6CAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB;AAAmB;AAAnB;EAAA,wJAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAwbjB,0BAA0B;AAS1B,kBAAkB;AASlB,6CAA6C;AAC7C;IACE;+CAC2C;EAC7C;AAEA;IACE;gDAC4C;EAC9C;AAEA;IACE;gDAC4C;EAC9C;AAEA;IACE;+CAC2C;EAC7C;AAEA;IACE;gDAC4C;EAC9C;AAEA,wBAAwB;AAKxB;IACE,2DAA2D;EAC7D;AAEA;IACE,0DAA0D;EAC5D;AAEA,oBAAoB;AACpB;IACE,wCAAwC;EAC1C;AAEA;IACE,wCAAwC;IACxC,mBAAmB;EACrB;AAEA,mBAAmB;AACnB;IACE,kBAAkB;IAClB,gBAAgB;EAClB;AAEA;IACE,WAAW;IACX,kBAAkB;IAClB,MAAM;IACN,WAAW;IACX,WAAW;IACX,YAAY;IACZ,sFAAsF;IACtF,8BAA8B;EAChC;AAEA,0BAA0B;AAC1B;IACE,uDAAuD;EACzD;AAEA;IACE,sBAAsB;EACxB;AAEA,gBAAgB;AAChB;IACE,uDAAuD;EACzD;AAEA;IACE,0DAA0D;EAC5D;AAEA,iCAAiC;AAajC;IACE,oCAAoC;EACtC;AAEA;IACE,qCAAqC;EACvC;AAcA,yBAAyB;AACzB,qBAAqB,qBAAqB,EAAE;AAC5C,qBAAqB,qBAAqB,EAAE;AAC5C,qBAAqB,qBAAqB,EAAE;AAI5C,0CAA0C;AAU1C,wBAAwB;;AA9kB1B,wBAAwB;;AAGxB,sBAAsB;AACtB,mHAAmH;AACnH,wHAAwH;;AAExH,gBAAgB;;AAwChB,qBAAqB;;AAqYrB,mBAAmB;;AAwKnB,+BAA+B;AAC/B;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,4BAA4B;IAC5B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,4BAA4B;IAC5B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,UAAU;EACZ;EACA;IACE,sBAAsB;IACtB,YAAY;EACd;EACA;IACE,qBAAqB;IACrB,UAAU;EACZ;EACA;IACE,mBAAmB;IACnB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,UAAU;EACZ;EACA;IACE,mBAAmB;IACnB,UAAU;EACZ;AACF;;AAEA;EACE;IACE,oCAAoC;IACpC,UAAU;EACZ;EACA;IACE,gCAAgC;IAChC,UAAU;EACZ;AACF;;AAEA;EACE;IACE,0BAA0B;EAC5B;EACA;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE;IACE,4CAA4C;EAC9C;EACA;IACE,gFAAgF;EAClF;AACF;;AAEA;EACE;IACE,0CAA0C;EAC5C;EACA;IACE,4EAA4E;EAC9E;AACF;;AAEA;EACE;IACE,2CAA2C;EAC7C;EACA;IACE,8EAA8E;EAChF;AACF;;AAEA;EACE;IACE,WAAW;EACb;EACA;IACE,UAAU;EACZ;AACF;;AAEA,iBAAiB;AACjB;EACE;IACE,wBAAwB;EAC1B;;EAGE;IAAA,kBAA0B;IAA1B,4DAA0B;IAA1B,oBAA0B;IAA1B;EAA0B;AAE9B;AACA;;;EAGE,6CAA6C;EAC7C,wBAAwB;EACxB,2BAA2B;EAC3B,mBAAmB;AACrB;;AAEA,wBAAwB;AACxB;EACE;;;IAGE,qCAAqC;IACrC,uCAAuC;IACvC,sCAAsC;IACtC,gCAAgC;EAClC;AACF;;AAEA,sBAAsB;AACtB;;;EAGE,kCAAkC;AACpC;;AAEA;;;;EAIE,oDAAoD;AACtD;;AAEA,uBAAuB;AACvB;EACE,iCAAiC;EACjC,yBAAyB;AAC3B;;AAEA,4BAA4B;AAC5B;EACE,6BAA6B;EAC7B,mCAAmC;EACnC,kCAAkC;AACpC;;AAEA,oBAAoB;AACpB;EACE,wBAAwB;EACxB,mCAAmC;AACrC;;AAEA,0BAA0B;AAC1B;EACE,2BAA2B;AAC7B;AAnaE;IACE,0BAA0B;IAC1B,mCAAmC;EACrC;AA/YF;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,wBAgzBA;EAhzBA,wDAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,gBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,8BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,2GAgzBA;EAhzBA,yGAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,0BAgzBA;EAhzBA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,8BAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,2GAgzBA;EAhzBA,yGAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,sBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,kBAgzBA;EAhzBA;AAgzBA;AAhzBA;EAAA,oBAgzBA;EAhzBA;AAgzBA;AAhzBA;;EAAA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA,uBAgzBA;IAhzBA,2DAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA,oBAgzBA;IAhzBA;EAgzBA;AAAA;AAhzBA;;EAAA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;AAAA;AAhzBA;;EAAA;IAAA;EAgzBA;;EAhzBA;IAAA,QAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA;EAgzBA;;EAhzBA;IAAA,uBAgzBA;IAhzBA,oDAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA,uBAgzBA;IAhzBA,2DAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA,kBAgzBA;IAhzBA;EAgzBA;;EAhzBA;IAAA;EAgzBA;AAAA;AAhzBA;;EAAA;IAAA;EAgzBA;AAAA\",\"sourcesContent\":[\"@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n/* Import theme system */\\n@import './themes.css';\\n\\n/* Import Inter font */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');\\n@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');\\n\\n/* Base styles */\\n@layer base {\\n  html {\\n    font-family: 'Inter', system-ui, sans-serif;\\n    scroll-behavior: smooth;\\n  }\\n  \\n  body {\\n    background-color: var(--bg-primary);\\n    color: var(--text-primary);\\n    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';\\n    transition: var(--theme-transition);\\n    @apply antialiased;\\n  }\\n\\n  /* Custom scrollbar */\\n  ::-webkit-scrollbar {\\n    width: 6px;\\n    height: 6px;\\n  }\\n\\n  ::-webkit-scrollbar-track {\\n    @apply bg-slate-800;\\n  }\\n\\n  ::-webkit-scrollbar-thumb {\\n    @apply bg-slate-600 rounded-full;\\n  }\\n\\n  ::-webkit-scrollbar-thumb:hover {\\n    @apply bg-slate-500;\\n  }\\n\\n  /* Firefox scrollbar */\\n  * {\\n    scrollbar-width: thin;\\n    scrollbar-color: #475569 #1e293b;\\n  }\\n}\\n\\n/* Component styles */\\n@layer components {\\n  /* Enhanced Card components with theme-aware immersive effects */\\n  .card {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-primary);\\n    box-shadow: var(--shadow-lg);\\n    @apply rounded-lg;\\n    transition: var(--theme-transition);\\n  }\\n\\n  .card-hover {\\n    @apply card;\\n    transform: translateY(0);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .card-hover:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n\\n  /* Immersive card with glow effect */\\n  .card-glow {\\n    @apply card-hover;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .card-glow::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);\\n    transition: left 0.5s;\\n  }\\n\\n  .card-glow:hover::before {\\n    left: 100%;\\n  }\\n\\n  /* Premium card with enhanced theme-aware effects */\\n  .card-premium {\\n    @apply card-glow;\\n    background: var(--bg-glass-strong);\\n    backdrop-filter: blur(15px);\\n    border: 1px solid var(--border-secondary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .card-premium:hover {\\n    background: var(--bg-glass-strong);\\n    box-shadow: var(--shadow-xl), var(--glow-primary);\\n    border-color: var(--brand-primary);\\n  }\\n\\n  /* Enhanced Button variants with immersive effects */\\n  .btn-primary {\\n    @apply bg-green-600 text-white font-medium px-4 py-2 rounded-lg focus:outline-none;\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  }\\n\\n  .btn-primary:hover {\\n    @apply bg-green-700;\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 20px rgba(34, 197, 94, 0.3);\\n  }\\n\\n  .btn-primary:active {\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .btn-secondary {\\n    @apply bg-slate-700 text-white font-medium px-4 py-2 rounded-lg focus:outline-none;\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .btn-secondary:hover {\\n    @apply bg-slate-600;\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 15px rgba(71, 85, 105, 0.3);\\n  }\\n\\n  .btn-outline {\\n    @apply border border-slate-600 text-slate-300 font-medium px-4 py-2 rounded-lg focus:outline-none;\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    background: transparent;\\n  }\\n\\n  .btn-outline:hover {\\n    @apply border-green-500 text-white bg-slate-800;\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 0 15px rgba(34, 197, 94, 0.2);\\n  }\\n\\n  .btn-danger {\\n    @apply bg-red-600 text-white font-medium px-4 py-2 rounded-lg focus:outline-none;\\n    position: relative;\\n    overflow: hidden;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    transform: translateY(0);\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .btn-danger:hover {\\n    @apply bg-red-700;\\n    transform: translateY(-1px);\\n    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2),\\n                0 4px 6px -2px rgba(0, 0, 0, 0.1),\\n                0 0 20px rgba(239, 68, 68, 0.3);\\n  }\\n\\n  /* Button ripple effect */\\n  .btn-ripple {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .btn-ripple::before {\\n    content: '';\\n    position: absolute;\\n    top: 50%;\\n    left: 50%;\\n    width: 0;\\n    height: 0;\\n    border-radius: 50%;\\n    background: rgba(255, 255, 255, 0.3);\\n    transform: translate(-50%, -50%);\\n    transition: width 0.3s, height 0.3s;\\n  }\\n\\n  .btn-ripple:active::before {\\n    width: 300px;\\n    height: 300px;\\n  }\\n\\n  /* Enhanced Input styles with immersive effects */\\n  .input-primary {\\n    @apply bg-slate-800 border border-slate-600 text-white placeholder-slate-400 rounded-lg px-3 py-2 focus:outline-none;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    position: relative;\\n  }\\n\\n  .input-primary:focus {\\n    @apply border-green-500 bg-slate-750;\\n    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1),\\n                0 0 20px rgba(34, 197, 94, 0.1);\\n    transform: translateY(-1px);\\n  }\\n\\n  .input-primary:hover:not(:focus) {\\n    @apply border-slate-500;\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  /* Floating label input */\\n  .input-floating {\\n    @apply input-primary;\\n    padding-top: 1.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n\\n  .input-floating + label {\\n    position: absolute;\\n    left: 0.75rem;\\n    top: 0.75rem;\\n    color: rgb(148, 163, 184);\\n    font-size: 0.875rem;\\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n    pointer-events: none;\\n    transform-origin: left top;\\n  }\\n\\n  .input-floating:focus + label,\\n  .input-floating:not(:placeholder-shown) + label {\\n    transform: translateY(-0.5rem) scale(0.75);\\n    color: rgb(34, 197, 94);\\n  }\\n\\n  /* Enhanced Status indicators with glow effects */\\n  .status-online {\\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/20 text-green-400 border border-green-500/20;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .status-online::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background: rgba(34, 197, 94, 0.1);\\n    border-radius: inherit;\\n    animation: glowPulseGreen 2s ease-in-out infinite alternate;\\n  }\\n\\n  .status-offline {\\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900/20 text-red-400 border border-red-500/20;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .status-warning {\\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/20 text-yellow-400 border border-yellow-500/20;\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  /* Live indicator with pulse */\\n  .status-live {\\n    @apply status-online;\\n  }\\n\\n  .status-live::after {\\n    content: '';\\n    position: absolute;\\n    top: 50%;\\n    left: 0.5rem;\\n    width: 6px;\\n    height: 6px;\\n    background: rgb(34, 197, 94);\\n    border-radius: 50%;\\n    transform: translateY(-50%);\\n    animation: pulse 1.5s ease-in-out infinite;\\n  }\\n\\n  /* Premium status indicator */\\n  .status-premium {\\n    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;\\n    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(79, 70, 229, 0.2));\\n    color: rgb(196, 181, 253);\\n    border: 1px solid rgba(147, 51, 234, 0.3);\\n    box-shadow: 0 0 15px rgba(147, 51, 234, 0.2);\\n  }\\n\\n  /* Risk level indicators */\\n  .risk-low {\\n    @apply text-green-400 bg-green-900/20 border border-green-500/20;\\n  }\\n\\n  .risk-medium {\\n    @apply text-yellow-400 bg-yellow-900/20 border border-yellow-500/20;\\n  }\\n\\n  .risk-high {\\n    @apply text-red-400 bg-red-900/20 border border-red-500/20;\\n  }\\n\\n  /* Loading states */\\n  .loading-skeleton {\\n    @apply animate-pulse bg-slate-700 rounded;\\n  }\\n\\n  .loading-spinner {\\n    @apply animate-spin rounded-full border-2 border-slate-600 border-t-green-400;\\n  }\\n\\n  /* Data visualization */\\n  .chart-container {\\n    @apply bg-slate-800/50 rounded-lg p-4 border border-slate-700;\\n  }\\n\\n  /* Navigation */\\n  .nav-link {\\n    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;\\n  }\\n\\n  .nav-link-active {\\n    @apply nav-link bg-green-900/20 text-green-400 border border-green-500/20;\\n  }\\n\\n  .nav-link-inactive {\\n    @apply nav-link text-slate-400 hover:text-white hover:bg-slate-800;\\n  }\\n\\n  /* Enhanced Tables with immersive effects */\\n  .table-container {\\n    @apply overflow-hidden rounded-lg border border-slate-700;\\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .table-header {\\n    @apply bg-slate-800 px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider border-b border-slate-700;\\n    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(51, 65, 85, 0.9));\\n  }\\n\\n  .table-cell {\\n    @apply px-6 py-4 whitespace-nowrap text-sm text-slate-300 border-b border-slate-700/50;\\n    transition: all 0.2s ease-in-out;\\n  }\\n\\n  .table-row {\\n    @apply bg-slate-900 transition-all duration-300;\\n    position: relative;\\n  }\\n\\n  .table-row:hover {\\n    @apply bg-slate-800/70;\\n    transform: translateX(2px);\\n    box-shadow: 4px 0 8px rgba(34, 197, 94, 0.1);\\n  }\\n\\n  .table-row:hover .table-cell {\\n    @apply text-white;\\n  }\\n\\n  /* Interactive table row with glow */\\n  .table-row-interactive {\\n    @apply table-row cursor-pointer;\\n  }\\n\\n  .table-row-interactive:hover {\\n    background: linear-gradient(90deg, rgba(30, 41, 59, 0.8), rgba(34, 197, 94, 0.05), rgba(30, 41, 59, 0.8));\\n    border-left: 3px solid rgb(34, 197, 94);\\n  }\\n\\n  .table-row-interactive:active {\\n    transform: translateX(1px);\\n    box-shadow: 2px 0 4px rgba(34, 197, 94, 0.2);\\n  }\\n\\n  /* Theme-aware text classes */\\n  .themed-text-primary {\\n    color: var(--text-primary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-text-secondary {\\n    color: var(--text-secondary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-text-tertiary {\\n    color: var(--text-tertiary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  /* Theme-aware border classes */\\n  .themed-border {\\n    border-color: var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-border-b {\\n    border-bottom: 1px solid var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-border-t {\\n    border-top: 1px solid var(--border-primary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  /* Theme-aware card classes */\\n  .themed-card-secondary {\\n    background-color: var(--bg-secondary);\\n    border: 1px solid var(--border-secondary);\\n    transition: var(--theme-transition);\\n  }\\n\\n  .themed-card-secondary:hover {\\n    background-color: var(--bg-tertiary);\\n    border-color: var(--border-primary);\\n  }\\n}\\n\\n/* Utility styles */\\n@layer utilities {\\n  /* Glass morphism effect */\\n  .glass {\\n    @apply bg-white/5 backdrop-blur-md border border-white/10;\\n  }\\n\\n  .glass-dark {\\n    @apply bg-black/20 backdrop-blur-md border border-white/5;\\n  }\\n\\n  /* Gradient text */\\n  .gradient-text {\\n    @apply bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent;\\n  }\\n\\n  .gradient-text-warm {\\n    @apply bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent;\\n  }\\n\\n  /* Enhanced Custom shadows and glow effects */\\n  .shadow-glow-green {\\n    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3),\\n                0 0 40px rgba(34, 197, 94, 0.1);\\n  }\\n\\n  .shadow-glow-blue {\\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3),\\n                0 0 40px rgba(59, 130, 246, 0.1);\\n  }\\n\\n  .shadow-glow-orange {\\n    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3),\\n                0 0 40px rgba(251, 191, 36, 0.1);\\n  }\\n\\n  .shadow-glow-red {\\n    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3),\\n                0 0 40px rgba(239, 68, 68, 0.1);\\n  }\\n\\n  .shadow-glow-purple {\\n    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3),\\n                0 0 40px rgba(147, 51, 234, 0.1);\\n  }\\n\\n  /* Pulsing glow effect */\\n  .glow-pulse {\\n    animation: glowPulse 2s ease-in-out infinite alternate;\\n  }\\n\\n  .glow-pulse-green {\\n    animation: glowPulseGreen 2s ease-in-out infinite alternate;\\n  }\\n\\n  .glow-pulse-blue {\\n    animation: glowPulseBlue 2s ease-in-out infinite alternate;\\n  }\\n\\n  /* Floating effect */\\n  .float {\\n    animation: float 3s ease-in-out infinite;\\n  }\\n\\n  .float-delayed {\\n    animation: float 3s ease-in-out infinite;\\n    animation-delay: 1s;\\n  }\\n\\n  /* Shimmer effect */\\n  .shimmer {\\n    position: relative;\\n    overflow: hidden;\\n  }\\n\\n  .shimmer::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: -100%;\\n    width: 100%;\\n    height: 100%;\\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);\\n    animation: shimmer 2s infinite;\\n  }\\n\\n  /* Magnetic hover effect */\\n  .magnetic {\\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n\\n  .magnetic:hover {\\n    transform: scale(1.02);\\n  }\\n\\n  /* Tilt effect */\\n  .tilt {\\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  }\\n\\n  .tilt:hover {\\n    transform: perspective(1000px) rotateX(5deg) rotateY(5deg);\\n  }\\n\\n  /* Enhanced Animation utilities */\\n  .animate-fade-in {\\n    animation: fadeIn 0.5s ease-in-out;\\n  }\\n\\n  .animate-slide-up {\\n    animation: slideUp 0.3s ease-out;\\n  }\\n\\n  .animate-slide-down {\\n    animation: slideDown 0.3s ease-out;\\n  }\\n\\n  .animate-slide-in-left {\\n    animation: slideInLeft 0.4s ease-out;\\n  }\\n\\n  .animate-slide-in-right {\\n    animation: slideInRight 0.4s ease-out;\\n  }\\n\\n  .animate-bounce-in {\\n    animation: bounceIn 0.6s ease-out;\\n  }\\n\\n  .animate-scale-in {\\n    animation: scaleIn 0.3s ease-out;\\n  }\\n\\n  .animate-rotate-in {\\n    animation: rotateIn 0.5s ease-out;\\n  }\\n\\n  /* Staggered animations */\\n  .animate-stagger-1 { animation-delay: 0.1s; }\\n  .animate-stagger-2 { animation-delay: 0.2s; }\\n  .animate-stagger-3 { animation-delay: 0.3s; }\\n  .animate-stagger-4 { animation-delay: 0.4s; }\\n  .animate-stagger-5 { animation-delay: 0.5s; }\\n\\n  /* Hide scrollbar but keep functionality */\\n  .scrollbar-hide {\\n    -ms-overflow-style: none;\\n    scrollbar-width: none;\\n  }\\n\\n  .scrollbar-hide::-webkit-scrollbar {\\n    display: none;\\n  }\\n\\n  /* Custom focus styles */\\n  .focus-ring {\\n    @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-slate-900;\\n  }\\n\\n  .focus-ring-blue {\\n    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900;\\n  }\\n\\n  .focus-ring-orange {\\n    @apply focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-slate-900;\\n  }\\n}\\n\\n/* Enhanced Custom animations */\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideDown {\\n  from {\\n    transform: translateY(-10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideInLeft {\\n  from {\\n    transform: translateX(-20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideInRight {\\n  from {\\n    transform: translateX(20px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes bounceIn {\\n  0% {\\n    transform: scale(0.3);\\n    opacity: 0;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.8;\\n  }\\n  70% {\\n    transform: scale(0.9);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes scaleIn {\\n  from {\\n    transform: scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes rotateIn {\\n  from {\\n    transform: rotate(-10deg) scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: rotate(0deg) scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n\\n@keyframes glowPulse {\\n  0% {\\n    box-shadow: 0 0 5px rgba(255, 255, 255, 0.1);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3), 0 0 30px rgba(255, 255, 255, 0.1);\\n  }\\n}\\n\\n@keyframes glowPulseGreen {\\n  0% {\\n    box-shadow: 0 0 5px rgba(34, 197, 94, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(34, 197, 94, 0.4), 0 0 30px rgba(34, 197, 94, 0.2);\\n  }\\n}\\n\\n@keyframes glowPulseBlue {\\n  0% {\\n    box-shadow: 0 0 5px rgba(59, 130, 246, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 0 30px rgba(59, 130, 246, 0.2);\\n  }\\n}\\n\\n@keyframes shimmer {\\n  0% {\\n    left: -100%;\\n  }\\n  100% {\\n    left: 100%;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n  \\n  body {\\n    @apply bg-white text-black;\\n  }\\n}\\n\\\\n\\\\n\\n/* Performance optimizations */\\n* {\\n  /* Use hardware acceleration for transforms */\\n  transform: translateZ(0);\\n  backface-visibility: hidden;\\n  perspective: 1000px;\\n}\\n\\n/* Optimize animations */\\n@media (prefers-reduced-motion: reduce) {\\n  *,\\n  *::before,\\n  *::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n    scroll-behavior: auto !important;\\n  }\\n}\\n\\n/* Optimize repaints */\\n.card-premium,\\n.card-glow,\\n.magnetic {\\n  will-change: transform, box-shadow;\\n}\\n\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline,\\n.btn-danger {\\n  will-change: transform, box-shadow, background-color;\\n}\\n\\n/* Optimize scrolling */\\n.scrollable {\\n  -webkit-overflow-scrolling: touch;\\n  overflow-scrolling: touch;\\n}\\n\\n/* Optimize text rendering */\\nbody {\\n  text-rendering: optimizeSpeed;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n/* Optimize images */\\nimg {\\n  content-visibility: auto;\\n  contain-intrinsic-size: 300px 200px;\\n}\\n\\n/* Critical CSS inlining */\\n.above-fold {\\n  contain: layout style paint;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./src/styles/globals.css\n"));

/***/ })

});