"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/players",{

/***/ "./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIService: function() { return /* binding */ APIService; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"./node_modules/next/node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_class_call_check */ \"./node_modules/next/node_modules/@swc/helpers/esm/_class_call_check.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"./node_modules/next/node_modules/@swc/helpers/esm/_ts_generator.js\");\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"./src/services/supabase.ts\");\n/* harmony import */ var _sportradar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sportradar */ \"./src/services/sportradar.ts\");\n/* harmony import */ var _dataTransform__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dataTransform */ \"./src/services/dataTransform.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/process/browser.js\");\n\n\n\n\n\n\n// API Configuration\nvar API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8004\";\nvar CACHE_ENABLED = true;\n// Enhanced API service with Supabase integration and local fallback\nvar APIService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function APIService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_3__._)(this, APIService);\n    }\n    // Universal player search with cloud-powered predictive search\n    APIService.searchPlayers = function searchPlayers(query) {\n        var options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var _options_useCache, useCache, _options_fallbackToLocal, fallbackToLocal, _options_limit, limit, cacheKey, cached, players, error, response, data, localError;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _options_useCache = options.useCache, useCache = _options_useCache === void 0 ? CACHE_ENABLED : _options_useCache, _options_fallbackToLocal = options.fallbackToLocal, fallbackToLocal = _options_fallbackToLocal === void 0 ? true : _options_fallbackToLocal, _options_limit = options.limit, limit = _options_limit === void 0 ? 10 : _options_limit;\n                        cacheKey = \"search_players_\".concat(query, \"_\").concat(limit);\n                        // Check cache first\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            10\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.PlayerService.searchPlayers(query, limit)\n                        ];\n                    case 2:\n                        players = _state.sent();\n                        // Cache the results\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, players);\n                        }\n                        return [\n                            2,\n                            players\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Supabase search failed, trying local API:\", error);\n                        if (!fallbackToLocal) return [\n                            3,\n                            9\n                        ];\n                        _state.label = 4;\n                    case 4:\n                        _state.trys.push([\n                            4,\n                            8,\n                            ,\n                            9\n                        ]);\n                        return [\n                            4,\n                            fetch(\"\".concat(API_BASE_URL, \"/players/search?q=\").concat(encodeURIComponent(query), \"&limit=\").concat(limit))\n                        ];\n                    case 5:\n                        response = _state.sent();\n                        if (!response.ok) return [\n                            3,\n                            7\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 6:\n                        data = _state.sent();\n                        return [\n                            2,\n                            data.players || []\n                        ];\n                    case 7:\n                        return [\n                            3,\n                            9\n                        ];\n                    case 8:\n                        localError = _state.sent();\n                        console.error(\"Local API also failed:\", localError);\n                        return [\n                            3,\n                            9\n                        ];\n                    case 9:\n                        // Return mock data as last resort\n                        return [\n                            2,\n                            _this.getMockPlayers().filter(function(p) {\n                                return p.name.toLowerCase().includes(query.toLowerCase()) || p.team.toLowerCase().includes(query.toLowerCase()) || p.position.toLowerCase().includes(query.toLowerCase());\n                            }).slice(0, limit)\n                        ];\n                    case 10:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get all players with advanced filtering\n    APIService.getPlayers = function getPlayers() {\n        var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var _options_useCache, useCache, _options_useRealData, useRealData, cacheKey, cached, url, params, response, data, error, sportRadarPlayers, transformedPlayers, filteredPlayers, searchTerm, start, end, sportRadarError, players, error1;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _options_useCache = options.useCache, useCache = _options_useCache === void 0 ? CACHE_ENABLED : _options_useCache, _options_useRealData = options.useRealData, useRealData = _options_useRealData === void 0 ? true : _options_useRealData;\n                        cacheKey = \"players_\".concat(JSON.stringify(options));\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            5,\n                            ,\n                            6\n                        ]);\n                        console.log(\"\\uD83D\\uDD04 Fetching players from cached data API...\");\n                        url = \"/api/players\";\n                        params = new URLSearchParams();\n                        if (options.search) params.append(\"search\", options.search);\n                        if (options.position) params.append(\"position\", options.position);\n                        if (options.team) params.append(\"team\", options.team);\n                        if (options.limit) params.append(\"limit\", options.limit.toString());\n                        if (params.toString()) {\n                            url += \"?\" + params.toString();\n                        }\n                        return [\n                            4,\n                            fetch(url)\n                        ];\n                    case 2:\n                        response = _state.sent();\n                        if (!response.ok) return [\n                            3,\n                            4\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 3:\n                        data = _state.sent();\n                        if (data.success && data.data.players) {\n                            console.log(\"✅ Loaded \".concat(data.data.players.length, \" players from cached data\"));\n                            if (useCache) {\n                                _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, data.data.players);\n                            }\n                            return [\n                                2,\n                                data.data.players\n                            ];\n                        }\n                        _state.label = 4;\n                    case 4:\n                        return [\n                            3,\n                            6\n                        ];\n                    case 5:\n                        error = _state.sent();\n                        console.error(\"❌ Cached data API failed:\", error);\n                        return [\n                            3,\n                            6\n                        ];\n                    case 6:\n                        _state.trys.push([\n                            6,\n                            12,\n                            ,\n                            13\n                        ]);\n                        if (!useRealData) return [\n                            3,\n                            10\n                        ];\n                        _state.label = 7;\n                    case 7:\n                        _state.trys.push([\n                            7,\n                            9,\n                            ,\n                            10\n                        ]);\n                        return [\n                            4,\n                            _sportradar__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getAllNRLPlayers()\n                        ];\n                    case 8:\n                        sportRadarPlayers = _state.sent();\n                        transformedPlayers = _dataTransform__WEBPACK_IMPORTED_MODULE_2__[\"default\"].transformPlayers(sportRadarPlayers);\n                        // Apply filtering\n                        filteredPlayers = transformedPlayers;\n                        if (options.search) {\n                            searchTerm = options.search.toLowerCase();\n                            filteredPlayers = filteredPlayers.filter(function(player) {\n                                return player.name.toLowerCase().includes(searchTerm) || player.team.toLowerCase().includes(searchTerm);\n                            });\n                        }\n                        if (options.position) {\n                            filteredPlayers = filteredPlayers.filter(function(player) {\n                                return player.position === options.position;\n                            });\n                        }\n                        if (options.team) {\n                            filteredPlayers = filteredPlayers.filter(function(player) {\n                                return player.team === options.team;\n                            });\n                        }\n                        // Apply pagination\n                        if (options.offset || options.limit) {\n                            start = options.offset || 0;\n                            end = options.limit ? start + options.limit : undefined;\n                            filteredPlayers = filteredPlayers.slice(start, end);\n                        }\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, filteredPlayers);\n                        }\n                        return [\n                            2,\n                            filteredPlayers\n                        ];\n                    case 9:\n                        sportRadarError = _state.sent();\n                        console.error(\"SportRadar API failed, falling back to Supabase:\", sportRadarError);\n                        return [\n                            3,\n                            10\n                        ];\n                    case 10:\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.PlayerService.getPlayers(options)\n                        ];\n                    case 11:\n                        players = _state.sent();\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, players);\n                        }\n                        return [\n                            2,\n                            players\n                        ];\n                    case 12:\n                        error1 = _state.sent();\n                        console.error(\"Error fetching players:\", error1);\n                        return [\n                            2,\n                            _this.getMockPlayers()\n                        ];\n                    case 13:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get all players - used by Players page\n    APIService.getAllPlayers = function getAllPlayers() {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var supabasePlayers, sportRadarPlayers, response, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        console.log(\"\\uD83D\\uDD04 APIService.getAllPlayers() called - attempting to load ALL 581 players from Supabase\");\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            7,\n                            ,\n                            8\n                        ]);\n                        // First try Supabase for complete dataset (should have 581 players)\n                        console.log(\"\\uD83D\\uDD04 Trying Supabase for complete player dataset...\");\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.PlayerService.getAllPlayers()\n                        ];\n                    case 2:\n                        supabasePlayers = _state.sent();\n                        if (supabasePlayers && supabasePlayers.length > 100) {\n                            console.log(\"✅ Supabase loaded \".concat(supabasePlayers.length, \" players\"));\n                            return [\n                                2,\n                                supabasePlayers\n                            ];\n                        }\n                        // Fallback to SportRadar API\n                        console.log(\"\\uD83D\\uDD04 Supabase failed, trying SportRadar API...\");\n                        return [\n                            4,\n                            _this.getAllNRLPlayers(true)\n                        ];\n                    case 3:\n                        sportRadarPlayers = _state.sent();\n                        if (sportRadarPlayers && sportRadarPlayers.length > 100) {\n                            console.log(\"✅ SportRadar API loaded \".concat(sportRadarPlayers.length, \" players\"));\n                            return [\n                                2,\n                                sportRadarPlayers\n                            ];\n                        }\n                        // Fallback to cached data API\n                        console.log(\"\\uD83D\\uDD04 SportRadar failed, trying cached data API...\");\n                        return [\n                            4,\n                            fetch(\"/api/players\")\n                        ];\n                    case 4:\n                        response = _state.sent();\n                        if (!response.ok) return [\n                            3,\n                            6\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 5:\n                        data = _state.sent();\n                        if (data.success && data.data.players) {\n                            console.log(\"✅ Cached data API loaded \".concat(data.data.players.length, \" players\"));\n                            // If we have less than expected, warn about incomplete dataset\n                            if (data.data.players.length < 500) {\n                                console.warn(\"⚠️ Only \".concat(data.data.players.length, \" players found, expected 581. Check Supabase connection.\"));\n                            }\n                            return [\n                                2,\n                                data.data.players\n                            ];\n                        }\n                        _state.label = 6;\n                    case 6:\n                        console.warn(\"⚠️ All data sources failed, falling back to mock data\");\n                        return [\n                            2,\n                            _this.getMockPlayers()\n                        ];\n                    case 7:\n                        error = _state.sent();\n                        console.error(\"❌ APIService.getAllPlayers() error:\", error);\n                        return [\n                            2,\n                            _this.getMockPlayers()\n                        ];\n                    case 8:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get all NRL players from SportRadar\n    APIService.getAllNRLPlayers = function getAllNRLPlayers() {\n        var useCache = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, sportRadarPlayers, transformedPlayers, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"all_nrl_players_transformed\";\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _sportradar__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getAllNRLPlayers()\n                        ];\n                    case 2:\n                        sportRadarPlayers = _state.sent();\n                        transformedPlayers = _dataTransform__WEBPACK_IMPORTED_MODULE_2__[\"default\"].transformPlayers(sportRadarPlayers);\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, transformedPlayers);\n                        }\n                        return [\n                            2,\n                            transformedPlayers\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching all NRL players from SportRadar:\", error);\n                        return [\n                            2,\n                            _this.getMockPlayers()\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get NRL teams from SportRadar\n    APIService.getNRLTeams = function getNRLTeams() {\n        var useCache = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, sportRadarTeams, transformedTeams, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"nrl_teams_transformed\";\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _sportradar__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getNRLTeams()\n                        ];\n                    case 2:\n                        sportRadarTeams = _state.sent();\n                        transformedTeams = _dataTransform__WEBPACK_IMPORTED_MODULE_2__[\"default\"].transformTeams(sportRadarTeams);\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, transformedTeams);\n                        }\n                        return [\n                            2,\n                            transformedTeams\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching NRL teams from SportRadar:\", error);\n                        return [\n                            2,\n                            []\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get injury reports\n    APIService.getInjuryReports = function getInjuryReports() {\n        var useCache = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : CACHE_ENABLED;\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, reports, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"injury_reports\";\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.InjuryService.getInjuryReports()\n                        ];\n                    case 2:\n                        reports = _state.sent();\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, reports);\n                        }\n                        return [\n                            2,\n                            reports\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching injury reports:\", error);\n                        return [\n                            2,\n                            _this.getMockInjuryReports()\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get upcoming fixtures from SportRadar\n    APIService.getUpcomingFixtures = function getUpcomingFixtures() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10, useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, sportRadarFixtures, transformedFixtures, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"upcoming_fixtures_\".concat(limit);\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _sportradar__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getUpcomingFixtures(limit)\n                        ];\n                    case 2:\n                        sportRadarFixtures = _state.sent();\n                        transformedFixtures = _dataTransform__WEBPACK_IMPORTED_MODULE_2__[\"default\"].transformFixtures(sportRadarFixtures);\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, transformedFixtures);\n                        }\n                        return [\n                            2,\n                            transformedFixtures\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching upcoming fixtures from SportRadar:\", error);\n                        return [\n                            2,\n                            []\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get dashboard statistics\n    APIService.getDashboardStats = function getDashboardStats() {\n        var useCache = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : CACHE_ENABLED;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, stats, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"dashboard_stats\";\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.AnalyticsService.getDashboardStats()\n                        ];\n                    case 2:\n                        stats = _state.sent();\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, stats);\n                        }\n                        return [\n                            2,\n                            stats\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching dashboard stats:\", error);\n                        return [\n                            2,\n                            {\n                                total_players: 581,\n                                total_teams: 17,\n                                active_injuries: 12,\n                                last_updated: new Date().toISOString()\n                            }\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get trade recommendations\n    APIService.getTradeRecommendations = function getTradeRecommendations(userId) {\n        var useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : CACHE_ENABLED;\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, recommendations, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"trade_recommendations_\".concat(userId);\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.TradeService.getTradeRecommendations(userId)\n                        ];\n                    case 2:\n                        recommendations = _state.sent();\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, recommendations);\n                        }\n                        return [\n                            2,\n                            recommendations\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching trade recommendations:\", error);\n                        return [\n                            2,\n                            _this.getMockTradeRecommendations()\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Execute trade\n    APIService.executeTrade = function executeTrade(tradeId, userId) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var result, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            2,\n                            ,\n                            3\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.TradeService.executeTrade(tradeId, userId)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        // Clear relevant caches\n                        _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.clear();\n                        return [\n                            2,\n                            result\n                        ];\n                    case 2:\n                        error = _state.sent();\n                        console.error(\"Error executing trade:\", error);\n                        throw error;\n                    case 3:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get top performers\n    APIService.getTopPerformers = function getTopPerformers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10, useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : CACHE_ENABLED;\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, players, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"top_performers_\".concat(limit);\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.PlayerService.getTopPerformers(limit)\n                        ];\n                    case 2:\n                        players = _state.sent();\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, players);\n                        }\n                        return [\n                            2,\n                            players\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching top performers:\", error);\n                        return [\n                            2,\n                            _this.getMockPlayers().slice(0, limit)\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get price risers\n    APIService.getPriceRisers = function getPriceRisers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10, useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : CACHE_ENABLED;\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, players, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"price_risers_\".concat(limit);\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.PlayerService.getPriceRisers(limit)\n                        ];\n                    case 2:\n                        players = _state.sent();\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, players);\n                        }\n                        return [\n                            2,\n                            players\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching price risers:\", error);\n                        return [\n                            2,\n                            _this.getMockPlayers().slice(0, limit)\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Mock data for fallback\n    APIService.getMockPlayers = function getMockPlayers() {\n        return [\n            {\n                id: \"1\",\n                name: \"Nathan Cleary\",\n                position: \"Halfback\",\n                team: \"Penrith Panthers\",\n                price: 750000,\n                points: 1250,\n                form: 8.5,\n                ownership: 45.2,\n                breakeven: 65,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            {\n                id: \"2\",\n                name: \"Kalyn Ponga\",\n                position: \"Fullback\",\n                team: \"Newcastle Knights\",\n                price: 680000,\n                points: 1180,\n                form: 7.8,\n                ownership: 38.7,\n                breakeven: 72,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            {\n                id: \"3\",\n                name: \"James Tedesco\",\n                position: \"Fullback\",\n                team: \"Sydney Roosters\",\n                price: 720000,\n                points: 1320,\n                form: 9.2,\n                ownership: 52.1,\n                breakeven: 58,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            {\n                id: \"4\",\n                name: \"Daly Cherry-Evans\",\n                position: \"Halfback\",\n                team: \"Manly Sea Eagles\",\n                price: 650000,\n                points: 1150,\n                form: 7.5,\n                ownership: 28.3,\n                breakeven: 68,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            {\n                id: \"5\",\n                name: \"Cameron Munster\",\n                position: \"Five-eighth\",\n                team: \"Melbourne Storm\",\n                price: 700000,\n                points: 1200,\n                form: 8.1,\n                ownership: 41.5,\n                breakeven: 62,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }\n        ];\n    };\n    APIService.getMockTradeRecommendations = function getMockTradeRecommendations() {\n        return [\n            {\n                id: \"1\",\n                user_id: \"demo\",\n                player_out_id: \"1\",\n                player_in_id: \"3\",\n                reason: \"Superior form and favorable upcoming fixtures make this an optimal trade opportunity.\",\n                confidence: 87,\n                price_change: 50000,\n                points_gain: 25,\n                risk_level: \"low\",\n                created_at: new Date().toISOString(),\n                player_out: this.getMockPlayers()[0],\n                player_in: this.getMockPlayers()[2]\n            }\n        ];\n    };\n    APIService.getMockInjuryReports = function getMockInjuryReports() {\n        return [\n            {\n                id: \"1\",\n                player_id: \"1\",\n                injury_type: \"Hamstring\",\n                status: \"Minor concern\",\n                severity: \"minor\",\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                player: this.getMockPlayers()[0]\n            }\n        ];\n    };\n    // Health check for API services\n    APIService.healthCheck = function healthCheck() {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var results, error, response, error1;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        results = {\n                            supabase: false,\n                            localApi: false,\n                            cache: false\n                        };\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.AnalyticsService.getDashboardStats()\n                        ];\n                    case 2:\n                        _state.sent();\n                        results.supabase = true;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Supabase health check failed:\", error);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        _state.trys.push([\n                            4,\n                            6,\n                            ,\n                            7\n                        ]);\n                        return [\n                            4,\n                            fetch(\"\".concat(API_BASE_URL, \"/health\"))\n                        ];\n                    case 5:\n                        response = _state.sent();\n                        results.localApi = response.ok;\n                        return [\n                            3,\n                            7\n                        ];\n                    case 6:\n                        error1 = _state.sent();\n                        console.error(\"Local API health check failed:\", error1);\n                        return [\n                            3,\n                            7\n                        ];\n                    case 7:\n                        // Check cache\n                        try {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(\"health_check\", \"ok\");\n                            results.cache = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(\"health_check\") === \"ok\";\n                        } catch (error) {\n                            console.error(\"Cache health check failed:\", error);\n                        }\n                        return [\n                            2,\n                            results\n                        ];\n                }\n            });\n        })();\n    };\n    // Clear all caches\n    APIService.clearCache = function clearCache() {\n        _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.clear();\n    };\n    return APIService;\n}();\n/* harmony default export */ __webpack_exports__[\"default\"] = (APIService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/services/api.ts\n"));

/***/ })

});