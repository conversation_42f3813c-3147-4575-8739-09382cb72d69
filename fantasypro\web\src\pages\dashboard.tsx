import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import { motion } from 'framer-motion';
import Layout from '../components/Layout';
import TradeAnalysis from '../components/TradeAnalysis';
import UniversalSearch from '../components/UniversalSearch';
import PredictiveSearch from '../components/PredictiveSearch';
import SportRadarTest from '../components/SportRadarTest';
import APIService from '../services/api';
import {
  ChartBarIcon,
  TrophyIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ClockIcon,
  FireIcon,
  MagnifyingGlassIcon,
  StarIcon
} from '@heroicons/react/24/outline';

// Types based on SuperCoach API structure
interface SuperCoachDashboardData {
  total_players: number;
  active_players: number;
  total_teams: number;
  last_updated: string;
  top_scorers: Array<{
    id: number;
    name: string;
    team: string;
    position: string;
    current_price: number;
    season_points: number;
    season_average: number;
    form_rating: number;
  }>;
  price_risers: Array<{
    id: number;
    name: string;
    team: string;
    position: string;
    current_price: number;
    season_points: number;
  }>;
  price_fallers: Array<{
    id: number;
    name: string;
    team: string;
    position: string;
    current_price: number;
    season_points: number;
  }>;
  best_value: Array<{
    id: number;
    name: string;
    team: string;
    position: string;
    current_price: number;
    value_score: number;
  }>;
  injury_watch: Array<{
    id: number;
    name: string;
    team: string;
    position: string;
    injury_status: string;
    ownership_percentage: number;
  }>;
}

interface AIRecommendations {
  recommendations: Array<{
    type: string;
    title: string;
    description: string;
    confidence: number;
    priority: number;
    data: any;
    reasoning: string[];
    created_at: string;
  }>;
  total_count: number;
  generated_at: string;
}

interface PricePredictions {
  predictions: Array<{
    player_id: number;
    player_name: string;
    team: string;
    position: string;
    current_price: number;
    predicted_price: number;
    price_change: number;
    confidence: number;
    breakeven_required: number;
  }>;
  total_count: number;
  generated_at: string;
}

const Dashboard: NextPage = () => {
  const [dashboardData, setDashboardData] = useState<SuperCoachDashboardData | null>(null);
  const [aiRecommendations, setAiRecommendations] = useState<AIRecommendations | null>(null);
  const [pricePredictions, setPricePredictions] = useState<PricePredictions | null>(null);
  const [injuryData, setInjuryData] = useState<any>(null);
  const [suspensionData, setSuspensionData] = useState<any>(null);
  const [newsData, setNewsData] = useState<any>(null);
  const [ladderData, setLadderData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [showTradeAnalysis, setShowTradeAnalysis] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<any>(null);

  // Button handlers
  const handlePlayerSelect = (player: any) => {
    setSelectedPlayer(player);
    console.log('Player selected for analysis:', player);
  };

  const handleCaptainSelect = (player: any) => {
    console.log('Captain selected:', player);
    // Add captain selection logic here
    alert(`${player.name} set as captain!`);
  };

  const handleTradeAnalysis = (player?: any) => {
    if (player) setSelectedPlayer(player);
    setShowTradeAnalysis(true);
  };

  const handleExecuteTrade = (tradeData: any) => {
    console.log('Executing trade:', tradeData);
    // Add actual trade execution logic here
    alert('Trade executed successfully!');
    setShowTradeAnalysis(false);
  };

  const handleViewFullLadder = () => {
    console.log('Viewing full ladder');
    // Navigate to full ladder page
    window.open('/ladder', '_blank');
  };

  const handleRefreshData = async () => {
    console.log('Refreshing dashboard data...');
    setLoading(true);

    try {
      // Clear cache and fetch fresh data
      APIService.clearCache();

      // Re-fetch all data
      const [dashboardStats, topPerformers, priceRisers, injuryReports] = await Promise.all([
        APIService.getDashboardStats(false), // Don't use cache
        APIService.getTopPerformers(10, false),
        APIService.getPriceRisers(10, false),
        APIService.getInjuryReports(false)
      ]);

      // Update dashboard data
      setDashboardData({
        total_players: dashboardStats.total_players || 581,
        active_players: dashboardStats.total_players || 581,
        total_teams: dashboardStats.total_teams || 17,
        last_updated: new Date().toISOString(),
        top_scorers: topPerformers.map(p => ({
          id: parseInt(p.id),
          name: p.name,
          team: p.team,
          position: p.position,
          current_price: p.price,
          season_points: p.points,
          season_average: p.points / 20,
          form_rating: p.form
        })),
        price_risers: priceRisers.map(p => ({
          id: parseInt(p.id),
          name: p.name,
          team: p.team,
          position: p.position,
          current_price: p.price,
          season_points: p.points
        })),
        price_fallers: [],
        best_value: topPerformers.slice(0, 5).map(p => ({
          id: parseInt(p.id),
          name: p.name,
          team: p.team,
          position: p.position,
          current_price: p.price,
          value_score: p.points / (p.price / 100000)
        })),
        injury_watch: injuryReports.map(r => ({
          id: parseInt(r.id),
          name: r.player?.name || 'Unknown',
          team: r.player?.team || 'Unknown',
          position: r.player?.position || 'Unknown',
          injury_status: r.status,
          ownership_percentage: r.player?.ownership || 0
        }))
      });

      setLoading(false);
      alert('Data refreshed successfully!');
    } catch (error) {
      console.error('Error refreshing data:', error);
      setLoading(false);
      alert('Error refreshing data. Please try again.');
    }
  };

  useEffect(() => {
    const fetchAllData = async () => {
      try {
        // Fetch dashboard data from our optimized cloud API with Supabase integration
        const [dashboardStats, topPerformers, priceRisers, injuryReports] = await Promise.all([
          APIService.getDashboardStats(),
          APIService.getTopPerformers(10),
          APIService.getPriceRisers(10),
          APIService.getInjuryReports()
        ]);

        // Convert to expected format
        setDashboardData({
          total_players: dashboardStats.total_players || 581,
          active_players: dashboardStats.total_players || 581,
          total_teams: dashboardStats.total_teams || 17,
          last_updated: dashboardStats.last_updated || new Date().toISOString(),
          top_scorers: topPerformers.map(p => ({
            id: parseInt(p.id),
            name: p.name,
            team: p.team,
            position: p.position,
            current_price: p.price,
            season_points: p.points,
            season_average: p.points / 20, // Assuming 20 rounds
            form_rating: p.form
          })),
          price_risers: priceRisers.map(p => ({
            id: parseInt(p.id),
            name: p.name,
            team: p.team,
            position: p.position,
            current_price: p.price,
            season_points: p.points
          })),
          price_fallers: [],
          best_value: topPerformers.slice(0, 5).map(p => ({
            id: parseInt(p.id),
            name: p.name,
            team: p.team,
            position: p.position,
            current_price: p.price,
            value_score: p.points / (p.price / 100000)
          })),
          injury_watch: injuryReports.map(r => ({
            id: parseInt(r.id),
            name: r.player?.name || 'Unknown',
            team: r.player?.team || 'Unknown',
            position: r.player?.position || 'Unknown',
            injury_status: r.status,
            ownership_percentage: r.player?.ownership || 0
          }))
        });

        // Fetch AI trade recommendations
        try {
          const tradeRecommendations = await APIService.getTradeRecommendations('demo-user');
          setAiRecommendations({
            recommendations: tradeRecommendations.map(rec => ({
              type: 'trade',
              title: `Trade ${rec.player_out?.name} for ${rec.player_in?.name}`,
              description: rec.reason,
              confidence: rec.confidence / 100,
              priority: 1,
              data: {
                player_out: rec.player_out,
                player_in: rec.player_in,
                expected_gain: rec.points_gain
              },
              reasoning: [rec.reason],
              created_at: rec.created_at
            })),
            total_count: tradeRecommendations.length,
            generated_at: new Date().toISOString()
          });
        } catch (error) {
          console.error('Error fetching trade recommendations:', error);
          // Fallback to mock data
          setAiRecommendations({
            recommendations: [
              {
                type: 'trade',
                title: 'Optimal Trade Opportunity',
                confidence: 0.85,
                description: 'Better value trade opportunity',
                reasoning: ['Player A has declining form while Player B is trending upward'],
                data: {
                  player_out: { name: 'Player A', team: 'Team A', price: 500000, position: 'CTW' },
                  player_in: { name: 'Player B', team: 'Team B', price: 480000, position: 'CTW' },
                  expected_gain: 15.2
                },
                priority: 1,
                created_at: new Date().toISOString()
              }
            ],
            total_count: 1,
            generated_at: new Date().toISOString()
          });
        }

        // Set mock price predictions
        setPricePredictions({
          predictions: [
            { player: "James Tedesco", current_price: 817700, predicted_price: 825000, price_change: 7300 },
            { player: "Herbie Farnworth", current_price: 815400, predicted_price: 820000, price_change: 4600 }
          ]
        });

        // Fetch injury and suspension data
        try {
          const injuryResponse = await fetch('http://localhost:8004/injuries-suspensions/alerts?limit=5');
          const injuryAlerts = await injuryResponse.json();
          setInjuryData(injuryAlerts);

          const suspensionResponse = await fetch('http://localhost:8004/injuries-suspensions');
          const suspensionData = await suspensionResponse.json();
          setSuspensionData(suspensionData);
        } catch (injuryError) {
          console.error('Error fetching injury/suspension data:', injuryError);
          // Set fallback data
          setInjuryData({
            alerts: [
              { player_name: 'Cameron Murray', team: 'South Sydney Rabbitohs', reason: 'Achilles', status: 'Injured' },
              { player_name: 'Dylan Brown', team: 'Parramatta Eels', reason: 'Suspension', status: 'Suspended' }
            ]
          });
        }

        // Fetch news data
        try {
          const newsResponse = await fetch('http://localhost:8004/news/trending?limit=5');
          const newsData = await newsResponse.json();
          setNewsData(newsData);
        } catch (newsError) {
          console.error('Error fetching news data:', newsError);
        }

        // Fetch SuperCoach ladder data
        try {
          const ladderResponse = await fetch('http://localhost:8004/supercoach/ladder?limit=10');
          const ladderData = await ladderResponse.json();
          setLadderData(ladderData);
        } catch (ladderError) {
          console.error('Error fetching ladder data:', ladderError);
        }

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Fallback to mock data if API fails
        try {
          const fallbackResponse = await fetch('http://localhost:8004/stats');
          const fallbackData = await fallbackResponse.json();
          // Convert fallback data to SuperCoach format
          setDashboardData({
            total_players: 500,
            active_players: 450,
            total_teams: 17,
            last_updated: new Date().toISOString(),
            top_scorers: [],
            price_risers: [],
            price_fallers: [],
            best_value: [],
            injury_watch: []
          });
        } catch (fallbackError) {
          console.error('Fallback data fetch failed:', fallbackError);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading dashboard...</div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-red-400 text-xl">Error loading dashboard data</div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return `$${(amount / 1000000).toFixed(2)}M`;
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-red-400 bg-red-900/20';
      case 'medium': return 'text-yellow-400 bg-yellow-900/20';
      case 'low': return 'text-green-400 bg-green-900/20';
      default: return 'text-gray-400 bg-gray-900/20';
    }
  };

  return (
    <Layout>
      <Head>
        <title>Dashboard - FantasyPro</title>
        <meta name="description" content="Your personal analytics dashboard for NRL SuperCoach" />
      </Head>

      <div className="space-y-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold themed-text-primary mb-2">Dashboard</h1>
          <p className="themed-text-tertiary">Your personal analytics dashboard for all things SuperCoach NRL</p>
        </div>

        {/* Universal Search Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="card-premium p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <MagnifyingGlassIcon className="w-6 h-6 text-green-400" />
            <h2 className="text-xl font-semibold themed-text-primary">Universal Player Search</h2>
            <span className="status-live">581 Players</span>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">
                🔍 Predictive Player Search
              </label>
              <PredictiveSearch
                placeholder="Type 'Na' for Nathan Cleary, 'Re' for Reece Walsh..."
                onPlayerSelect={handlePlayerSelect}
                showPlayerDetails={true}
                maxResults={8}
                minQueryLength={1}
              />
            </div>

            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">
                👑 Captain Selection
              </label>
              <PredictiveSearch
                placeholder="Search for captain..."
                showCaptainOption={true}
                onCaptainSelect={handleCaptainSelect}
                onPlayerSelect={handlePlayerSelect}
                showPlayerDetails={true}
                maxResults={6}
                minQueryLength={1}
              />
            </div>
          </div>

          {selectedPlayer && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="mt-6 pt-6 border-t themed-border"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold themed-text-primary">Selected: {selectedPlayer.name}</h3>
                  <p className="text-sm themed-text-tertiary">{selectedPlayer.position} - {selectedPlayer.team}</p>
                </div>
                <div className="flex space-x-3">
                  <button
                    type="button" onClick={() => handleTradeAnalysis(selectedPlayer)}
                    className="btn-primary btn-ripple flex items-center space-x-2"
                  >
                    <ArrowTrendingUpIcon className="w-4 h-4" />
                    <span>Analyze Trade</span>
                  </button>
                  <button
                    type="button" onClick={() => handleCaptainSelect(selectedPlayer)}
                    className="btn-secondary btn-ripple flex items-center space-x-2"
                  >
                    <StarIcon className="w-4 h-4" />
                    <span>Set Captain</span>
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* SportRadar API Test */}
        <SportRadarTest />

        {/* Top Stats Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Total Points */}
            <motion.div
              className="card-premium magnetic glow-pulse-green p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <TrophyIcon className="w-5 h-5 text-green-400" />
                  <span className="text-sm themed-text-tertiary font-medium">Total Points</span>
                </div>
                <span className="text-xs text-green-400 font-semibold">+7%</span>
              </div>
              <div className="text-3xl font-bold themed-text-primary mb-2">
                {dashboardData?.active_players.toLocaleString() || '581'}
              </div>
              <div className="text-sm themed-text-secondary">Active Players</div>
            </motion.div>

            {/* Live Round Score */}
            <motion.div
              className="card-premium magnetic glow-pulse-blue p-6 animate-stagger-1"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <ChartBarIcon className="w-5 h-5 text-blue-400" />
                  <span className="text-sm themed-text-tertiary font-medium">Live Round Score</span>
                </div>
                <span className="text-xs text-blue-400 font-semibold">+12</span>
              </div>
              <div className="text-3xl font-bold themed-text-primary mb-2">
                {dashboardData?.total_teams || '17'}
              </div>
              <div className="text-sm themed-text-secondary">
                NRL Teams
              </div>
            </motion.div>

            {/* Trades Available */}
            <motion.div
              className="card-premium magnetic shadow-glow-orange p-6 animate-stagger-2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <ArrowTrendingUpIcon className="w-5 h-5 text-orange-400" />
                  <span className="text-sm themed-text-tertiary font-medium">Trades Available</span>
                </div>
                <span className="text-xs text-orange-400 font-semibold">-2</span>
              </div>
              <div className="text-3xl font-bold themed-text-primary mb-2">
                {aiRecommendations?.recommendations.filter(r => r.type === 'trade').length || '1'}
              </div>
              <div className="text-sm themed-text-secondary">
                AI Trade Recs
              </div>
            </motion.div>

            {/* Salary Cap */}
            <motion.div
              className="card-premium magnetic shadow-glow-green p-6 animate-stagger-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <CurrencyDollarIcon className="w-5 h-5 text-green-400" />
                  <span className="text-sm themed-text-tertiary font-medium">Salary Cap</span>
                </div>
                <span className="text-xs text-red-400 font-semibold">-$50k</span>
              </div>
              <div className="text-3xl font-bold themed-text-primary mb-2">
                {pricePredictions?.predictions.filter(p => p.price_change > 0).length || '2'}
              </div>
              <div className="text-sm themed-text-secondary">
                Price Risers
              </div>
            </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Multiple Widgets */}
          <div className="lg:col-span-2 space-y-8">
            {/* Trade Recommendations */}
            <motion.div
              className="card-premium shimmer tilt"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
                <div className="p-6 themed-border-b">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
                    <div className="flex items-center space-x-3">
                      <ArrowTrendingUpIcon className="w-6 h-6 text-green-400" />
                      <h2 className="text-xl font-semibold themed-text-primary">Trade Recommendations</h2>
                    </div>
                    <div className="flex flex-col lg:flex-row lg:items-center space-y-2 lg:space-y-0 lg:space-x-4">
                      <span className="text-sm themed-text-tertiary">Strategic player transfers for optimal ROI powered by our advanced AI trade engine</span>
                      <button type="button" className="text-green-400 hover:text-green-300 text-sm font-medium status-live px-3 py-1 rounded-full bg-green-400/10">
                        Live Analysis
                      </button>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <div className="space-y-6">
                    {aiRecommendations?.recommendations.filter(r => r.type === 'trade').slice(0, 3).map((rec, index) => (
                      <div key={index} className="card-glow magnetic animate-slide-in-left p-5 themed-card-secondary rounded-xl" style={{animationDelay: `${index * 0.1}s`}}>
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-center">
                          {/* Player Out */}
                          <div className="flex items-center space-x-4">
                            <div className="w-10 h-10 bg-red-500/20 rounded-full flex items-center justify-center shadow-lg">
                              <span className="text-red-400 text-xs font-bold">OUT</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="font-semibold themed-text-primary text-base truncate">{rec.data.player_out?.name || 'Nathan Cleary'}</div>
                              <div className="text-sm themed-text-tertiary">
                                {rec.data.player_out?.team || 'Penrith Panthers'} • {formatCurrency(rec.data.player_out?.price || 587300)}
                              </div>
                              <div className="text-xs themed-text-secondary">
                                {rec.data.player_out?.position || 'Halfback'}
                              </div>
                            </div>
                          </div>

                          {/* Arrow & Stats */}
                          <div className="flex flex-col items-center space-y-3">
                            <ArrowTrendingUpIcon className="w-8 h-8 text-green-400" />
                            <div className="text-center">
                              <div className="text-base font-semibold text-green-400">+{rec.data.expected_gain?.toFixed(1) || '15.2'} pts</div>
                              <div className="text-xs themed-text-tertiary">{Math.round(rec.confidence * 100) || 87}% confidence</div>
                            </div>
                          </div>

                          {/* Player In */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4 flex-1">
                              <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center shadow-lg">
                                <span className="text-green-400 text-xs font-bold">IN</span>
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="font-semibold themed-text-primary text-base truncate">{rec.data.player_in?.name || 'James Tedesco'}</div>
                                <div className="text-sm themed-text-tertiary">
                                  {rec.data.player_in?.team || 'Sydney Roosters'} • {formatCurrency(rec.data.player_in?.price || 587300)}
                                </div>
                                <div className="text-xs themed-text-secondary">
                                  {rec.data.player_in?.position || 'Fullback'}
                                </div>
                              </div>
                            </div>
                            <button
                              type="button" onClick={() => {
                                console.log('Executing trade:', rec);
                                setShowTradeAnalysis(true);
                                // Add trade execution logic here
                              }}
                              className="btn-primary btn-ripple ml-4 px-5 py-2.5 text-sm font-semibold"
                            >
                              Execute
                            </button>
                          </div>
                        </div>

                        {/* AI Reasoning */}
                        <div className="mt-4 pt-4 themed-border-t">
                          <div className="text-sm themed-text-tertiary">
                            <span className="font-semibold text-blue-400">AI Insight:</span> {rec.reasoning?.[0] || rec.description || 'Optimal trade opportunity based on form and financial upswing potential.'}
                          </div>
                        </div>
                      </div>
                    )) || (
                      <div className="text-center py-8 text-slate-400">
                        <div className="text-sm">Loading AI trade recommendations...</div>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>

            {/* Injury Oracle */}
            <motion.div
              className="card-premium shadow-glow-red float"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <div className="p-6 themed-border-b">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
                  <div className="flex items-center space-x-3">
                    <ExclamationTriangleIcon className="w-6 h-6 text-red-400" />
                    <h2 className="text-xl font-semibold themed-text-primary">Injury Oracle</h2>
                    <span className="status-live px-3 py-1 rounded-full bg-red-400/10">Live</span>
                  </div>
                  <p className="text-sm themed-text-tertiary">
                    Real-time injury intel and impact analysis
                  </p>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-5">
                  {injuryData?.alerts?.slice(0, 5).map((injury, index) => (
                    <div key={index} className="flex items-start space-x-4 p-3 themed-card-secondary rounded-lg">
                      <div className={`w-3 h-3 rounded-full mt-2 shadow-lg ${
                        injury.status === 'Season Ending' ? 'bg-red-600' :
                        injury.status === 'Injured' ? 'bg-red-400' :
                        injury.status === 'Short Term' ? 'bg-yellow-400' :
                        'bg-orange-400'
                      }`}></div>
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                          <div className="font-semibold themed-text-primary text-base">{injury.player_name}</div>
                          <span className={`px-3 py-1 rounded-full text-xs font-semibold self-start ${
                            injury.status === 'Season Ending' ? 'text-red-400 bg-red-900/20' :
                            injury.status === 'Injured' ? 'text-red-400 bg-red-900/20' :
                            injury.status === 'Short Term' ? 'text-yellow-400 bg-yellow-900/20' :
                            'text-orange-400 bg-orange-900/20'
                          }`}>
                            {injury.status}
                          </span>
                        </div>
                        <div className="text-sm themed-text-tertiary mt-2">
                          {injury.team} • {injury.reason}
                        </div>
                        <div className="text-xs themed-text-secondary mt-1">
                          Return: {injury.expected_return}
                        </div>
                      </div>
                    </div>
                  )) || (
                    // Fallback to AI injury alerts if no real data
                    aiRecommendations?.recommendations.filter(r => r.type === 'injury_alert').slice(0, 3).map((rec, index) => (
                      <div key={index} className="flex items-start space-x-4 p-3 themed-card-secondary rounded-lg">
                        <div className="w-3 h-3 rounded-full mt-2 bg-yellow-400 shadow-lg"></div>
                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                            <div className="font-semibold themed-text-primary text-base">{rec.data.player_name || 'Player'}</div>
                            <span className="px-3 py-1 rounded-full text-xs font-semibold text-yellow-400 bg-yellow-900/20 self-start">
                              Monitor
                            </span>
                          </div>
                          <div className="text-sm themed-text-tertiary mt-2">
                            {rec.data.team || 'Team'} • {rec.data.injury_status || 'Injury concern'}
                          </div>
                          <div className="text-xs themed-text-secondary mt-1">
                            AI Alert: {rec.description}
                          </div>
                        </div>
                      </div>
                    )) || (
                      <div className="text-center py-8 themed-text-tertiary">
                        <div className="text-sm">Loading injury data...</div>
                      </div>
                    )
                  )}
                </div>

                <div className="mt-6 pt-4 border-t border-slate-700">
                  <button type="button" className="w-full text-center text-sm text-green-400 hover:text-green-300 font-medium">
                    View All Injuries →
                  </button>
                </div>
              </div>
            </motion.div>

            {/* Captain Optimizer */}
            <motion.div
              className="card-premium shadow-glow-orange float-delayed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <div className="p-6 themed-border-b">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
                  <div className="flex items-center space-x-3">
                    <FireIcon className="w-6 h-6 text-orange-400" />
                    <h2 className="text-xl font-semibold themed-text-primary">Captain Optimizer</h2>
                  </div>
                  <p className="text-sm themed-text-tertiary">
                    AI-powered captaincy recommendations
                  </p>
                </div>
              </div>

              <div className="p-6">
                {(() => {
                  const captainRec = aiRecommendations?.recommendations.find(r => r.type === 'captain');
                  const topScorer = dashboardData?.top_performers?.[0];

                  if (captainRec) {
                    return (
                      <>
                        <div className="text-center mb-6">
                          <div className="text-2xl font-bold themed-text-primary mb-2">
                            {captainRec.data.player_name || 'AI Captain Pick'}
                          </div>
                          <div className="text-sm themed-text-tertiary mb-3">
                            Projected: {captainRec.data.recent_average?.toFixed(1) || 'N/A'} pts
                          </div>
                          <div className="flex items-center justify-center space-x-4">
                            <span className="text-xl font-bold text-green-400">
                              {captainRec.data.form_rating?.toFixed(1) || 'N/A'}/10
                            </span>
                            <span className="text-sm themed-text-tertiary">
                              {Math.round(captainRec.confidence * 100)}% confidence
                            </span>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div className="flex justify-between text-sm">
                            <span className="themed-text-tertiary font-medium">Team:</span>
                            <span className="themed-text-primary font-semibold">{captainRec.data.team || 'Unknown'}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="themed-text-tertiary font-medium">Position:</span>
                            <span className="themed-text-primary font-semibold">{captainRec.data.position || 'Unknown'}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="themed-text-tertiary font-medium">Momentum:</span>
                            <span className="themed-text-primary font-semibold">{captainRec.data.momentum ? `${Math.round(captainRec.data.momentum * 100)}%` : 'High'}</span>
                          </div>
                        </div>

                        <div className="mt-6 pt-4 themed-border-t">
                          <div className="text-sm themed-text-tertiary mb-3 font-medium">AI Reasoning:</div>
                          <div className="text-sm themed-text-secondary leading-relaxed">
                            {captainRec.reasoning?.[0] || captainRec.description}
                          </div>
                        </div>
                      </>
                    );
                  } else if (topScorer) {
                    return (
                      <>
                        <div className="text-center mb-4">
                          <div className="text-xl font-bold text-white mb-1">
                            {topScorer.name}
                          </div>
                          <div className="text-sm text-slate-400 mb-2">
                            Season avg: {topScorer.season_average?.toFixed(1) || 'N/A'} pts
                          </div>
                          <div className="flex items-center justify-center space-x-2">
                            <span className="text-lg font-bold text-green-400">
                              {topScorer.form_rating?.toFixed(1) || 'N/A'}/10
                            </span>
                            <span className="text-sm text-slate-400">
                              Top scorer
                            </span>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="flex justify-between text-sm">
                            <span className="text-slate-400">Team:</span>
                            <span className="text-white">{topScorer.team}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-slate-400">Position:</span>
                            <span className="text-white">{topScorer.position}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-slate-400">Season Points:</span>
                            <span className="text-white">{topScorer.season_points?.toLocaleString() || 'N/A'}</span>
                          </div>
                        </div>
                      </>
                    );
                  } else {
                    return (
                      <div className="text-center py-8 text-slate-400">
                        <div className="text-sm">Loading captain recommendations...</div>
                      </div>
                    );
                  }
                })()}

                <button type="button" className="w-full mt-4 btn-primary btn-ripple shadow-glow-orange">
                  Set as Captain
                </button>
              </div>
            </motion.div>
          </div>

          {/* Right Column - News and Ladder */}
          <div className="space-y-6">
            {/* Trending News */}
            <motion.div
              className="card-premium shadow-glow-blue animate-slide-in-right"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              <div className="p-6 themed-border-b">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
                  <div className="flex items-center space-x-3">
                    <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                    </svg>
                    <h2 className="text-xl font-semibold themed-text-primary">Trending News</h2>
                    <span className="status-live px-3 py-1 rounded-full bg-blue-400/10">Live</span>
                  </div>
                  <p className="text-sm themed-text-tertiary">
                    Latest NRL news and updates
                  </p>
                </div>
              </div>

                <div className="p-6">
                <div className="space-y-5">
                  {newsData?.articles?.slice(0, 5).map((article, index) => (
                    <div key={index} className="flex items-start space-x-4 p-3 themed-card-secondary rounded-lg hover:bg-opacity-80 transition-all duration-200">
                      <div className="w-3 h-3 rounded-full mt-2 bg-blue-400 shadow-lg"></div>
                      <div className="flex-1 min-w-0">
                        <div className="font-semibold themed-text-primary text-sm hover:text-blue-400 cursor-pointer transition-colors duration-200">
                          <a href={article.url} target="_blank" rel="noopener noreferrer" className="line-clamp-2">
                            {article.title}
                          </a>
                        </div>
                        <div className="text-xs themed-text-tertiary mt-2">
                          {article.source} • {article.time_ago}
                        </div>
                        {article.summary && (
                          <div className="text-xs themed-text-secondary mt-2 line-clamp-2 leading-relaxed">
                            {article.summary}
                          </div>
                        )}
                      </div>
                    </div>
                  )) || (
                    // Fallback news items
                    <>
                      <div className="flex items-start space-x-4 p-3 themed-card-secondary rounded-lg">
                        <div className="w-3 h-3 rounded-full mt-2 bg-blue-400 shadow-lg"></div>
                        <div className="flex-1 min-w-0">
                          <div className="font-semibold themed-text-primary text-sm">
                            Panthers secure top spot with dominant win
                          </div>
                          <div className="text-xs themed-text-tertiary mt-2">
                            NRL.com • 2 hours ago
                          </div>
                        </div>
                      </div>

                      <div className="flex items-start space-x-4 p-3 themed-card-secondary rounded-lg">
                        <div className="w-3 h-3 rounded-full mt-2 bg-blue-400 shadow-lg"></div>
                        <div className="flex-1 min-w-0">
                          <div className="font-semibold themed-text-primary text-sm">
                            Origin stars return to club duties
                          </div>
                          <div className="text-xs themed-text-tertiary mt-2">
                            NRL.com • 4 hours ago
                          </div>
                        </div>
                      </div>

                      <div className="flex items-start space-x-4 p-3 themed-card-secondary rounded-lg">
                        <div className="w-3 h-3 rounded-full mt-2 bg-blue-400 shadow-lg"></div>
                        <div className="flex-1 min-w-0">
                          <div className="font-semibold themed-text-primary text-sm">
                            Trade period heating up
                          </div>
                          <div className="text-xs themed-text-tertiary mt-2">
                            NRL.com • 6 hours ago
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>

                <div className="mt-6 pt-4 border-t border-slate-700">
                  <button type="button" className="w-full text-center text-sm text-blue-400 hover:text-blue-300 font-medium">
                    View All News →
                  </button>
                </div>
              </div>
            </motion.div>

            {/* SuperCoach Ladder */}
            <motion.div
              className="card-premium shadow-glow-purple animate-slide-in-right animate-stagger-1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
            >
              <div className="p-6 themed-border-b">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
                  <div className="flex items-center space-x-3">
                    <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <h2 className="text-xl font-semibold themed-text-primary">SuperCoach Ladder</h2>
                    <span className="status-premium px-3 py-1 rounded-full bg-yellow-400/10">Live</span>
                  </div>
                  <p className="text-sm themed-text-tertiary">
                    Official SuperCoach rankings
                  </p>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {ladderData?.rankings?.slice(0, 4).map((entry, index) => (
                    <div key={index} className="flex items-center justify-between text-sm p-3 themed-card-secondary rounded-lg">
                      <div className="flex items-center space-x-4">
                        <span className={`font-bold text-lg ${
                          entry.rank === 1 ? 'text-yellow-400' :
                          entry.coach_name === 'Your Team' ? 'text-blue-400' :
                          'themed-text-tertiary'
                        }`}>
                          {entry.rank}
                        </span>
                        <span className="themed-text-primary font-semibold">{entry.coach_name}</span>
                      </div>
                      <span className="text-green-400 font-semibold">
                        {entry.total_points?.toLocaleString() || entry.total_points}
                      </span>
                    </div>
                  )) || (
                    // Fallback ladder data
                    <>
                      <div className="flex items-center justify-between text-sm p-3 themed-card-secondary rounded-lg">
                        <div className="flex items-center space-x-4">
                          <span className="text-yellow-400 font-bold text-lg">1</span>
                          <span className="themed-text-primary font-semibold">SuperCoach Legend</span>
                        </div>
                        <span className="text-green-400 font-semibold">2,847</span>
                      </div>

                      <div className="flex items-center justify-between text-sm p-3 themed-card-secondary rounded-lg">
                        <div className="flex items-center space-x-4">
                          <span className="themed-text-tertiary font-bold text-lg">2</span>
                          <span className="themed-text-primary font-semibold">Fantasy Master</span>
                        </div>
                        <span className="text-green-400 font-semibold">2,821</span>
                      </div>

                      <div className="flex items-center justify-between text-sm p-3 themed-card-secondary rounded-lg">
                        <div className="flex items-center space-x-4">
                          <span className="themed-text-tertiary font-bold text-lg">3</span>
                          <span className="themed-text-primary font-semibold">NRL Guru</span>
                        </div>
                        <span className="text-green-400 font-semibold">2,798</span>
                      </div>

                      <div className="themed-border-t pt-4 mt-4">
                        <div className="flex items-center justify-between text-sm p-3 themed-card-secondary rounded-lg">
                          <div className="flex items-center space-x-4">
                            <span className="text-blue-400 font-bold text-lg">47</span>
                            <span className="themed-text-primary font-semibold">Your Team</span>
                          </div>
                          <span className="text-green-400 font-semibold">2,156</span>
                        </div>
                      </div>
                    </>
                  )}
                </div>

                <div className="mt-6 pt-4 border-t border-slate-700">
                  <button
                    type="button" onClick={handleViewFullLadder}
                    className="w-full text-center text-sm text-yellow-400 hover:text-yellow-300 font-medium transition-colors"
                  >
                    View Full Ladder →
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Trade Analysis Modal */}
        {showTradeAnalysis && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowTradeAnalysis(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <TradeAnalysis
                recommendation={{
                  id: '1',
                  playerOut: {
                    id: '1',
                    name: selectedPlayer?.name || 'Current Player',
                    position: selectedPlayer?.position || 'Position',
                    team: selectedPlayer?.team || 'Team',
                    price: selectedPlayer?.price || 500000,
                    points: selectedPlayer?.points || 100,
                    form: selectedPlayer?.form || 7.5,
                    ownership: 25.5,
                    breakeven: 65
                  },
                  playerIn: {
                    id: '2',
                    name: 'James Tedesco',
                    position: 'Fullback',
                    team: 'Sydney Roosters',
                    price: 720000,
                    points: 1320,
                    form: 9.2,
                    ownership: 52.1,
                    breakeven: 58
                  },
                  reason: 'Superior form and favorable upcoming fixtures make this an optimal trade opportunity.',
                  confidence: 87,
                  priceChange: 50000,
                  pointsGain: 25,
                  riskLevel: 'low'
                }}
                onExecute={(tradeId) => {
                  handleExecuteTrade(tradeId);
                }}
                onDismiss={(tradeId) => {
                  console.log('Trade dismissed:', tradeId);
                  setShowTradeAnalysis(false);
                }}
              />
            </motion.div>
          </motion.div>
        )}
      </div>
    </Layout>
  );
};

export default Dashboard;
