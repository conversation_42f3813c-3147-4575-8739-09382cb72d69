"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/players",{

/***/ "./src/services/supabase.ts":
/*!**********************************!*\
  !*** ./src/services/supabase.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: function() { return /* binding */ AnalyticsService; },\n/* harmony export */   CacheService: function() { return /* binding */ CacheService; },\n/* harmony export */   InjuryService: function() { return /* binding */ InjuryService; },\n/* harmony export */   PlayerService: function() { return /* binding */ PlayerService; },\n/* harmony export */   SquadService: function() { return /* binding */ SquadService; },\n/* harmony export */   TradeService: function() { return /* binding */ TradeService; },\n/* harmony export */   supabase: function() { return /* binding */ supabase; },\n/* harmony export */   supabaseAdmin: function() { return /* binding */ supabaseAdmin; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"./node_modules/next/node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @swc/helpers/_/_class_call_check */ \"./node_modules/next/node_modules/@swc/helpers/esm/_class_call_check.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"./node_modules/next/node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"./node_modules/next/node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"./node_modules/next/node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"./node_modules/next/node_modules/@swc/helpers/esm/_ts_generator.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/process/browser.js\");\n\n\n\n\n\n\n\n// Supabase configuration\nvar supabaseUrl = \"https://fuxpdgsixnbbsdspusmp.supabase.co\" || 0;\nvar supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ3NzE5NzQsImV4cCI6MjA1MDM0Nzk3NH0.Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5\" || 0;\nvar supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDUwODA1NiwiZXhwIjoyMDY2MDg0MDU2fQ.f5qZOHQlAKz_sbIwoGmY4Gub8bGHyx_jxlRYZk-1VPg\";\n// Create Supabase clients\nvar supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\nvar supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n// Player service functions\nvar PlayerService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function PlayerService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, PlayerService);\n    }\n    // Get all players with search and filtering\n    PlayerService.getPlayers = function getPlayers() {\n        var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var query, _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        query = supabase.from(\"players\").select(\"*\");\n                        if (options.search) {\n                            query = query.or(\"name.ilike.%\".concat(options.search, \"%,team.ilike.%\").concat(options.search, \"%,position.ilike.%\").concat(options.search, \"%\"));\n                        }\n                        if (options.position) {\n                            query = query.eq(\"position\", options.position);\n                        }\n                        if (options.team) {\n                            query = query.eq(\"team\", options.team);\n                        }\n                        if (options.limit) {\n                            query = query.limit(options.limit);\n                        }\n                        if (options.offset) {\n                            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n                        }\n                        return [\n                            4,\n                            query.order(\"points\", {\n                                ascending: false\n                            })\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching players:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Get ALL players (for Players page - should return 581 players)\n    PlayerService.getAllPlayers = function getAllPlayers() {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, playersData, playersError, _ref1, statsData, statsError, statsMap, transformedPlayers, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        console.log(\"\\uD83D\\uDD04 PlayerService.getAllPlayers() - fetching complete 581 player dataset from nrl_players table\");\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            4,\n                            ,\n                            5\n                        ]);\n                        return [\n                            4,\n                            supabase.from(\"nrl_players\").select(\"*\").order(\"name\")\n                        ];\n                    case 2:\n                        _ref = _state.sent(), playersData = _ref.data, playersError = _ref.error;\n                        if (playersError) {\n                            console.error(\"❌ Error fetching players from nrl_players table:\", playersError);\n                            throw playersError;\n                        }\n                        return [\n                            4,\n                            supabase.from(\"nrl_player_stats\").select(\"*\")\n                        ];\n                    case 3:\n                        _ref1 = _state.sent(), statsData = _ref1.data, statsError = _ref1.error;\n                        if (statsError) {\n                            console.warn(\"⚠️ Could not fetch supplementary stats:\", statsError);\n                        }\n                        console.log(\"✅ Supabase nrl_players table returned \".concat((playersData === null || playersData === void 0 ? void 0 : playersData.length) || 0, \" players\"));\n                        console.log(\"✅ Supabase nrl_player_stats table returned \".concat((statsData === null || statsData === void 0 ? void 0 : statsData.length) || 0, \" stat records\"));\n                        // Create a map of stats by player name for quick lookup\n                        statsMap = new Map();\n                        if (statsData) {\n                            statsData.forEach(function(stat) {\n                                if (stat.name) {\n                                    statsMap.set(stat.name.toLowerCase(), stat);\n                                }\n                            });\n                        }\n                        // Transform nrl_players data to match our Player interface\n                        transformedPlayers = (playersData === null || playersData === void 0 ? void 0 : playersData.map(function(player) {\n                            var baseStats = player.statistics || {};\n                            var supplementaryStats = statsMap.get(player.name.toLowerCase()) || {};\n                            // Use supplementary stats if available, otherwise use base stats\n                            var price = supplementaryStats.price || baseStats.price || 300000;\n                            var totalPoints = supplementaryStats.total_points || baseStats.total_points || 0;\n                            var averagePoints = supplementaryStats.average_points || baseStats.average_points || 0;\n                            var breakeven = supplementaryStats.breakeven || baseStats.breakeven || 0;\n                            // Calculate average if not available\n                            var calculatedAverage = averagePoints > 0 ? averagePoints : totalPoints > 0 && supplementaryStats.games_played > 0 ? totalPoints / supplementaryStats.games_played : 0;\n                            return {\n                                id: player.id.toString(),\n                                name: player.name,\n                                position: player.position || supplementaryStats.position || \"Unknown\",\n                                team: player.team_name || supplementaryStats.team || \"Unknown Team\",\n                                price: price,\n                                points: totalPoints,\n                                average: calculatedAverage,\n                                form: baseStats.form || calculatedAverage / 10,\n                                ownership: baseStats.ownership || 0,\n                                breakeven: breakeven,\n                                image_url: player.image_url,\n                                created_at: player.created_at,\n                                updated_at: player.updated_at\n                            };\n                        })) || [];\n                        console.log(\"✅ Transformed \".concat(transformedPlayers.length, \" players with enhanced stats\"));\n                        return [\n                            2,\n                            transformedPlayers\n                        ];\n                    case 4:\n                        error = _state.sent();\n                        console.error(\"❌ Error in getAllPlayers:\", error);\n                        throw error;\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get player by ID\n    PlayerService.getPlayer = function getPlayer(id) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").eq(\"id\", id).single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching player:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Search players with predictive algorithm (searches all 581 players)\n    PlayerService.searchPlayers = function searchPlayers(query) {\n        var limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error, transformedResults;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!query || query.length < 2) return [\n                            2,\n                            []\n                        ];\n                        console.log('\\uD83D\\uDD0D Searching nrl_players table for: \"'.concat(query, '\"'));\n                        return [\n                            4,\n                            supabase.from(\"nrl_players\").select(\"*\").or(\"name.ilike.%\".concat(query, \"%,team_name.ilike.%\").concat(query, \"%,position.ilike.%\").concat(query, \"%\")).order(\"name\").limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error searching nrl_players:\", error);\n                            return [\n                                2,\n                                []\n                            ];\n                        }\n                        console.log(\"✅ Search found \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" players in nrl_players table\"));\n                        // Transform search results to match our Player interface\n                        transformedResults = (data === null || data === void 0 ? void 0 : data.map(function(player) {\n                            var stats = player.statistics || {};\n                            var totalPoints = stats.total_points || 0;\n                            var gamesPlayed = stats.games_played || 1;\n                            var averagePoints = gamesPlayed > 0 ? totalPoints / gamesPlayed : 0;\n                            return {\n                                id: player.id.toString(),\n                                name: player.name,\n                                position: player.position || \"Unknown\",\n                                team: player.team_name || \"Unknown Team\",\n                                price: stats.price || 300000,\n                                points: totalPoints,\n                                average: averagePoints,\n                                form: stats.form || averagePoints / 10,\n                                ownership: stats.ownership || 0,\n                                breakeven: stats.breakeven || 0,\n                                image_url: player.image_url,\n                                created_at: player.created_at,\n                                updated_at: player.updated_at\n                            };\n                        })) || [];\n                        return [\n                            2,\n                            transformedResults\n                        ];\n                }\n            });\n        })();\n    };\n    // Get top performers\n    PlayerService.getTopPerformers = function getTopPerformers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").order(\"points\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching top performers:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Get price risers\n    PlayerService.getPriceRisers = function getPriceRisers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").order(\"price\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching price risers:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Update player data\n    PlayerService.updatePlayer = function updatePlayer(id, updates) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").update((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, updates), {\n                                updated_at: new Date().toISOString()\n                            })).eq(\"id\", id).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error updating player:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return PlayerService;\n}();\n// Trade recommendation service\nvar TradeService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function TradeService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, TradeService);\n    }\n    // Get trade recommendations for user\n    TradeService.getTradeRecommendations = function getTradeRecommendations(userId) {\n        var limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"trade_recommendations\").select(\"\\n        *,\\n        player_out:players!player_out_id(*),\\n        player_in:players!player_in_id(*)\\n      \").eq(\"user_id\", userId).order(\"confidence\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching trade recommendations:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Create trade recommendation\n    TradeService.createTradeRecommendation = function createTradeRecommendation(recommendation) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"trade_recommendations\").insert((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, recommendation), {\n                                created_at: new Date().toISOString()\n                            })).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error creating trade recommendation:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Execute trade (log the trade)\n    TradeService.executeTrade = function executeTrade(tradeId, userId) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"executed_trades\").insert({\n                                trade_recommendation_id: tradeId,\n                                user_id: userId,\n                                executed_at: new Date().toISOString()\n                            }).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error executing trade:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return TradeService;\n}();\n// Injury service\nvar InjuryService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function InjuryService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, InjuryService);\n    }\n    // Get current injury reports\n    InjuryService.getInjuryReports = function getInjuryReports() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"injury_reports\").select(\"\\n        *,\\n        player:players(*)\\n      \").order(\"created_at\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching injury reports:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Create injury report\n    InjuryService.createInjuryReport = function createInjuryReport(report) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"injury_reports\").insert((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, report), {\n                                created_at: new Date().toISOString(),\n                                updated_at: new Date().toISOString()\n                            })).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error creating injury report:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return InjuryService;\n}();\n// User squad service\nvar SquadService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function SquadService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, SquadService);\n    }\n    // Get user's squad\n    SquadService.getUserSquad = function getUserSquad(userId) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"user_squads\").select(\"\\n        *,\\n        player:players(*)\\n      \").eq(\"user_id\", userId).order(\"position_in_squad\")\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching user squad:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Add player to squad\n    SquadService.addPlayerToSquad = function addPlayerToSquad(squadEntry) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"user_squads\").insert((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, squadEntry), {\n                                created_at: new Date().toISOString()\n                            })).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error adding player to squad:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Set captain\n    SquadService.setCaptain = function setCaptain(userId, playerId) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        // First, remove captain status from all players\n                        return [\n                            4,\n                            supabase.from(\"user_squads\").update({\n                                is_captain: false,\n                                is_vice_captain: false\n                            }).eq(\"user_id\", userId)\n                        ];\n                    case 1:\n                        _state.sent();\n                        return [\n                            4,\n                            supabase.from(\"user_squads\").update({\n                                is_captain: true\n                            }).eq(\"user_id\", userId).eq(\"player_id\", playerId).select().single()\n                        ];\n                    case 2:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error setting captain:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return SquadService;\n}();\n// Analytics service\nvar AnalyticsService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function AnalyticsService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, AnalyticsService);\n    }\n    // Get dashboard stats\n    AnalyticsService.getDashboardStats = function getDashboardStats() {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, playersCount, teamsCount, injuriesCount;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            Promise.all([\n                                supabase.from(\"players\").select(\"id\", {\n                                    count: \"exact\",\n                                    head: true\n                                }),\n                                supabase.from(\"players\").select(\"team\", {\n                                    count: \"exact\",\n                                    head: true\n                                }).distinct(),\n                                supabase.from(\"injury_reports\").select(\"id\", {\n                                    count: \"exact\",\n                                    head: true\n                                })\n                            ])\n                        ];\n                    case 1:\n                        _ref = _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._.apply(void 0, [\n                            _state.sent(),\n                            3\n                        ]), playersCount = _ref[0], teamsCount = _ref[1], injuriesCount = _ref[2];\n                        return [\n                            2,\n                            {\n                                total_players: playersCount.count || 0,\n                                total_teams: 17,\n                                active_injuries: injuriesCount.count || 0,\n                                last_updated: new Date().toISOString()\n                            }\n                        ];\n                }\n            });\n        })();\n    };\n    // Get trending players\n    AnalyticsService.getTrendingPlayers = function getTrendingPlayers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").order(\"form\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching trending players:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return AnalyticsService;\n}();\n// Cache service for offline support\nvar CacheService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function CacheService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, CacheService);\n    }\n    CacheService.set = function set(key, data) {\n        var cacheData = {\n            data: data,\n            timestamp: Date.now()\n        };\n        localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheData));\n    };\n    CacheService.get = function get(key) {\n        var cached = localStorage.getItem(this.CACHE_PREFIX + key);\n        if (!cached) return null;\n        var _JSON_parse = JSON.parse(cached), data = _JSON_parse.data, timestamp = _JSON_parse.timestamp;\n        if (Date.now() - timestamp > this.CACHE_DURATION) {\n            localStorage.removeItem(this.CACHE_PREFIX + key);\n            return null;\n        }\n        return data;\n    };\n    CacheService.clear = function clear() {\n        var _this = this;\n        Object.keys(localStorage).forEach(function(key) {\n            if (key.startsWith(_this.CACHE_PREFIX)) {\n                localStorage.removeItem(key);\n            }\n        });\n    };\n    return CacheService;\n}();\nCacheService.CACHE_PREFIX = \"fantasypro_cache_\";\nCacheService.CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n;\n/* harmony default export */ __webpack_exports__[\"default\"] = (supabase);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/services/supabase.ts\n"));

/***/ })

});