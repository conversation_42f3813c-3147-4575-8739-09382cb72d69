"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/players",{

/***/ "./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIService: function() { return /* binding */ APIService; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"./node_modules/next/node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_class_call_check */ \"./node_modules/next/node_modules/@swc/helpers/esm/_class_call_check.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"./node_modules/next/node_modules/@swc/helpers/esm/_ts_generator.js\");\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"./src/services/supabase.ts\");\n/* harmony import */ var _sportradar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sportradar */ \"./src/services/sportradar.ts\");\n/* harmony import */ var _dataTransform__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dataTransform */ \"./src/services/dataTransform.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/process/browser.js\");\n\n\n\n\n\n\n// API Configuration\nvar API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8004\";\nvar CACHE_ENABLED = true;\n// Enhanced API service with Supabase integration and local fallback\nvar APIService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function APIService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_3__._)(this, APIService);\n    }\n    // Universal player search with cloud-powered predictive search\n    APIService.searchPlayers = function searchPlayers(query) {\n        var options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var _options_useCache, useCache, _options_fallbackToLocal, fallbackToLocal, _options_limit, limit, cacheKey, cached, players, error, response, data, localError;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _options_useCache = options.useCache, useCache = _options_useCache === void 0 ? CACHE_ENABLED : _options_useCache, _options_fallbackToLocal = options.fallbackToLocal, fallbackToLocal = _options_fallbackToLocal === void 0 ? true : _options_fallbackToLocal, _options_limit = options.limit, limit = _options_limit === void 0 ? 10 : _options_limit;\n                        cacheKey = \"search_players_\".concat(query, \"_\").concat(limit);\n                        // Check cache first\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            10\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.PlayerService.searchPlayers(query, limit)\n                        ];\n                    case 2:\n                        players = _state.sent();\n                        // Cache the results\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, players);\n                        }\n                        return [\n                            2,\n                            players\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Supabase search failed, trying local API:\", error);\n                        if (!fallbackToLocal) return [\n                            3,\n                            9\n                        ];\n                        _state.label = 4;\n                    case 4:\n                        _state.trys.push([\n                            4,\n                            8,\n                            ,\n                            9\n                        ]);\n                        return [\n                            4,\n                            fetch(\"\".concat(API_BASE_URL, \"/players/search?q=\").concat(encodeURIComponent(query), \"&limit=\").concat(limit))\n                        ];\n                    case 5:\n                        response = _state.sent();\n                        if (!response.ok) return [\n                            3,\n                            7\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 6:\n                        data = _state.sent();\n                        return [\n                            2,\n                            data.players || []\n                        ];\n                    case 7:\n                        return [\n                            3,\n                            9\n                        ];\n                    case 8:\n                        localError = _state.sent();\n                        console.error(\"Local API also failed:\", localError);\n                        return [\n                            3,\n                            9\n                        ];\n                    case 9:\n                        // Return mock data as last resort\n                        return [\n                            2,\n                            _this.getMockPlayers().filter(function(p) {\n                                return p.name.toLowerCase().includes(query.toLowerCase()) || p.team.toLowerCase().includes(query.toLowerCase()) || p.position.toLowerCase().includes(query.toLowerCase());\n                            }).slice(0, limit)\n                        ];\n                    case 10:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get all players with advanced filtering\n    APIService.getPlayers = function getPlayers() {\n        var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var _options_useCache, useCache, _options_useRealData, useRealData, cacheKey, cached, url, params, response, data, error, sportRadarPlayers, transformedPlayers, filteredPlayers, searchTerm, start, end, sportRadarError, players, error1;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _options_useCache = options.useCache, useCache = _options_useCache === void 0 ? CACHE_ENABLED : _options_useCache, _options_useRealData = options.useRealData, useRealData = _options_useRealData === void 0 ? true : _options_useRealData;\n                        cacheKey = \"players_\".concat(JSON.stringify(options));\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            5,\n                            ,\n                            6\n                        ]);\n                        console.log(\"\\uD83D\\uDD04 Fetching players from cached data API...\");\n                        url = \"/api/players\";\n                        params = new URLSearchParams();\n                        if (options.search) params.append(\"search\", options.search);\n                        if (options.position) params.append(\"position\", options.position);\n                        if (options.team) params.append(\"team\", options.team);\n                        if (options.limit) params.append(\"limit\", options.limit.toString());\n                        if (params.toString()) {\n                            url += \"?\" + params.toString();\n                        }\n                        return [\n                            4,\n                            fetch(url)\n                        ];\n                    case 2:\n                        response = _state.sent();\n                        if (!response.ok) return [\n                            3,\n                            4\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 3:\n                        data = _state.sent();\n                        if (data.success && data.data.players) {\n                            console.log(\"✅ Loaded \".concat(data.data.players.length, \" players from cached data\"));\n                            if (useCache) {\n                                _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, data.data.players);\n                            }\n                            return [\n                                2,\n                                data.data.players\n                            ];\n                        }\n                        _state.label = 4;\n                    case 4:\n                        return [\n                            3,\n                            6\n                        ];\n                    case 5:\n                        error = _state.sent();\n                        console.error(\"❌ Cached data API failed:\", error);\n                        return [\n                            3,\n                            6\n                        ];\n                    case 6:\n                        _state.trys.push([\n                            6,\n                            12,\n                            ,\n                            13\n                        ]);\n                        if (!useRealData) return [\n                            3,\n                            10\n                        ];\n                        _state.label = 7;\n                    case 7:\n                        _state.trys.push([\n                            7,\n                            9,\n                            ,\n                            10\n                        ]);\n                        return [\n                            4,\n                            _sportradar__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getAllNRLPlayers()\n                        ];\n                    case 8:\n                        sportRadarPlayers = _state.sent();\n                        transformedPlayers = _dataTransform__WEBPACK_IMPORTED_MODULE_2__[\"default\"].transformPlayers(sportRadarPlayers);\n                        // Apply filtering\n                        filteredPlayers = transformedPlayers;\n                        if (options.search) {\n                            searchTerm = options.search.toLowerCase();\n                            filteredPlayers = filteredPlayers.filter(function(player) {\n                                return player.name.toLowerCase().includes(searchTerm) || player.team.toLowerCase().includes(searchTerm);\n                            });\n                        }\n                        if (options.position) {\n                            filteredPlayers = filteredPlayers.filter(function(player) {\n                                return player.position === options.position;\n                            });\n                        }\n                        if (options.team) {\n                            filteredPlayers = filteredPlayers.filter(function(player) {\n                                return player.team === options.team;\n                            });\n                        }\n                        // Apply pagination\n                        if (options.offset || options.limit) {\n                            start = options.offset || 0;\n                            end = options.limit ? start + options.limit : undefined;\n                            filteredPlayers = filteredPlayers.slice(start, end);\n                        }\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, filteredPlayers);\n                        }\n                        return [\n                            2,\n                            filteredPlayers\n                        ];\n                    case 9:\n                        sportRadarError = _state.sent();\n                        console.error(\"SportRadar API failed, falling back to Supabase:\", sportRadarError);\n                        return [\n                            3,\n                            10\n                        ];\n                    case 10:\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.PlayerService.getPlayers(options)\n                        ];\n                    case 11:\n                        players = _state.sent();\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, players);\n                        }\n                        return [\n                            2,\n                            players\n                        ];\n                    case 12:\n                        error1 = _state.sent();\n                        console.error(\"Error fetching players:\", error1);\n                        return [\n                            2,\n                            _this.getMockPlayers()\n                        ];\n                    case 13:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get all players - used by Players page\n    APIService.getAllPlayers = function getAllPlayers() {\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var sportRadarPlayers, response, data, additionalPlayers, sportRadarError, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        console.log(\"\\uD83D\\uDD04 APIService.getAllPlayers() called - attempting to load ALL 581 players\");\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            10,\n                            ,\n                            11\n                        ]);\n                        // First try SportRadar API for complete dataset\n                        console.log(\"\\uD83D\\uDD04 Trying SportRadar API for complete player dataset...\");\n                        return [\n                            4,\n                            _this.getAllNRLPlayers(true)\n                        ];\n                    case 2:\n                        sportRadarPlayers = _state.sent();\n                        if (sportRadarPlayers && sportRadarPlayers.length > 100) {\n                            console.log(\"✅ SportRadar API loaded \".concat(sportRadarPlayers.length, \" players\"));\n                            return [\n                                2,\n                                sportRadarPlayers\n                            ];\n                        }\n                        // Fallback to cached data API\n                        console.log(\"\\uD83D\\uDD04 SportRadar failed, trying cached data API...\");\n                        return [\n                            4,\n                            fetch(\"/api/players\")\n                        ];\n                    case 3:\n                        response = _state.sent();\n                        if (!response.ok) return [\n                            3,\n                            9\n                        ];\n                        return [\n                            4,\n                            response.json()\n                        ];\n                    case 4:\n                        data = _state.sent();\n                        if (!(data.success && data.data.players)) return [\n                            3,\n                            9\n                        ];\n                        console.log(\"✅ Cached data API loaded \".concat(data.data.players.length, \" players\"));\n                        if (!(data.data.players.length < 500)) return [\n                            3,\n                            8\n                        ];\n                        console.log(\"⚠️ Cached data has fewer players than expected, supplementing with SportRadar...\");\n                        _state.label = 5;\n                    case 5:\n                        _state.trys.push([\n                            5,\n                            7,\n                            ,\n                            8\n                        ]);\n                        return [\n                            4,\n                            _this.getAllNRLPlayers(false)\n                        ];\n                    case 6:\n                        additionalPlayers = _state.sent();\n                        if (additionalPlayers && additionalPlayers.length > data.data.players.length) {\n                            console.log(\"✅ SportRadar provided \".concat(additionalPlayers.length, \" players, using that instead\"));\n                            return [\n                                2,\n                                additionalPlayers\n                            ];\n                        }\n                        return [\n                            3,\n                            8\n                        ];\n                    case 7:\n                        sportRadarError = _state.sent();\n                        console.warn(\"⚠️ SportRadar supplement failed:\", sportRadarError);\n                        return [\n                            3,\n                            8\n                        ];\n                    case 8:\n                        return [\n                            2,\n                            data.data.players\n                        ];\n                    case 9:\n                        console.warn(\"⚠️ All APIs failed, falling back to mock data\");\n                        return [\n                            2,\n                            _this.getMockPlayers()\n                        ];\n                    case 10:\n                        error = _state.sent();\n                        console.error(\"❌ APIService.getAllPlayers() error:\", error);\n                        return [\n                            2,\n                            _this.getMockPlayers()\n                        ];\n                    case 11:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get all NRL players from SportRadar\n    APIService.getAllNRLPlayers = function getAllNRLPlayers() {\n        var useCache = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, sportRadarPlayers, transformedPlayers, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"all_nrl_players_transformed\";\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _sportradar__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getAllNRLPlayers()\n                        ];\n                    case 2:\n                        sportRadarPlayers = _state.sent();\n                        transformedPlayers = _dataTransform__WEBPACK_IMPORTED_MODULE_2__[\"default\"].transformPlayers(sportRadarPlayers);\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, transformedPlayers);\n                        }\n                        return [\n                            2,\n                            transformedPlayers\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching all NRL players from SportRadar:\", error);\n                        return [\n                            2,\n                            _this.getMockPlayers()\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get NRL teams from SportRadar\n    APIService.getNRLTeams = function getNRLTeams() {\n        var useCache = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, sportRadarTeams, transformedTeams, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"nrl_teams_transformed\";\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _sportradar__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getNRLTeams()\n                        ];\n                    case 2:\n                        sportRadarTeams = _state.sent();\n                        transformedTeams = _dataTransform__WEBPACK_IMPORTED_MODULE_2__[\"default\"].transformTeams(sportRadarTeams);\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, transformedTeams);\n                        }\n                        return [\n                            2,\n                            transformedTeams\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching NRL teams from SportRadar:\", error);\n                        return [\n                            2,\n                            []\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get injury reports\n    APIService.getInjuryReports = function getInjuryReports() {\n        var useCache = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : CACHE_ENABLED;\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, reports, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"injury_reports\";\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.InjuryService.getInjuryReports()\n                        ];\n                    case 2:\n                        reports = _state.sent();\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, reports);\n                        }\n                        return [\n                            2,\n                            reports\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching injury reports:\", error);\n                        return [\n                            2,\n                            _this.getMockInjuryReports()\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get upcoming fixtures from SportRadar\n    APIService.getUpcomingFixtures = function getUpcomingFixtures() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10, useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, sportRadarFixtures, transformedFixtures, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"upcoming_fixtures_\".concat(limit);\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _sportradar__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getUpcomingFixtures(limit)\n                        ];\n                    case 2:\n                        sportRadarFixtures = _state.sent();\n                        transformedFixtures = _dataTransform__WEBPACK_IMPORTED_MODULE_2__[\"default\"].transformFixtures(sportRadarFixtures);\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, transformedFixtures);\n                        }\n                        return [\n                            2,\n                            transformedFixtures\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching upcoming fixtures from SportRadar:\", error);\n                        return [\n                            2,\n                            []\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get dashboard statistics\n    APIService.getDashboardStats = function getDashboardStats() {\n        var useCache = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : CACHE_ENABLED;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, stats, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"dashboard_stats\";\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.AnalyticsService.getDashboardStats()\n                        ];\n                    case 2:\n                        stats = _state.sent();\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, stats);\n                        }\n                        return [\n                            2,\n                            stats\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching dashboard stats:\", error);\n                        return [\n                            2,\n                            {\n                                total_players: 581,\n                                total_teams: 17,\n                                active_injuries: 12,\n                                last_updated: new Date().toISOString()\n                            }\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get trade recommendations\n    APIService.getTradeRecommendations = function getTradeRecommendations(userId) {\n        var useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : CACHE_ENABLED;\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, recommendations, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"trade_recommendations_\".concat(userId);\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.TradeService.getTradeRecommendations(userId)\n                        ];\n                    case 2:\n                        recommendations = _state.sent();\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, recommendations);\n                        }\n                        return [\n                            2,\n                            recommendations\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching trade recommendations:\", error);\n                        return [\n                            2,\n                            _this.getMockTradeRecommendations()\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Execute trade\n    APIService.executeTrade = function executeTrade(tradeId, userId) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var result, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            2,\n                            ,\n                            3\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.TradeService.executeTrade(tradeId, userId)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        // Clear relevant caches\n                        _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.clear();\n                        return [\n                            2,\n                            result\n                        ];\n                    case 2:\n                        error = _state.sent();\n                        console.error(\"Error executing trade:\", error);\n                        throw error;\n                    case 3:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get top performers\n    APIService.getTopPerformers = function getTopPerformers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10, useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : CACHE_ENABLED;\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, players, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"top_performers_\".concat(limit);\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.PlayerService.getTopPerformers(limit)\n                        ];\n                    case 2:\n                        players = _state.sent();\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, players);\n                        }\n                        return [\n                            2,\n                            players\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching top performers:\", error);\n                        return [\n                            2,\n                            _this.getMockPlayers().slice(0, limit)\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Get price risers\n    APIService.getPriceRisers = function getPriceRisers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10, useCache = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : CACHE_ENABLED;\n        var _this = this;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var cacheKey, cached, players, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        cacheKey = \"price_risers_\".concat(limit);\n                        if (useCache) {\n                            cached = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(cacheKey);\n                            if (cached) return [\n                                2,\n                                cached\n                            ];\n                        }\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.PlayerService.getPriceRisers(limit)\n                        ];\n                    case 2:\n                        players = _state.sent();\n                        if (useCache) {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(cacheKey, players);\n                        }\n                        return [\n                            2,\n                            players\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Error fetching price risers:\", error);\n                        return [\n                            2,\n                            _this.getMockPlayers().slice(0, limit)\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        })();\n    };\n    // Mock data for fallback\n    APIService.getMockPlayers = function getMockPlayers() {\n        return [\n            {\n                id: \"1\",\n                name: \"Nathan Cleary\",\n                position: \"Halfback\",\n                team: \"Penrith Panthers\",\n                price: 750000,\n                points: 1250,\n                form: 8.5,\n                ownership: 45.2,\n                breakeven: 65,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            {\n                id: \"2\",\n                name: \"Kalyn Ponga\",\n                position: \"Fullback\",\n                team: \"Newcastle Knights\",\n                price: 680000,\n                points: 1180,\n                form: 7.8,\n                ownership: 38.7,\n                breakeven: 72,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            {\n                id: \"3\",\n                name: \"James Tedesco\",\n                position: \"Fullback\",\n                team: \"Sydney Roosters\",\n                price: 720000,\n                points: 1320,\n                form: 9.2,\n                ownership: 52.1,\n                breakeven: 58,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            {\n                id: \"4\",\n                name: \"Daly Cherry-Evans\",\n                position: \"Halfback\",\n                team: \"Manly Sea Eagles\",\n                price: 650000,\n                points: 1150,\n                form: 7.5,\n                ownership: 28.3,\n                breakeven: 68,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            },\n            {\n                id: \"5\",\n                name: \"Cameron Munster\",\n                position: \"Five-eighth\",\n                team: \"Melbourne Storm\",\n                price: 700000,\n                points: 1200,\n                form: 8.1,\n                ownership: 41.5,\n                breakeven: 62,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }\n        ];\n    };\n    APIService.getMockTradeRecommendations = function getMockTradeRecommendations() {\n        return [\n            {\n                id: \"1\",\n                user_id: \"demo\",\n                player_out_id: \"1\",\n                player_in_id: \"3\",\n                reason: \"Superior form and favorable upcoming fixtures make this an optimal trade opportunity.\",\n                confidence: 87,\n                price_change: 50000,\n                points_gain: 25,\n                risk_level: \"low\",\n                created_at: new Date().toISOString(),\n                player_out: this.getMockPlayers()[0],\n                player_in: this.getMockPlayers()[2]\n            }\n        ];\n    };\n    APIService.getMockInjuryReports = function getMockInjuryReports() {\n        return [\n            {\n                id: \"1\",\n                player_id: \"1\",\n                injury_type: \"Hamstring\",\n                status: \"Minor concern\",\n                severity: \"minor\",\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                player: this.getMockPlayers()[0]\n            }\n        ];\n    };\n    // Health check for API services\n    APIService.healthCheck = function healthCheck() {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_4__._)(function() {\n            var results, error, response, error1;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_5__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        results = {\n                            supabase: false,\n                            localApi: false,\n                            cache: false\n                        };\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.AnalyticsService.getDashboardStats()\n                        ];\n                    case 2:\n                        _state.sent();\n                        results.supabase = true;\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"Supabase health check failed:\", error);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        _state.trys.push([\n                            4,\n                            6,\n                            ,\n                            7\n                        ]);\n                        return [\n                            4,\n                            fetch(\"\".concat(API_BASE_URL, \"/health\"))\n                        ];\n                    case 5:\n                        response = _state.sent();\n                        results.localApi = response.ok;\n                        return [\n                            3,\n                            7\n                        ];\n                    case 6:\n                        error1 = _state.sent();\n                        console.error(\"Local API health check failed:\", error1);\n                        return [\n                            3,\n                            7\n                        ];\n                    case 7:\n                        // Check cache\n                        try {\n                            _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.set(\"health_check\", \"ok\");\n                            results.cache = _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.get(\"health_check\") === \"ok\";\n                        } catch (error) {\n                            console.error(\"Cache health check failed:\", error);\n                        }\n                        return [\n                            2,\n                            results\n                        ];\n                }\n            });\n        })();\n    };\n    // Clear all caches\n    APIService.clearCache = function clearCache() {\n        _supabase__WEBPACK_IMPORTED_MODULE_0__.CacheService.clear();\n    };\n    return APIService;\n}();\n/* harmony default export */ __webpack_exports__[\"default\"] = (APIService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/services/api.ts\n"));

/***/ })

});