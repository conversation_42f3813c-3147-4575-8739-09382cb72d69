import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import Link from 'next/link';
import { motion } from 'framer-motion';
import Layout from '../components/Layout';
import LoadingSpinner from '../components/LoadingSpinner';
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ExclamationTriangleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'warning' | 'error';
  message: string;
  details?: string;
}

const TestAllFunctionality: NextPage = () => {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Dashboard Page', status: 'pending', message: 'Testing...' },
    { name: 'My Team Page', status: 'pending', message: 'Testing...' },
    { name: 'Players Page', status: 'pending', message: 'Testing...' },
    { name: 'Analytics Page', status: 'pending', message: 'Testing...' },
    { name: 'Trades Page', status: 'pending', message: 'Testing...' },
    { name: 'Injuries Page', status: 'pending', message: 'Testing...' },
    { name: 'Settings Page', status: 'pending', message: 'Testing...' },
    { name: 'Supabase Connection', status: 'pending', message: 'Testing...' },
    { name: 'Player Data Loading', status: 'pending', message: 'Testing...' },
    { name: 'Search Functionality', status: 'pending', message: 'Testing...' }
  ]);

  const [currentTest, setCurrentTest] = useState(0);
  const [allTestsComplete, setAllTestsComplete] = useState(false);

  useEffect(() => {
    runTests();
  }, []);

  const updateTest = (index: number, status: TestResult['status'], message: string, details?: string) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, status, message, details } : test
    ));
  };

  const runTests = async () => {
    console.log('🧪 Starting comprehensive functionality tests...');

    // Test 1: Dashboard Page
    setCurrentTest(0);
    try {
      const response = await fetch('/dashboard');
      if (response.ok) {
        updateTest(0, 'success', 'Dashboard loads successfully');
      } else {
        updateTest(0, 'warning', `Dashboard returned ${response.status}`);
      }
    } catch (error) {
      updateTest(0, 'error', 'Dashboard failed to load', error.message);
    }

    // Test 2: My Team Page
    setCurrentTest(1);
    try {
      const response = await fetch('/my-team');
      if (response.ok) {
        updateTest(1, 'success', 'My Team page loads successfully');
      } else {
        updateTest(1, 'warning', `My Team returned ${response.status}`);
      }
    } catch (error) {
      updateTest(1, 'error', 'My Team failed to load', error.message);
    }

    // Test 3: Players Page
    setCurrentTest(2);
    try {
      const response = await fetch('/players');
      if (response.ok) {
        updateTest(2, 'success', 'Players page loads successfully');
      } else {
        updateTest(2, 'warning', `Players returned ${response.status}`);
      }
    } catch (error) {
      updateTest(2, 'error', 'Players failed to load', error.message);
    }

    // Test 4: Analytics Page
    setCurrentTest(3);
    try {
      const response = await fetch('/analytics');
      if (response.ok) {
        updateTest(3, 'success', 'Analytics page loads successfully');
      } else {
        updateTest(3, 'warning', `Analytics returned ${response.status}`);
      }
    } catch (error) {
      updateTest(3, 'error', 'Analytics failed to load', error.message);
    }

    // Test 5: Trades Page
    setCurrentTest(4);
    try {
      const response = await fetch('/trades');
      if (response.ok) {
        updateTest(4, 'success', 'Trades page loads successfully');
      } else {
        updateTest(4, 'warning', `Trades returned ${response.status}`);
      }
    } catch (error) {
      updateTest(4, 'error', 'Trades failed to load', error.message);
    }

    // Test 6: Injuries Page
    setCurrentTest(5);
    try {
      const response = await fetch('/injuries');
      if (response.ok) {
        updateTest(5, 'success', 'Injuries page loads successfully');
      } else {
        updateTest(5, 'warning', `Injuries returned ${response.status}`);
      }
    } catch (error) {
      updateTest(5, 'error', 'Injuries failed to load', error.message);
    }

    // Test 7: Settings Page
    setCurrentTest(6);
    try {
      const response = await fetch('/settings');
      if (response.ok) {
        updateTest(6, 'success', 'Settings page loads successfully');
      } else {
        updateTest(6, 'warning', `Settings returned ${response.status}`);
      }
    } catch (error) {
      updateTest(6, 'error', 'Settings failed to load', error.message);
    }

    // Test 8: Supabase Connection
    setCurrentTest(7);
    try {
      const response = await fetch('/api/test-supabase-direct');
      const data = await response.json();
      if (data.success) {
        updateTest(7, 'success', 'Supabase connection working', `${data.data?.total_players || 0} players found`);
      } else {
        updateTest(7, 'warning', 'Supabase connection issues', data.error);
      }
    } catch (error) {
      updateTest(7, 'error', 'Supabase connection failed', error.message);
    }

    // Test 9: Player Data Loading
    setCurrentTest(8);
    try {
      const response = await fetch('/api/test-player-count');
      const data = await response.json();
      if (data.success && data.integration_test?.total_players > 0) {
        updateTest(8, 'success', `Player data loading works`, `${data.integration_test.total_players} players loaded`);
      } else {
        updateTest(8, 'warning', 'Player data loading issues', 'No players returned');
      }
    } catch (error) {
      updateTest(8, 'error', 'Player data loading failed', error.message);
    }

    // Test 10: Search Functionality
    setCurrentTest(9);
    try {
      const response = await fetch('/api/test-player-count');
      const data = await response.json();
      if (data.success && data.search_test?.results_count > 0) {
        updateTest(9, 'success', 'Search functionality works', `Found ${data.search_test.results_count} results for "Nathan"`);
      } else {
        updateTest(9, 'warning', 'Search functionality issues', 'No search results returned');
      }
    } catch (error) {
      updateTest(9, 'error', 'Search functionality failed', error.message);
    }

    setAllTestsComplete(true);
    setCurrentTest(-1);
    console.log('🎉 All functionality tests completed');
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="w-5 h-5 text-green-400" />;
      case 'warning':
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-400" />;
      case 'error':
        return <XCircleIcon className="w-5 h-5 text-red-400" />;
      default:
        return <ClockIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-500/30 bg-green-500/10';
      case 'warning':
        return 'border-yellow-500/30 bg-yellow-500/10';
      case 'error':
        return 'border-red-500/30 bg-red-500/10';
      default:
        return 'border-gray-500/30 bg-gray-500/10';
    }
  };

  const successCount = tests.filter(t => t.status === 'success').length;
  const warningCount = tests.filter(t => t.status === 'warning').length;
  const errorCount = tests.filter(t => t.status === 'error').length;

  return (
    <Layout>
      <Head>
        <title>Functionality Test - FantasyPro</title>
        <meta name="description" content="Comprehensive functionality testing for FantasyPro" />
      </Head>

      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <motion.h1 
            className="text-4xl font-bold themed-text-primary mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            🧪 Functionality Test Suite
          </motion.h1>
          <p className="text-lg themed-text-tertiary">
            Comprehensive testing of all FantasyPro features and functionality
          </p>
        </div>

        {/* Test Progress */}
        {!allTestsComplete && (
          <div className="card p-6">
            <div className="flex items-center space-x-4">
              <LoadingSpinner size="sm" text="" />
              <div>
                <h3 className="font-semibold themed-text-primary">
                  Running Tests... ({currentTest + 1}/{tests.length})
                </h3>
                <p className="text-sm themed-text-tertiary">
                  {currentTest >= 0 ? `Testing: ${tests[currentTest]?.name}` : 'Initializing...'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Test Results Summary */}
        {allTestsComplete && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="card p-4 text-center">
              <div className="text-2xl font-bold text-green-400">{successCount}</div>
              <div className="text-sm themed-text-tertiary">Passed</div>
            </div>
            <div className="card p-4 text-center">
              <div className="text-2xl font-bold text-yellow-400">{warningCount}</div>
              <div className="text-sm themed-text-tertiary">Warnings</div>
            </div>
            <div className="card p-4 text-center">
              <div className="text-2xl font-bold text-red-400">{errorCount}</div>
              <div className="text-sm themed-text-tertiary">Errors</div>
            </div>
            <div className="card p-4 text-center">
              <div className="text-2xl font-bold themed-text-primary">{tests.length}</div>
              <div className="text-sm themed-text-tertiary">Total Tests</div>
            </div>
          </div>
        )}

        {/* Test Results */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold themed-text-primary mb-6">Test Results</h2>
          
          <div className="space-y-4">
            {tests.map((test, index) => (
              <motion.div
                key={test.name}
                className={`p-4 rounded-lg border ${getStatusColor(test.status)}`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(test.status)}
                    <div>
                      <h3 className="font-medium themed-text-primary">{test.name}</h3>
                      <p className="text-sm themed-text-tertiary">{test.message}</p>
                      {test.details && (
                        <p className="text-xs themed-text-tertiary mt-1">{test.details}</p>
                      )}
                    </div>
                  </div>
                  {currentTest === index && !allTestsComplete && (
                    <LoadingSpinner size="sm" text="" />
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Navigation Links */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold themed-text-primary mb-4">Quick Navigation</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {[
              { name: 'Dashboard', href: '/dashboard' },
              { name: 'My Team', href: '/my-team' },
              { name: 'Players', href: '/players' },
              { name: 'Analytics', href: '/analytics' },
              { name: 'Trades', href: '/trades' },
              { name: 'Injuries', href: '/injuries' },
              { name: 'Settings', href: '/settings' },
              { name: 'Home', href: '/' }
            ].map((link) => (
              <Link
                key={link.name}
                href={link.href}
                className="p-3 text-center themed-bg-secondary themed-border rounded-lg hover:themed-bg-tertiary transition-colors"
              >
                <span className="text-sm font-medium themed-text-primary">{link.name}</span>
              </Link>
            ))}
          </div>
        </div>

        {/* Refresh Button */}
        <div className="text-center">
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
          >
            Run Tests Again
          </button>
        </div>
      </div>
    </Layout>
  );
};

export default TestAllFunctionality;
