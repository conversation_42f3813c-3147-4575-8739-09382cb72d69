import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import { motion } from 'framer-motion';
import Layout from '../components/Layout';
import {
  ExclamationTriangleIcon,
  HeartIcon,
  ClockIcon,
  CheckCircleIcon,
  XMarkIcon,
  FireIcon,
  ShieldCheckIcon,
  UserGroupIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface Injury {
  id: string;
  player: {
    name: string;
    team: string;
    position: string;
    price: number;
    ownership: number;
  };
  injury_type: string;
  severity: 'minor' | 'moderate' | 'major' | 'season_ending';
  status: 'injured' | 'test' | 'cleared' | 'doubtful';
  expected_return: string;
  weeks_out: number;
  description: string;
  impact_rating: number;
  last_updated: string;
  source: string;
}

const Injuries: NextPage = () => {
  const [injuries, setInjuries] = useState<Injury[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('impact');

  useEffect(() => {
    const fetchInjuries = async () => {
      try {
        // Simulate API call - replace with real SportRadar data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockInjuries: Injury[] = [
          {
            id: '1',
            player: {
              name: 'Ryan Papenhuyzen',
              team: 'Melbourne Storm',
              position: 'FLB',
              price: 760900,
              ownership: 23.4
            },
            injury_type: 'Hamstring Strain',
            severity: 'moderate',
            status: 'injured',
            expected_return: '2024-07-15',
            weeks_out: 3,
            description: 'Grade 2 hamstring strain sustained during training. Expected to miss 2-3 weeks.',
            impact_rating: 8.5,
            last_updated: '2024-06-22T10:30:00Z',
            source: 'Official Club Statement'
          },
          {
            id: '2',
            player: {
              name: 'Kalyn Ponga',
              team: 'Newcastle Knights',
              position: 'FLB',
              price: 798600,
              ownership: 31.7
            },
            injury_type: 'Concussion',
            severity: 'major',
            status: 'test',
            expected_return: '2024-06-30',
            weeks_out: 1,
            description: 'Failed HIA during last match. Must pass concussion protocols before return.',
            impact_rating: 9.2,
            last_updated: '2024-06-21T16:45:00Z',
            source: 'NRL Medical Report'
          },
          {
            id: '3',
            player: {
              name: 'Tino Fa\'asuamaleaui',
              team: 'Gold Coast Titans',
              position: '2RF',
              price: 699900,
              ownership: 18.9
            },
            injury_type: 'Shoulder Soreness',
            severity: 'minor',
            status: 'doubtful',
            expected_return: '2024-06-25',
            weeks_out: 0,
            description: 'Minor shoulder complaint. Listed as doubtful for next match.',
            impact_rating: 4.2,
            last_updated: '2024-06-22T08:15:00Z',
            source: 'Team List Tuesday'
          },
          {
            id: '4',
            player: {
              name: 'Mitchell Moses',
              team: 'Parramatta Eels',
              position: 'HFB',
              price: 689400,
              ownership: 28.1
            },
            injury_type: 'Bicep Tear',
            severity: 'season_ending',
            status: 'injured',
            expected_return: '2025-03-01',
            weeks_out: 20,
            description: 'Complete bicep tear requiring surgery. Season over.',
            impact_rating: 9.8,
            last_updated: '2024-06-20T14:20:00Z',
            source: 'Medical Scan Results'
          },
          {
            id: '5',
            player: {
              name: 'Jahrome Hughes',
              team: 'Melbourne Storm',
              position: 'HFB',
              price: 756300,
              ownership: 42.6
            },
            injury_type: 'Ankle Sprain',
            severity: 'minor',
            status: 'cleared',
            expected_return: '2024-06-23',
            weeks_out: 0,
            description: 'Minor ankle sprain. Cleared to play after successful fitness test.',
            impact_rating: 2.1,
            last_updated: '2024-06-22T12:00:00Z',
            source: 'Captain\'s Run Report'
          }
        ];

        setInjuries(mockInjuries);
      } catch (error) {
        console.error('Error fetching injuries:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInjuries();
  }, []);

  const filteredInjuries = injuries
    .filter(injury => {
      if (filter === 'all') return true;
      if (filter === 'active') return ['injured', 'test', 'doubtful'].includes(injury.status);
      if (filter === 'cleared') return injury.status === 'cleared';
      return injury.severity === filter;
    })
    .filter(injury => 
      injury.player.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      injury.player.team.toLowerCase().includes(searchTerm.toLowerCase()) ||
      injury.injury_type.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'impact':
          return b.impact_rating - a.impact_rating;
        case 'ownership':
          return b.player.ownership - a.player.ownership;
        case 'return':
          return new Date(a.expected_return).getTime() - new Date(b.expected_return).getTime();
        case 'updated':
          return new Date(b.last_updated).getTime() - new Date(a.last_updated).getTime();
        default:
          return 0;
      }
    });

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'minor':
        return 'text-yellow-400 bg-yellow-900/20';
      case 'moderate':
        return 'text-orange-400 bg-orange-900/20';
      case 'major':
        return 'text-red-400 bg-red-900/20';
      case 'season_ending':
        return 'text-red-600 bg-red-900/40';
      default:
        return 'text-slate-400 bg-slate-900/20';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'injured':
        return 'text-red-400 bg-red-900/20';
      case 'test':
        return 'text-yellow-400 bg-yellow-900/20';
      case 'doubtful':
        return 'text-orange-400 bg-orange-900/20';
      case 'cleared':
        return 'text-green-400 bg-green-900/20';
      default:
        return 'text-slate-400 bg-slate-900/20';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'injured':
        return <ExclamationTriangleIcon className="w-4 h-4" />;
      case 'test':
        return <ClockIcon className="w-4 h-4" />;
      case 'doubtful':
        return <ExclamationTriangleIcon className="w-4 h-4" />;
      case 'cleared':
        return <CheckCircleIcon className="w-4 h-4" />;
      default:
        return <ExclamationTriangleIcon className="w-4 h-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return `$${(amount / 1000000).toFixed(2)}M`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getImpactColor = (rating: number) => {
    if (rating >= 8) return 'text-red-400';
    if (rating >= 6) return 'text-orange-400';
    if (rating >= 4) return 'text-yellow-400';
    return 'text-green-400';
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-8 h-8"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Injuries - FantasyPro</title>
        <meta name="description" content="Real-time NRL injury reports and impact analysis for SuperCoach" />
      </Head>

      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold themed-text-primary">Injury Oracle</h1>
            <p className="themed-text-tertiary mt-1">Real-time injury reports and fantasy impact analysis</p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              type="button" onClick={() => window.location.reload()}
              className="btn-secondary btn-ripple"
            >
              <ArrowPathIcon className="w-4 h-4 mr-2" />
              Refresh
            </button>
            <span className="status-live">{filteredInjuries.length} Reports</span>
          </div>
        </div>

        {/* Filters and Search */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="card-premium p-6"
        >
          <div className="flex items-center space-x-3 mb-6">
            <FunnelIcon className="w-6 h-6 text-blue-400" />
            <h2 className="text-xl font-semibold themed-text-primary">Filter & Search</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">Search</label>
              <input
                type="text"
                className="input-primary w-full"
                placeholder="Search players, teams, injuries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">Status</label>
              <select
                className="input-primary w-full"
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
              >
                <option value="all">All Injuries</option>
                <option value="active">Active Concerns</option>
                <option value="cleared">Cleared to Play</option>
                <option value="minor">Minor Injuries</option>
                <option value="moderate">Moderate Injuries</option>
                <option value="major">Major Injuries</option>
                <option value="season_ending">Season Ending</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium themed-text-secondary mb-2">Sort By</label>
              <select
                className="input-primary w-full"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="impact">Impact Rating</option>
                <option value="ownership">Ownership %</option>
                <option value="return">Expected Return</option>
                <option value="updated">Last Updated</option>
              </select>
            </div>

            <div className="flex items-end">
              <button
                type="button" onClick={() => {
                  setFilter('all');
                  setSearchTerm('');
                  setSortBy('impact');
                }}
                className="btn-outline w-full"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </motion.div>

        {/* Injury Reports */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="card"
        >
          <div className="p-6 border-b themed-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <HeartIcon className="w-5 h-5 text-red-400" />
                <h2 className="text-lg font-semibold themed-text-primary">Injury Reports</h2>
                <span className="text-xs text-red-400">
                  {filteredInjuries.length} active reports
                </span>
              </div>
              <div className="text-sm themed-text-tertiary">
                Updated from SportRadar API
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              {filteredInjuries.map((injury, index) => (
                <motion.div
                  key={injury.id}
                  className="card-hover p-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-semibold themed-text-primary">{injury.player.name}</h3>
                        <span className="text-sm themed-text-tertiary">{injury.player.team}</span>
                        <span className="px-2 py-1 rounded text-xs font-medium bg-blue-600/20 text-blue-400">
                          {injury.player.position}
                        </span>
                      </div>
                      <div className="text-sm themed-text-tertiary mb-2">
                        <strong>Injury:</strong> {injury.injury_type}
                      </div>
                      <div className="text-sm themed-text-tertiary">
                        {injury.description}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-bold ${getImpactColor(injury.impact_rating)}`}>
                        {injury.impact_rating.toFixed(1)}
                      </div>
                      <div className="text-xs themed-text-tertiary">Impact</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-sm themed-text-secondary">Status</div>
                      <div className={`flex items-center justify-center space-x-1 px-2 py-1 rounded text-xs font-medium ${getStatusColor(injury.status)}`}>
                        {getStatusIcon(injury.status)}
                        <span>{injury.status.toUpperCase()}</span>
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm themed-text-secondary">Severity</div>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(injury.severity)}`}>
                        {injury.severity.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                    <div className="text-center">
                      <div className="text-sm themed-text-secondary">Weeks Out</div>
                      <div className="text-sm font-medium themed-text-primary">
                        {injury.weeks_out === 0 ? 'TBC' : `${injury.weeks_out}w`}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm themed-text-secondary">Expected Return</div>
                      <div className="text-sm font-medium themed-text-primary">
                        {formatDate(injury.expected_return)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm themed-text-secondary">Ownership</div>
                      <div className="text-sm font-medium themed-text-primary">
                        {injury.player.ownership.toFixed(1)}%
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t themed-border">
                    <div className="flex items-center space-x-4">
                      <div className="text-xs themed-text-tertiary">
                        <strong>Price:</strong> {formatCurrency(injury.player.price)}
                      </div>
                      <div className="text-xs themed-text-tertiary">
                        <strong>Source:</strong> {injury.source}
                      </div>
                    </div>
                    <div className="text-xs themed-text-tertiary">
                      Updated {formatDate(injury.last_updated)}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {filteredInjuries.length === 0 && (
              <div className="text-center py-12 themed-text-tertiary">
                <HeartIcon className="w-12 h-12 mx-auto mb-4 text-slate-600" />
                <div className="text-lg font-medium mb-2">No injury reports found</div>
                <div className="text-sm mb-4">Try adjusting your filters or search terms</div>
                <button
                  type="button" onClick={() => {
                    setFilter('all');
                    setSearchTerm('');
                  }}
                  className="btn-primary"
                >
                  Show All Reports
                </button>
              </div>
            )}
          </div>
        </motion.div>

        {/* Injury Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <motion.div
            className="card-premium p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <ExclamationTriangleIcon className="w-5 h-5 text-red-400" />
                <span className="text-sm themed-text-secondary">Active Injuries</span>
              </div>
            </div>
            <div className="text-2xl font-bold themed-text-primary mb-1">
              {injuries.filter(i => ['injured', 'test', 'doubtful'].includes(i.status)).length}
            </div>
            <div className="text-sm themed-text-tertiary">
              Players with concerns
            </div>
          </motion.div>

          <motion.div
            className="card-premium p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <FireIcon className="w-5 h-5 text-orange-400" />
                <span className="text-sm themed-text-secondary">High Impact</span>
              </div>
            </div>
            <div className="text-2xl font-bold themed-text-primary mb-1">
              {injuries.filter(i => i.impact_rating >= 8).length}
            </div>
            <div className="text-sm themed-text-tertiary">
              Major fantasy impact
            </div>
          </motion.div>

          <motion.div
            className="card-premium p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <UserGroupIcon className="w-5 h-5 text-blue-400" />
                <span className="text-sm themed-text-secondary">High Ownership</span>
              </div>
            </div>
            <div className="text-2xl font-bold themed-text-primary mb-1">
              {injuries.filter(i => i.player.ownership >= 25 && ['injured', 'test', 'doubtful'].includes(i.status)).length}
            </div>
            <div className="text-sm themed-text-tertiary">
              Popular players affected
            </div>
          </motion.div>

          <motion.div
            className="card-premium p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <CheckCircleIcon className="w-5 h-5 text-green-400" />
                <span className="text-sm themed-text-secondary">Cleared</span>
              </div>
            </div>
            <div className="text-2xl font-bold themed-text-primary mb-1">
              {injuries.filter(i => i.status === 'cleared').length}
            </div>
            <div className="text-sm themed-text-tertiary">
              Ready to play
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default Injuries;
