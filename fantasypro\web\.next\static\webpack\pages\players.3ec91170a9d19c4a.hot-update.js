"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/players",{

/***/ "./src/services/supabase.ts":
/*!**********************************!*\
  !*** ./src/services/supabase.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: function() { return /* binding */ AnalyticsService; },\n/* harmony export */   CacheService: function() { return /* binding */ CacheService; },\n/* harmony export */   InjuryService: function() { return /* binding */ InjuryService; },\n/* harmony export */   PlayerService: function() { return /* binding */ PlayerService; },\n/* harmony export */   SquadService: function() { return /* binding */ SquadService; },\n/* harmony export */   TradeService: function() { return /* binding */ TradeService; },\n/* harmony export */   supabase: function() { return /* binding */ supabase; },\n/* harmony export */   supabaseAdmin: function() { return /* binding */ supabaseAdmin; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"./node_modules/next/node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @swc/helpers/_/_class_call_check */ \"./node_modules/next/node_modules/@swc/helpers/esm/_class_call_check.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"./node_modules/next/node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"./node_modules/next/node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"./node_modules/next/node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"./node_modules/next/node_modules/@swc/helpers/esm/_ts_generator.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/process/browser.js\");\n\n\n\n\n\n\n\n// Supabase configuration\nvar supabaseUrl = \"https://fuxpdgsixnbbsdspusmp.supabase.co\" || 0;\nvar supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ3NzE5NzQsImV4cCI6MjA1MDM0Nzk3NH0.Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5\" || 0;\nvar supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDUwODA1NiwiZXhwIjoyMDY2MDg0MDU2fQ.f5qZOHQlAKz_sbIwoGmY4Gub8bGHyx_jxlRYZk-1VPg\";\n// Create Supabase clients\nvar supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\nvar supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n// Player service functions\nvar PlayerService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function PlayerService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, PlayerService);\n    }\n    // Get all players with search and filtering\n    PlayerService.getPlayers = function getPlayers() {\n        var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var query, _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        query = supabase.from(\"players\").select(\"*\");\n                        if (options.search) {\n                            query = query.or(\"name.ilike.%\".concat(options.search, \"%,team.ilike.%\").concat(options.search, \"%,position.ilike.%\").concat(options.search, \"%\"));\n                        }\n                        if (options.position) {\n                            query = query.eq(\"position\", options.position);\n                        }\n                        if (options.team) {\n                            query = query.eq(\"team\", options.team);\n                        }\n                        if (options.limit) {\n                            query = query.limit(options.limit);\n                        }\n                        if (options.offset) {\n                            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n                        }\n                        return [\n                            4,\n                            query.order(\"points\", {\n                                ascending: false\n                            })\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching players:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Get ALL players (for Players page - should return 581 players)\n    PlayerService.getAllPlayers = function getAllPlayers() {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error, transformedPlayers;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        console.log(\"\\uD83D\\uDD04 PlayerService.getAllPlayers() - fetching complete 581 player dataset from nrl_players table\");\n                        return [\n                            4,\n                            supabase.from(\"nrl_players\").select(\"*\").order(\"name\")\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"❌ Error fetching all players from nrl_players table:\", error);\n                            throw error;\n                        }\n                        console.log(\"✅ Supabase nrl_players table returned \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" players\"));\n                        // Transform nrl_players data to match our Player interface\n                        transformedPlayers = (data === null || data === void 0 ? void 0 : data.map(function(player) {\n                            var _player_statistics, _player_statistics1, _player_statistics2, _player_statistics3, _player_statistics4;\n                            return {\n                                id: player.id.toString(),\n                                name: player.name,\n                                position: player.position || \"Unknown\",\n                                team: player.team_name || \"Unknown Team\",\n                                price: ((_player_statistics = player.statistics) === null || _player_statistics === void 0 ? void 0 : _player_statistics.price) || 300000,\n                                points: ((_player_statistics1 = player.statistics) === null || _player_statistics1 === void 0 ? void 0 : _player_statistics1.total_points) || 0,\n                                form: ((_player_statistics2 = player.statistics) === null || _player_statistics2 === void 0 ? void 0 : _player_statistics2.form) || 0,\n                                ownership: ((_player_statistics3 = player.statistics) === null || _player_statistics3 === void 0 ? void 0 : _player_statistics3.ownership) || 0,\n                                breakeven: ((_player_statistics4 = player.statistics) === null || _player_statistics4 === void 0 ? void 0 : _player_statistics4.breakeven) || 0,\n                                image_url: player.image_url,\n                                created_at: player.created_at,\n                                updated_at: player.updated_at\n                            };\n                        })) || [];\n                        return [\n                            2,\n                            transformedPlayers\n                        ];\n                }\n            });\n        })();\n    };\n    // Get player by ID\n    PlayerService.getPlayer = function getPlayer(id) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").eq(\"id\", id).single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching player:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Search players with predictive algorithm\n    PlayerService.searchPlayers = function searchPlayers(query) {\n        var limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!query || query.length < 2) return [\n                            2,\n                            []\n                        ];\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").or(\"name.ilike.%\".concat(query, \"%,team.ilike.%\").concat(query, \"%,position.ilike.%\").concat(query, \"%\")).order(\"points\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error searching players:\", error);\n                            return [\n                                2,\n                                []\n                            ];\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Get top performers\n    PlayerService.getTopPerformers = function getTopPerformers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").order(\"points\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching top performers:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Get price risers\n    PlayerService.getPriceRisers = function getPriceRisers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").order(\"price\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching price risers:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Update player data\n    PlayerService.updatePlayer = function updatePlayer(id, updates) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").update((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, updates), {\n                                updated_at: new Date().toISOString()\n                            })).eq(\"id\", id).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error updating player:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return PlayerService;\n}();\n// Trade recommendation service\nvar TradeService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function TradeService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, TradeService);\n    }\n    // Get trade recommendations for user\n    TradeService.getTradeRecommendations = function getTradeRecommendations(userId) {\n        var limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"trade_recommendations\").select(\"\\n        *,\\n        player_out:players!player_out_id(*),\\n        player_in:players!player_in_id(*)\\n      \").eq(\"user_id\", userId).order(\"confidence\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching trade recommendations:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Create trade recommendation\n    TradeService.createTradeRecommendation = function createTradeRecommendation(recommendation) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"trade_recommendations\").insert((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, recommendation), {\n                                created_at: new Date().toISOString()\n                            })).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error creating trade recommendation:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Execute trade (log the trade)\n    TradeService.executeTrade = function executeTrade(tradeId, userId) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"executed_trades\").insert({\n                                trade_recommendation_id: tradeId,\n                                user_id: userId,\n                                executed_at: new Date().toISOString()\n                            }).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error executing trade:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return TradeService;\n}();\n// Injury service\nvar InjuryService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function InjuryService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, InjuryService);\n    }\n    // Get current injury reports\n    InjuryService.getInjuryReports = function getInjuryReports() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"injury_reports\").select(\"\\n        *,\\n        player:players(*)\\n      \").order(\"created_at\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching injury reports:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Create injury report\n    InjuryService.createInjuryReport = function createInjuryReport(report) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"injury_reports\").insert((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, report), {\n                                created_at: new Date().toISOString(),\n                                updated_at: new Date().toISOString()\n                            })).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error creating injury report:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return InjuryService;\n}();\n// User squad service\nvar SquadService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function SquadService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, SquadService);\n    }\n    // Get user's squad\n    SquadService.getUserSquad = function getUserSquad(userId) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"user_squads\").select(\"\\n        *,\\n        player:players(*)\\n      \").eq(\"user_id\", userId).order(\"position_in_squad\")\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching user squad:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Add player to squad\n    SquadService.addPlayerToSquad = function addPlayerToSquad(squadEntry) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"user_squads\").insert((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_4__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_5__._)({}, squadEntry), {\n                                created_at: new Date().toISOString()\n                            })).select().single()\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error adding player to squad:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    // Set captain\n    SquadService.setCaptain = function setCaptain(userId, playerId) {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        // First, remove captain status from all players\n                        return [\n                            4,\n                            supabase.from(\"user_squads\").update({\n                                is_captain: false,\n                                is_vice_captain: false\n                            }).eq(\"user_id\", userId)\n                        ];\n                    case 1:\n                        _state.sent();\n                        return [\n                            4,\n                            supabase.from(\"user_squads\").update({\n                                is_captain: true\n                            }).eq(\"user_id\", userId).eq(\"player_id\", playerId).select().single()\n                        ];\n                    case 2:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error setting captain:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return SquadService;\n}();\n// Analytics service\nvar AnalyticsService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function AnalyticsService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, AnalyticsService);\n    }\n    // Get dashboard stats\n    AnalyticsService.getDashboardStats = function getDashboardStats() {\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, playersCount, teamsCount, injuriesCount;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            Promise.all([\n                                supabase.from(\"players\").select(\"id\", {\n                                    count: \"exact\",\n                                    head: true\n                                }),\n                                supabase.from(\"players\").select(\"team\", {\n                                    count: \"exact\",\n                                    head: true\n                                }).distinct(),\n                                supabase.from(\"injury_reports\").select(\"id\", {\n                                    count: \"exact\",\n                                    head: true\n                                })\n                            ])\n                        ];\n                    case 1:\n                        _ref = _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._.apply(void 0, [\n                            _state.sent(),\n                            3\n                        ]), playersCount = _ref[0], teamsCount = _ref[1], injuriesCount = _ref[2];\n                        return [\n                            2,\n                            {\n                                total_players: playersCount.count || 0,\n                                total_teams: 17,\n                                active_injuries: injuriesCount.count || 0,\n                                last_updated: new Date().toISOString()\n                            }\n                        ];\n                }\n            });\n        })();\n    };\n    // Get trending players\n    AnalyticsService.getTrendingPlayers = function getTrendingPlayers() {\n        var limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_2__._)(function() {\n            var _ref, data, error;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_3__._)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            supabase.from(\"players\").select(\"*\").order(\"form\", {\n                                ascending: false\n                            }).limit(limit)\n                        ];\n                    case 1:\n                        _ref = _state.sent(), data = _ref.data, error = _ref.error;\n                        if (error) {\n                            console.error(\"Error fetching trending players:\", error);\n                            throw error;\n                        }\n                        return [\n                            2,\n                            data\n                        ];\n                }\n            });\n        })();\n    };\n    return AnalyticsService;\n}();\n// Cache service for offline support\nvar CacheService = /*#__PURE__*/ function() {\n    \"use strict\";\n    function CacheService() {\n        (0,_swc_helpers_class_call_check__WEBPACK_IMPORTED_MODULE_1__._)(this, CacheService);\n    }\n    CacheService.set = function set(key, data) {\n        var cacheData = {\n            data: data,\n            timestamp: Date.now()\n        };\n        localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheData));\n    };\n    CacheService.get = function get(key) {\n        var cached = localStorage.getItem(this.CACHE_PREFIX + key);\n        if (!cached) return null;\n        var _JSON_parse = JSON.parse(cached), data = _JSON_parse.data, timestamp = _JSON_parse.timestamp;\n        if (Date.now() - timestamp > this.CACHE_DURATION) {\n            localStorage.removeItem(this.CACHE_PREFIX + key);\n            return null;\n        }\n        return data;\n    };\n    CacheService.clear = function clear() {\n        var _this = this;\n        Object.keys(localStorage).forEach(function(key) {\n            if (key.startsWith(_this.CACHE_PREFIX)) {\n                localStorage.removeItem(key);\n            }\n        });\n    };\n    return CacheService;\n}();\nCacheService.CACHE_PREFIX = \"fantasypro_cache_\";\nCacheService.CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n;\n/* harmony default export */ __webpack_exports__[\"default\"] = (supabase);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc2VydmljZXMvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFxRDtBQUVyRCx5QkFBeUI7QUFDekIsSUFBTUMsY0FBY0MsMENBQW9DLElBQUk7QUFDNUQsSUFBTUcsa0JBQWtCSCxpTkFBeUMsSUFBSTtBQUNyRSxJQUFNSyxxQkFBcUJMLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ0sseUJBQXlCLElBQUk7QUFFcEUsMEJBQTBCO0FBQ25CLElBQU1DLFdBQVdULG1FQUFZQSxDQUFDQyxhQUFhSSxpQkFBaUI7QUFDNUQsSUFBTUssZ0JBQWdCVixtRUFBWUEsQ0FBQ0MsYUFBYU0sb0JBQW9CO0FBb0QzRSwyQkFBMkI7QUFDcEIsa0NBQUs7O2FBQUNJOytFQUFBQTs7SUFDWCw0Q0FBNEM7SUFEakNBLGNBRUVDLFVBdUNaLEdBdkNELFNBQWFBO1lBQVdDLFVBQUFBLGlFQU1wQixDQUFDO2VBTkw7Z0JBT01DLE9Bd0JvQixNQUFoQkMsTUFBTUM7Ozs7d0JBeEJWRixRQUFRTCxTQUNUUSxJQUFJLENBQUMsV0FDTEMsTUFBTSxDQUFDO3dCQUVWLElBQUlMLFFBQVFNLE1BQU0sRUFBRTs0QkFDbEJMLFFBQVFBLE1BQU1NLEVBQUUsQ0FBQyxlQUE4Q1AsT0FBL0JBLFFBQVFNLE1BQU0sRUFBQyxrQkFBbUROLE9BQW5DQSxRQUFRTSxNQUFNLEVBQUMsc0JBQW1DLE9BQWZOLFFBQVFNLE1BQU0sRUFBQzt3QkFDbkg7d0JBRUEsSUFBSU4sUUFBUVEsUUFBUSxFQUFFOzRCQUNwQlAsUUFBUUEsTUFBTVEsRUFBRSxDQUFDLFlBQVlULFFBQVFRLFFBQVE7d0JBQy9DO3dCQUVBLElBQUlSLFFBQVFVLElBQUksRUFBRTs0QkFDaEJULFFBQVFBLE1BQU1RLEVBQUUsQ0FBQyxRQUFRVCxRQUFRVSxJQUFJO3dCQUN2Qzt3QkFFQSxJQUFJVixRQUFRVyxLQUFLLEVBQUU7NEJBQ2pCVixRQUFRQSxNQUFNVSxLQUFLLENBQUNYLFFBQVFXLEtBQUs7d0JBQ25DO3dCQUVBLElBQUlYLFFBQVFZLE1BQU0sRUFBRTs0QkFDbEJYLFFBQVFBLE1BQU1ZLEtBQUssQ0FBQ2IsUUFBUVksTUFBTSxFQUFFWixRQUFRWSxNQUFNLEdBQUlaLENBQUFBLFFBQVFXLEtBQUssSUFBSSxFQUFDLElBQUs7d0JBQy9FO3dCQUV3Qjs7NEJBQU1WLE1BQU1hLEtBQUssQ0FBQyxVQUFVO2dDQUFFQyxXQUFXOzRCQUFNOzs7d0JBQS9DLHNCQUFoQmIsT0FBZ0IsS0FBaEJBLE1BQU1DLFFBQVUsS0FBVkE7d0JBRWQsSUFBSUEsT0FBTzs0QkFDVGEsUUFBUWIsS0FBSyxDQUFDLDJCQUEyQkE7NEJBQ3pDLE1BQU1BO3dCQUNSO3dCQUVBOzs0QkFBT0Q7Ozs7UUFDVDs7SUFFQSxpRUFBaUU7SUEzQ3RESixjQTRDRW1CLGFBZ0NaLEdBaENELFNBQWFBO2VBQWI7Z0JBRzBCLE1BQWhCZixNQUFNQyxPQWFSZTs7Ozt3QkFmTkYsUUFBUUcsR0FBRyxDQUFDO3dCQUVZOzs0QkFBTXZCLFNBQzNCUSxJQUFJLENBQUMsZUFDTEMsTUFBTSxDQUFDLEtBQ1BTLEtBQUssQ0FBQzs7O3dCQUhlLHNCQUFoQlosT0FBZ0IsS0FBaEJBLE1BQU1DLFFBQVUsS0FBVkE7d0JBS2QsSUFBSUEsT0FBTzs0QkFDVGEsUUFBUWIsS0FBSyxDQUFDLHdEQUF3REE7NEJBQ3RFLE1BQU1BO3dCQUNSO3dCQUVBYSxRQUFRRyxHQUFHLENBQUMseUNBQTJELE9BQWxCakIsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNa0IsTUFBTSxLQUFJLEdBQUU7d0JBRXZFLDJEQUEyRDt3QkFDckRGLHFCQUFxQmhCLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTW1CLEdBQUcsQ0FBQ0MsU0FBQUE7Z0NBSzVCQSxvQkFDQ0EscUJBQ0ZBLHFCQUNLQSxxQkFDQUE7bUNBVG1DO2dDQUM5Q0MsSUFBSUQsT0FBT0MsRUFBRSxDQUFDQyxRQUFRO2dDQUN0QkMsTUFBTUgsT0FBT0csSUFBSTtnQ0FDakJqQixVQUFVYyxPQUFPZCxRQUFRLElBQUk7Z0NBQzdCRSxNQUFNWSxPQUFPSSxTQUFTLElBQUk7Z0NBQzFCQyxPQUFPTCxFQUFBQSxxQkFBQUEsT0FBT00sVUFBVSxjQUFqQk4seUNBQUFBLG1CQUFtQkssS0FBSyxLQUFJO2dDQUNuQ0UsUUFBUVAsRUFBQUEsc0JBQUFBLE9BQU9NLFVBQVUsY0FBakJOLDBDQUFBQSxvQkFBbUJRLFlBQVksS0FBSTtnQ0FDM0NDLE1BQU1ULEVBQUFBLHNCQUFBQSxPQUFPTSxVQUFVLGNBQWpCTiwwQ0FBQUEsb0JBQW1CUyxJQUFJLEtBQUk7Z0NBQ2pDQyxXQUFXVixFQUFBQSxzQkFBQUEsT0FBT00sVUFBVSxjQUFqQk4sMENBQUFBLG9CQUFtQlUsU0FBUyxLQUFJO2dDQUMzQ0MsV0FBV1gsRUFBQUEsc0JBQUFBLE9BQU9NLFVBQVUsY0FBakJOLDBDQUFBQSxvQkFBbUJXLFNBQVMsS0FBSTtnQ0FDM0NDLFdBQVdaLE9BQU9ZLFNBQVM7Z0NBQzNCQyxZQUFZYixPQUFPYSxVQUFVO2dDQUM3QkMsWUFBWWQsT0FBT2MsVUFBVTs0QkFDL0I7O3dCQUVBOzs0QkFBT2xCOzs7O1FBQ1Q7O0lBRUEsbUJBQW1CO0lBOUVScEIsY0ErRUV1QyxTQWFaLEdBYkQsU0FBYUEsVUFBVWQsRUFBVTtlQUFqQztnQkFDMEIsTUFBaEJyQixNQUFNQzs7Ozt3QkFBVTs7NEJBQU1QLFNBQzNCUSxJQUFJLENBQUMsV0FDTEMsTUFBTSxDQUFDLEtBQ1BJLEVBQUUsQ0FBQyxNQUFNYyxJQUNUZSxNQUFNOzs7d0JBSmUsc0JBQWhCcEMsT0FBZ0IsS0FBaEJBLE1BQU1DLFFBQVUsS0FBVkE7d0JBTWQsSUFBSUEsT0FBTzs0QkFDVGEsUUFBUWIsS0FBSyxDQUFDLDBCQUEwQkE7NEJBQ3hDLE1BQU1BO3dCQUNSO3dCQUVBOzs0QkFBT0Q7Ozs7UUFDVDs7SUFFQSwyQ0FBMkM7SUE5RmhDSixjQStGRXlDLGFBZ0JaLEdBaEJELFNBQWFBLGNBQWN0QyxLQUFhO1lBQUVVLFFBQUFBLGlFQUFnQjtlQUExRDtnQkFHMEIsTUFBaEJULE1BQU1DOzs7O3dCQUZkLElBQUksQ0FBQ0YsU0FBU0EsTUFBTW1CLE1BQU0sR0FBRyxHQUFHOzs7O3dCQUVSOzs0QkFBTXhCLFNBQzNCUSxJQUFJLENBQUMsV0FDTEMsTUFBTSxDQUFDLEtBQ1BFLEVBQUUsQ0FBQyxlQUFxQ04sT0FBdEJBLE9BQU0sa0JBQTBDQSxPQUExQkEsT0FBTSxzQkFBMEIsT0FBTkEsT0FBTSxNQUN4RWEsS0FBSyxDQUFDLFVBQVU7Z0NBQUVDLFdBQVc7NEJBQU0sR0FDbkNKLEtBQUssQ0FBQ0E7Ozt3QkFMZSxzQkFBaEJULE9BQWdCLEtBQWhCQSxNQUFNQyxRQUFVLEtBQVZBO3dCQU9kLElBQUlBLE9BQU87NEJBQ1RhLFFBQVFiLEtBQUssQ0FBQyw0QkFBNEJBOzRCQUMxQzs7Ozt3QkFDRjt3QkFFQTs7NEJBQU9EOzs7O1FBQ1Q7O0lBRUEscUJBQXFCO0lBakhWSixjQWtIRTBDLGdCQWFaLEdBYkQsU0FBYUE7WUFBaUI3QixRQUFBQSxpRUFBZ0I7ZUFBOUM7Z0JBQzBCLE1BQWhCVCxNQUFNQzs7Ozt3QkFBVTs7NEJBQU1QLFNBQzNCUSxJQUFJLENBQUMsV0FDTEMsTUFBTSxDQUFDLEtBQ1BTLEtBQUssQ0FBQyxVQUFVO2dDQUFFQyxXQUFXOzRCQUFNLEdBQ25DSixLQUFLLENBQUNBOzs7d0JBSmUsc0JBQWhCVCxPQUFnQixLQUFoQkEsTUFBTUMsUUFBVSxLQUFWQTt3QkFNZCxJQUFJQSxPQUFPOzRCQUNUYSxRQUFRYixLQUFLLENBQUMsa0NBQWtDQTs0QkFDaEQsTUFBTUE7d0JBQ1I7d0JBRUE7OzRCQUFPRDs7OztRQUNUOztJQUVBLG1CQUFtQjtJQWpJUkosY0FrSUUyQyxjQWFaLEdBYkQsU0FBYUE7WUFBZTlCLFFBQUFBLGlFQUFnQjtlQUE1QztnQkFDMEIsTUFBaEJULE1BQU1DOzs7O3dCQUFVOzs0QkFBTVAsU0FDM0JRLElBQUksQ0FBQyxXQUNMQyxNQUFNLENBQUMsS0FDUFMsS0FBSyxDQUFDLFNBQVM7Z0NBQUVDLFdBQVc7NEJBQU0sR0FDbENKLEtBQUssQ0FBQ0E7Ozt3QkFKZSxzQkFBaEJULE9BQWdCLEtBQWhCQSxNQUFNQyxRQUFVLEtBQVZBO3dCQU1kLElBQUlBLE9BQU87NEJBQ1RhLFFBQVFiLEtBQUssQ0FBQyxnQ0FBZ0NBOzRCQUM5QyxNQUFNQTt3QkFDUjt3QkFFQTs7NEJBQU9EOzs7O1FBQ1Q7O0lBRUEscUJBQXFCO0lBakpWSixjQWtKRTRDLFlBY1osR0FkRCxTQUFhQSxhQUFhbkIsRUFBVSxFQUFFb0IsT0FBd0I7ZUFBOUQ7Z0JBQzBCLE1BQWhCekMsTUFBTUM7Ozs7d0JBQVU7OzRCQUFNUCxTQUMzQlEsSUFBSSxDQUFDLFdBQ0x3QyxNQUFNLENBQUMsc0lBQUtEO2dDQUFTUCxZQUFZLElBQUlTLE9BQU9DLFdBQVc7Z0NBQ3ZEckMsRUFBRSxDQUFDLE1BQU1jLElBQ1RsQixNQUFNLEdBQ05pQyxNQUFNOzs7d0JBTGUsc0JBQWhCcEMsT0FBZ0IsS0FBaEJBLE1BQU1DLFFBQVUsS0FBVkE7d0JBT2QsSUFBSUEsT0FBTzs0QkFDVGEsUUFBUWIsS0FBSyxDQUFDLDBCQUEwQkE7NEJBQ3hDLE1BQU1BO3dCQUNSO3dCQUVBOzs0QkFBT0Q7Ozs7UUFDVDs7V0FoS1dKO0lBaUtaO0FBRUQsK0JBQStCO0FBQ3hCLGlDQUFLOzthQUFDaUQ7K0VBQUFBOztJQUNYLHFDQUFxQztJQUQxQkEsYUFFRUMsdUJBa0JaLEdBbEJELFNBQWFBLHdCQUF3QkMsTUFBYztZQUFFdEMsUUFBQUEsaUVBQWdCO2VBQXJFO2dCQUMwQixNQUFoQlQsTUFBTUM7Ozs7d0JBQVU7OzRCQUFNUCxTQUMzQlEsSUFBSSxDQUFDLHlCQUNMQyxNQUFNLENBQUUsaUhBS1JJLEVBQUUsQ0FBQyxXQUFXd0MsUUFDZG5DLEtBQUssQ0FBQyxjQUFjO2dDQUFFQyxXQUFXOzRCQUFNLEdBQ3ZDSixLQUFLLENBQUNBOzs7d0JBVGUsc0JBQWhCVCxPQUFnQixLQUFoQkEsTUFBTUMsUUFBVSxLQUFWQTt3QkFXZCxJQUFJQSxPQUFPOzRCQUNUYSxRQUFRYixLQUFLLENBQUMseUNBQXlDQTs0QkFDdkQsTUFBTUE7d0JBQ1I7d0JBRUE7OzRCQUFPRDs7OztRQUNUOztJQUVBLDhCQUE4QjtJQXRCbkI2QyxhQXVCRUcseUJBZ0JaLEdBaEJELFNBQWFBLDBCQUEwQkMsY0FBOEQ7ZUFBckc7Z0JBQzBCLE1BQWhCakQsTUFBTUM7Ozs7d0JBQVU7OzRCQUFNUCxTQUMzQlEsSUFBSSxDQUFDLHlCQUNMZ0QsTUFBTSxDQUFDLHNJQUNIRDtnQ0FDSGhCLFlBQVksSUFBSVUsT0FBT0MsV0FBVztnQ0FFbkN6QyxNQUFNLEdBQ05pQyxNQUFNOzs7d0JBUGUsc0JBQWhCcEMsT0FBZ0IsS0FBaEJBLE1BQU1DLFFBQVUsS0FBVkE7d0JBU2QsSUFBSUEsT0FBTzs0QkFDVGEsUUFBUWIsS0FBSyxDQUFDLHdDQUF3Q0E7NEJBQ3RELE1BQU1BO3dCQUNSO3dCQUVBOzs0QkFBT0Q7Ozs7UUFDVDs7SUFFQSxnQ0FBZ0M7SUF6Q3JCNkMsYUEwQ0VNLFlBaUJaLEdBakJELFNBQWFBLGFBQWFDLE9BQWUsRUFBRUwsTUFBYztlQUF6RDtnQkFDMEIsTUFBaEIvQyxNQUFNQzs7Ozt3QkFBVTs7NEJBQU1QLFNBQzNCUSxJQUFJLENBQUMsbUJBQ0xnRCxNQUFNLENBQUM7Z0NBQ05HLHlCQUF5QkQ7Z0NBQ3pCRSxTQUFTUDtnQ0FDVFEsYUFBYSxJQUFJWixPQUFPQyxXQUFXOzRCQUNyQyxHQUNDekMsTUFBTSxHQUNOaUMsTUFBTTs7O3dCQVJlLHNCQUFoQnBDLE9BQWdCLEtBQWhCQSxNQUFNQyxRQUFVLEtBQVZBO3dCQVVkLElBQUlBLE9BQU87NEJBQ1RhLFFBQVFiLEtBQUssQ0FBQywwQkFBMEJBOzRCQUN4QyxNQUFNQTt3QkFDUjt3QkFFQTs7NEJBQU9EOzs7O1FBQ1Q7O1dBM0RXNkM7SUE0RFo7QUFFRCxpQkFBaUI7QUFDVixrQ0FBSzs7YUFBQ1c7K0VBQUFBOztJQUNYLDZCQUE2QjtJQURsQkEsY0FFRUMsZ0JBZ0JaLEdBaEJELFNBQWFBO1lBQWlCaEQsUUFBQUEsaUVBQWdCO2VBQTlDO2dCQUMwQixNQUFoQlQsTUFBTUM7Ozs7d0JBQVU7OzRCQUFNUCxTQUMzQlEsSUFBSSxDQUFDLGtCQUNMQyxNQUFNLENBQUUsbURBSVJTLEtBQUssQ0FBQyxjQUFjO2dDQUFFQyxXQUFXOzRCQUFNLEdBQ3ZDSixLQUFLLENBQUNBOzs7d0JBUGUsc0JBQWhCVCxPQUFnQixLQUFoQkEsTUFBTUMsUUFBVSxLQUFWQTt3QkFTZCxJQUFJQSxPQUFPOzRCQUNUYSxRQUFRYixLQUFLLENBQUMsa0NBQWtDQTs0QkFDaEQsTUFBTUE7d0JBQ1I7d0JBRUE7OzRCQUFPRDs7OztRQUNUOztJQUVBLHVCQUF1QjtJQXBCWndELGNBcUJFRSxrQkFpQlosR0FqQkQsU0FBYUEsbUJBQW1CQyxNQUE4RDtlQUE5RjtnQkFDMEIsTUFBaEIzRCxNQUFNQzs7Ozt3QkFBVTs7NEJBQU1QLFNBQzNCUSxJQUFJLENBQUMsa0JBQ0xnRCxNQUFNLENBQUMsc0lBQ0hTO2dDQUNIMUIsWUFBWSxJQUFJVSxPQUFPQyxXQUFXO2dDQUNsQ1YsWUFBWSxJQUFJUyxPQUFPQyxXQUFXO2dDQUVuQ3pDLE1BQU0sR0FDTmlDLE1BQU07Ozt3QkFSZSxzQkFBaEJwQyxPQUFnQixLQUFoQkEsTUFBTUMsUUFBVSxLQUFWQTt3QkFVZCxJQUFJQSxPQUFPOzRCQUNUYSxRQUFRYixLQUFLLENBQUMsaUNBQWlDQTs0QkFDL0MsTUFBTUE7d0JBQ1I7d0JBRUE7OzRCQUFPRDs7OztRQUNUOztXQXRDV3dEO0lBdUNaO0FBRUQscUJBQXFCO0FBQ2QsaUNBQUs7O2FBQUNJOytFQUFBQTs7SUFDWCxtQkFBbUI7SUFEUkEsYUFFRUMsWUFnQlosR0FoQkQsU0FBYUEsYUFBYWQsTUFBYztlQUF4QztnQkFDMEIsTUFBaEIvQyxNQUFNQzs7Ozt3QkFBVTs7NEJBQU1QLFNBQzNCUSxJQUFJLENBQUMsZUFDTEMsTUFBTSxDQUFFLG1EQUlSSSxFQUFFLENBQUMsV0FBV3dDLFFBQ2RuQyxLQUFLLENBQUM7Ozt3QkFQZSxzQkFBaEJaLE9BQWdCLEtBQWhCQSxNQUFNQyxRQUFVLEtBQVZBO3dCQVNkLElBQUlBLE9BQU87NEJBQ1RhLFFBQVFiLEtBQUssQ0FBQyw4QkFBOEJBOzRCQUM1QyxNQUFNQTt3QkFDUjt3QkFFQTs7NEJBQU9EOzs7O1FBQ1Q7O0lBRUEsc0JBQXNCO0lBcEJYNEQsYUFxQkVFLGdCQWdCWixHQWhCRCxTQUFhQSxpQkFBaUJDLFVBQWdEO2VBQTlFO2dCQUMwQixNQUFoQi9ELE1BQU1DOzs7O3dCQUFVOzs0QkFBTVAsU0FDM0JRLElBQUksQ0FBQyxlQUNMZ0QsTUFBTSxDQUFDLHNJQUNIYTtnQ0FDSDlCLFlBQVksSUFBSVUsT0FBT0MsV0FBVztnQ0FFbkN6QyxNQUFNLEdBQ05pQyxNQUFNOzs7d0JBUGUsc0JBQWhCcEMsT0FBZ0IsS0FBaEJBLE1BQU1DLFFBQVUsS0FBVkE7d0JBU2QsSUFBSUEsT0FBTzs0QkFDVGEsUUFBUWIsS0FBSyxDQUFDLGlDQUFpQ0E7NEJBQy9DLE1BQU1BO3dCQUNSO3dCQUVBOzs0QkFBT0Q7Ozs7UUFDVDs7SUFFQSxjQUFjO0lBdkNINEQsYUF3Q0VJLFVBc0JaLEdBdEJELFNBQWFBLFdBQVdqQixNQUFjLEVBQUVrQixRQUFnQjtlQUF4RDtnQkFRMEIsTUFBaEJqRSxNQUFNQzs7Ozt3QkFQZCxnREFBZ0Q7d0JBQ2hEOzs0QkFBTVAsU0FDSFEsSUFBSSxDQUFDLGVBQ0x3QyxNQUFNLENBQUM7Z0NBQUV3QixZQUFZO2dDQUFPQyxpQkFBaUI7NEJBQU0sR0FDbkQ1RCxFQUFFLENBQUMsV0FBV3dDOzs7d0JBSGpCO3dCQU13Qjs7NEJBQU1yRCxTQUMzQlEsSUFBSSxDQUFDLGVBQ0x3QyxNQUFNLENBQUM7Z0NBQUV3QixZQUFZOzRCQUFLLEdBQzFCM0QsRUFBRSxDQUFDLFdBQVd3QyxRQUNkeEMsRUFBRSxDQUFDLGFBQWEwRCxVQUNoQjlELE1BQU0sR0FDTmlDLE1BQU07Ozt3QkFOZSxzQkFBaEJwQyxPQUFnQixLQUFoQkEsTUFBTUMsUUFBVSxLQUFWQTt3QkFRZCxJQUFJQSxPQUFPOzRCQUNUYSxRQUFRYixLQUFLLENBQUMsMEJBQTBCQTs0QkFDeEMsTUFBTUE7d0JBQ1I7d0JBRUE7OzRCQUFPRDs7OztRQUNUOztXQTlEVzREO0lBK0RaO0FBRUQsb0JBQW9CO0FBQ2IscUNBQUs7O2FBQUNROytFQUFBQTs7SUFDWCxzQkFBc0I7SUFEWEEsaUJBRUVDLGlCQWFaLEdBYkQsU0FBYUE7ZUFBYjtnQkFDb0QsTUFBM0NDLGNBQWNDLFlBQVlDOzs7O3dCQUFpQjs7NEJBQU1DLFFBQVFDLEdBQUc7Z0NBQ2pFaEYsU0FBU1EsSUFBSSxDQUFDLFdBQVdDLE1BQU0sQ0FBQyxNQUFNO29DQUFFd0UsT0FBTztvQ0FBU0MsTUFBTTtnQ0FBSztnQ0FDbkVsRixTQUFTUSxJQUFJLENBQUMsV0FBV0MsTUFBTSxDQUFDLFFBQVE7b0NBQUV3RSxPQUFPO29DQUFTQyxNQUFNO2dDQUFLLEdBQUdDLFFBQVE7Z0NBQ2hGbkYsU0FBU1EsSUFBSSxDQUFDLGtCQUFrQkMsTUFBTSxDQUFDLE1BQU07b0NBQUV3RSxPQUFPO29DQUFTQyxNQUFNO2dDQUFLOzs7O3dCQUgxQjs0QkFBQTs7NEJBQTNDTixlQUEyQyxTQUE3QkMsYUFBNkIsU0FBakJDLGdCQUFpQjt3QkFNbEQ7OzRCQUFPO2dDQUNMTSxlQUFlUixhQUFhSyxLQUFLLElBQUk7Z0NBQ3JDSSxhQUFhO2dDQUNiQyxpQkFBaUJSLGNBQWNHLEtBQUssSUFBSTtnQ0FDeENNLGNBQWMsSUFBSXRDLE9BQU9DLFdBQVc7NEJBQ3RDOzs7O1FBQ0Y7O0lBRUEsdUJBQXVCO0lBakJad0IsaUJBa0JFYyxrQkFhWixHQWJELFNBQWFBO1lBQW1CekUsUUFBQUEsaUVBQWdCO2VBQWhEO2dCQUMwQixNQUFoQlQsTUFBTUM7Ozs7d0JBQVU7OzRCQUFNUCxTQUMzQlEsSUFBSSxDQUFDLFdBQ0xDLE1BQU0sQ0FBQyxLQUNQUyxLQUFLLENBQUMsUUFBUTtnQ0FBRUMsV0FBVzs0QkFBTSxHQUNqQ0osS0FBSyxDQUFDQTs7O3dCQUplLHNCQUFoQlQsT0FBZ0IsS0FBaEJBLE1BQU1DLFFBQVUsS0FBVkE7d0JBTWQsSUFBSUEsT0FBTzs0QkFDVGEsUUFBUWIsS0FBSyxDQUFDLG9DQUFvQ0E7NEJBQ2xELE1BQU1BO3dCQUNSO3dCQUVBOzs0QkFBT0Q7Ozs7UUFDVDs7V0EvQldvRTtJQWdDWjtBQUVELG9DQUFvQztBQUM3QixpQ0FBSzs7YUFBQ2U7K0VBQUFBOztJQUFBQSxhQUlKQyxHQU1OLEdBTkQsU0FBT0EsSUFBSUMsR0FBVyxFQUFFckYsSUFBUztRQUMvQixJQUFNc0YsWUFBWTtZQUNoQnRGLE1BQUFBO1lBQ0F1RixXQUFXNUMsS0FBSzZDLEdBQUc7UUFDckI7UUFDQUMsYUFBYUMsT0FBTyxDQUFDLElBQUksQ0FBQ0MsWUFBWSxHQUFHTixLQUFLTyxLQUFLQyxTQUFTLENBQUNQO0lBQy9EO0lBVldILGFBWUpXLEdBV04sR0FYRCxTQUFPQSxJQUFJVCxHQUFXO1FBQ3BCLElBQU1VLFNBQVNOLGFBQWFPLE9BQU8sQ0FBQyxJQUFJLENBQUNMLFlBQVksR0FBR047UUFDeEQsSUFBSSxDQUFDVSxRQUFRLE9BQU87UUFFcEIsSUFBNEJILGNBQUFBLEtBQUtLLEtBQUssQ0FBQ0YsU0FBL0IvRixPQUFvQjRGLFlBQXBCNUYsTUFBTXVGLFlBQWNLLFlBQWRMO1FBQ2QsSUFBSTVDLEtBQUs2QyxHQUFHLEtBQUtELFlBQVksSUFBSSxDQUFDVyxjQUFjLEVBQUU7WUFDaERULGFBQWFVLFVBQVUsQ0FBQyxJQUFJLENBQUNSLFlBQVksR0FBR047WUFDNUMsT0FBTztRQUNUO1FBRUEsT0FBT3JGO0lBQ1Q7SUF2QldtRixhQXlCSmlCLEtBTU4sR0FORCxTQUFPQTs7UUFDTEMsT0FBT0MsSUFBSSxDQUFDYixjQUFjYyxPQUFPLENBQUNsQixTQUFBQTtZQUNoQyxJQUFJQSxJQUFJbUIsVUFBVSxDQUFDLE1BQUtiLFlBQVksR0FBRztnQkFDckNGLGFBQWFVLFVBQVUsQ0FBQ2Q7WUFDMUI7UUFDRjtJQUNGO1dBL0JXRjtJQWdDWjtBQWhDWUEsYUFDSVEsZUFBZTtBQURuQlIsYUFFSWUsaUJBQWlCLElBQUksS0FBSyxLQUFNLFlBQVk7O0FBZ0M3RCwrREFBZXhHLFFBQVFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL3NlcnZpY2VzL3N1cGFiYXNlLnRzPzEyNDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcblxuLy8gU3VwYWJhc2UgY29uZmlndXJhdGlvblxuY29uc3Qgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwgfHwgJ2h0dHBzOi8vZnV4cGRnc2l4bmJic2RzcHVzbXAuc3VwYWJhc2UuY28nO1xuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkgfHwgJ2V5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpwYzNNaU9pSnpkWEJoWW1GelpTSXNJbkpsWmlJNkltWjFlSEJrWjNOcGVHNWlZbk5rYzNCMWMyMXdJaXdpY205c1pTSTZJbUZ1YjI0aUxDSnBZWFFpT2pFM05UQTFNRGd3TlRZc0ltVjRjQ0k2TWpBMk5qQTROREExTm4wLmlmRjE1Wm9ycUN6R1p2TC1idUxobTd0NTFUME1XdFYtOUVQU05vdHpxYUEnO1xuY29uc3Qgc3VwYWJhc2VTZXJ2aWNlS2V5ID0gcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSB8fCAnZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SnBjM01pT2lKemRYQmhZbUZ6WlNJc0luSmxaaUk2SW1aMWVIQmtaM05wZUc1aVluTmtjM0IxYzIxd0lpd2ljbTlzWlNJNkluTmxjblpwWTJWZmNtOXNaU0lzSW1saGRDSTZNVGMxTURVd09EQTFOaXdpWlhod0lqb3lNRFkyTURnME1EVTJmUS5mNXFaT0hRbEFLel9zYkl3b0dtWTRHdWI4YkdIeXhfanhsUllaay0xVlBnJztcblxuLy8gQ3JlYXRlIFN1cGFiYXNlIGNsaWVudHNcbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VBbm9uS2V5KTtcbmV4cG9ydCBjb25zdCBzdXBhYmFzZUFkbWluID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZVNlcnZpY2VLZXkpO1xuXG4vLyBUeXBlcyBmb3Igb3VyIGRhdGFiYXNlIHNjaGVtYVxuZXhwb3J0IGludGVyZmFjZSBQbGF5ZXIge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIHBvc2l0aW9uOiBzdHJpbmc7XG4gIHRlYW06IHN0cmluZztcbiAgcHJpY2U6IG51bWJlcjtcbiAgcG9pbnRzOiBudW1iZXI7XG4gIGZvcm06IG51bWJlcjtcbiAgb3duZXJzaGlwOiBudW1iZXI7XG4gIGJyZWFrZXZlbjogbnVtYmVyO1xuICBpbWFnZV91cmw/OiBzdHJpbmc7XG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFRyYWRlUmVjb21tZW5kYXRpb24ge1xuICBpZDogc3RyaW5nO1xuICB1c2VyX2lkOiBzdHJpbmc7XG4gIHBsYXllcl9vdXRfaWQ6IHN0cmluZztcbiAgcGxheWVyX2luX2lkOiBzdHJpbmc7XG4gIHJlYXNvbjogc3RyaW5nO1xuICBjb25maWRlbmNlOiBudW1iZXI7XG4gIHByaWNlX2NoYW5nZTogbnVtYmVyO1xuICBwb2ludHNfZ2FpbjogbnVtYmVyO1xuICByaXNrX2xldmVsOiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnO1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5qdXJ5UmVwb3J0IHtcbiAgaWQ6IHN0cmluZztcbiAgcGxheWVyX2lkOiBzdHJpbmc7XG4gIGluanVyeV90eXBlOiBzdHJpbmc7XG4gIHN0YXR1czogc3RyaW5nO1xuICBleHBlY3RlZF9yZXR1cm4/OiBzdHJpbmc7XG4gIHNldmVyaXR5OiAnbWlub3InIHwgJ21vZGVyYXRlJyB8ICdtYWpvcic7XG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFVzZXJTcXVhZCB7XG4gIGlkOiBzdHJpbmc7XG4gIHVzZXJfaWQ6IHN0cmluZztcbiAgcGxheWVyX2lkOiBzdHJpbmc7XG4gIHBvc2l0aW9uX2luX3NxdWFkOiBzdHJpbmc7XG4gIGlzX2NhcHRhaW46IGJvb2xlYW47XG4gIGlzX3ZpY2VfY2FwdGFpbjogYm9vbGVhbjtcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xufVxuXG4vLyBQbGF5ZXIgc2VydmljZSBmdW5jdGlvbnNcbmV4cG9ydCBjbGFzcyBQbGF5ZXJTZXJ2aWNlIHtcbiAgLy8gR2V0IGFsbCBwbGF5ZXJzIHdpdGggc2VhcmNoIGFuZCBmaWx0ZXJpbmdcbiAgc3RhdGljIGFzeW5jIGdldFBsYXllcnMob3B0aW9uczoge1xuICAgIHNlYXJjaD86IHN0cmluZztcbiAgICBwb3NpdGlvbj86IHN0cmluZztcbiAgICB0ZWFtPzogc3RyaW5nO1xuICAgIGxpbWl0PzogbnVtYmVyO1xuICAgIG9mZnNldD86IG51bWJlcjtcbiAgfSA9IHt9KSB7XG4gICAgbGV0IHF1ZXJ5ID0gc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdwbGF5ZXJzJylcbiAgICAgIC5zZWxlY3QoJyonKTtcblxuICAgIGlmIChvcHRpb25zLnNlYXJjaCkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5vcihgbmFtZS5pbGlrZS4lJHtvcHRpb25zLnNlYXJjaH0lLHRlYW0uaWxpa2UuJSR7b3B0aW9ucy5zZWFyY2h9JSxwb3NpdGlvbi5pbGlrZS4lJHtvcHRpb25zLnNlYXJjaH0lYCk7XG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnMucG9zaXRpb24pIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuZXEoJ3Bvc2l0aW9uJywgb3B0aW9ucy5wb3NpdGlvbik7XG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnMudGVhbSkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5lcSgndGVhbScsIG9wdGlvbnMudGVhbSk7XG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnMubGltaXQpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkubGltaXQob3B0aW9ucy5saW1pdCk7XG4gICAgfVxuXG4gICAgaWYgKG9wdGlvbnMub2Zmc2V0KSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LnJhbmdlKG9wdGlvbnMub2Zmc2V0LCBvcHRpb25zLm9mZnNldCArIChvcHRpb25zLmxpbWl0IHx8IDEwKSAtIDEpO1xuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHF1ZXJ5Lm9yZGVyKCdwb2ludHMnLCB7IGFzY2VuZGluZzogZmFsc2UgfSk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHBsYXllcnM6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgUGxheWVyW107XG4gIH1cblxuICAvLyBHZXQgQUxMIHBsYXllcnMgKGZvciBQbGF5ZXJzIHBhZ2UgLSBzaG91bGQgcmV0dXJuIDU4MSBwbGF5ZXJzKVxuICBzdGF0aWMgYXN5bmMgZ2V0QWxsUGxheWVycygpIHtcbiAgICBjb25zb2xlLmxvZygn8J+UhCBQbGF5ZXJTZXJ2aWNlLmdldEFsbFBsYXllcnMoKSAtIGZldGNoaW5nIGNvbXBsZXRlIDU4MSBwbGF5ZXIgZGF0YXNldCBmcm9tIG5ybF9wbGF5ZXJzIHRhYmxlJyk7XG5cbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ25ybF9wbGF5ZXJzJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLm9yZGVyKCduYW1lJyk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBmZXRjaGluZyBhbGwgcGxheWVycyBmcm9tIG5ybF9wbGF5ZXJzIHRhYmxlOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKGDinIUgU3VwYWJhc2UgbnJsX3BsYXllcnMgdGFibGUgcmV0dXJuZWQgJHtkYXRhPy5sZW5ndGggfHwgMH0gcGxheWVyc2ApO1xuXG4gICAgLy8gVHJhbnNmb3JtIG5ybF9wbGF5ZXJzIGRhdGEgdG8gbWF0Y2ggb3VyIFBsYXllciBpbnRlcmZhY2VcbiAgICBjb25zdCB0cmFuc2Zvcm1lZFBsYXllcnMgPSBkYXRhPy5tYXAocGxheWVyID0+ICh7XG4gICAgICBpZDogcGxheWVyLmlkLnRvU3RyaW5nKCksXG4gICAgICBuYW1lOiBwbGF5ZXIubmFtZSxcbiAgICAgIHBvc2l0aW9uOiBwbGF5ZXIucG9zaXRpb24gfHwgJ1Vua25vd24nLFxuICAgICAgdGVhbTogcGxheWVyLnRlYW1fbmFtZSB8fCAnVW5rbm93biBUZWFtJyxcbiAgICAgIHByaWNlOiBwbGF5ZXIuc3RhdGlzdGljcz8ucHJpY2UgfHwgMzAwMDAwLFxuICAgICAgcG9pbnRzOiBwbGF5ZXIuc3RhdGlzdGljcz8udG90YWxfcG9pbnRzIHx8IDAsXG4gICAgICBmb3JtOiBwbGF5ZXIuc3RhdGlzdGljcz8uZm9ybSB8fCAwLFxuICAgICAgb3duZXJzaGlwOiBwbGF5ZXIuc3RhdGlzdGljcz8ub3duZXJzaGlwIHx8IDAsXG4gICAgICBicmVha2V2ZW46IHBsYXllci5zdGF0aXN0aWNzPy5icmVha2V2ZW4gfHwgMCxcbiAgICAgIGltYWdlX3VybDogcGxheWVyLmltYWdlX3VybCxcbiAgICAgIGNyZWF0ZWRfYXQ6IHBsYXllci5jcmVhdGVkX2F0LFxuICAgICAgdXBkYXRlZF9hdDogcGxheWVyLnVwZGF0ZWRfYXRcbiAgICB9KSkgfHwgW107XG5cbiAgICByZXR1cm4gdHJhbnNmb3JtZWRQbGF5ZXJzIGFzIFBsYXllcltdO1xuICB9XG5cbiAgLy8gR2V0IHBsYXllciBieSBJRFxuICBzdGF0aWMgYXN5bmMgZ2V0UGxheWVyKGlkOiBzdHJpbmcpIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3BsYXllcnMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ2lkJywgaWQpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHBsYXllcjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YSBhcyBQbGF5ZXI7XG4gIH1cblxuICAvLyBTZWFyY2ggcGxheWVycyB3aXRoIHByZWRpY3RpdmUgYWxnb3JpdGhtXG4gIHN0YXRpYyBhc3luYyBzZWFyY2hQbGF5ZXJzKHF1ZXJ5OiBzdHJpbmcsIGxpbWl0OiBudW1iZXIgPSAxMCkge1xuICAgIGlmICghcXVlcnkgfHwgcXVlcnkubGVuZ3RoIDwgMikgcmV0dXJuIFtdO1xuXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdwbGF5ZXJzJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLm9yKGBuYW1lLmlsaWtlLiUke3F1ZXJ5fSUsdGVhbS5pbGlrZS4lJHtxdWVyeX0lLHBvc2l0aW9uLmlsaWtlLiUke3F1ZXJ5fSVgKVxuICAgICAgLm9yZGVyKCdwb2ludHMnLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcbiAgICAgIC5saW1pdChsaW1pdCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNlYXJjaGluZyBwbGF5ZXJzOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YSBhcyBQbGF5ZXJbXTtcbiAgfVxuXG4gIC8vIEdldCB0b3AgcGVyZm9ybWVyc1xuICBzdGF0aWMgYXN5bmMgZ2V0VG9wUGVyZm9ybWVycyhsaW1pdDogbnVtYmVyID0gMTApIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3BsYXllcnMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAub3JkZXIoJ3BvaW50cycsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgLmxpbWl0KGxpbWl0KTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdG9wIHBlcmZvcm1lcnM6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgUGxheWVyW107XG4gIH1cblxuICAvLyBHZXQgcHJpY2UgcmlzZXJzXG4gIHN0YXRpYyBhc3luYyBnZXRQcmljZVJpc2VycyhsaW1pdDogbnVtYmVyID0gMTApIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3BsYXllcnMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAub3JkZXIoJ3ByaWNlJywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG4gICAgICAubGltaXQobGltaXQpO1xuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwcmljZSByaXNlcnM6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgUGxheWVyW107XG4gIH1cblxuICAvLyBVcGRhdGUgcGxheWVyIGRhdGFcbiAgc3RhdGljIGFzeW5jIHVwZGF0ZVBsYXllcihpZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPFBsYXllcj4pIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3BsYXllcnMnKVxuICAgICAgLnVwZGF0ZSh7IC4uLnVwZGF0ZXMsIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSB9KVxuICAgICAgLmVxKCdpZCcsIGlkKVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIHBsYXllcjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YSBhcyBQbGF5ZXI7XG4gIH1cbn1cblxuLy8gVHJhZGUgcmVjb21tZW5kYXRpb24gc2VydmljZVxuZXhwb3J0IGNsYXNzIFRyYWRlU2VydmljZSB7XG4gIC8vIEdldCB0cmFkZSByZWNvbW1lbmRhdGlvbnMgZm9yIHVzZXJcbiAgc3RhdGljIGFzeW5jIGdldFRyYWRlUmVjb21tZW5kYXRpb25zKHVzZXJJZDogc3RyaW5nLCBsaW1pdDogbnVtYmVyID0gMTApIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3RyYWRlX3JlY29tbWVuZGF0aW9ucycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgcGxheWVyX291dDpwbGF5ZXJzIXBsYXllcl9vdXRfaWQoKiksXG4gICAgICAgIHBsYXllcl9pbjpwbGF5ZXJzIXBsYXllcl9pbl9pZCgqKVxuICAgICAgYClcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgIC5vcmRlcignY29uZmlkZW5jZScsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgLmxpbWl0KGxpbWl0KTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdHJhZGUgcmVjb21tZW5kYXRpb25zOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cblxuICAgIHJldHVybiBkYXRhO1xuICB9XG5cbiAgLy8gQ3JlYXRlIHRyYWRlIHJlY29tbWVuZGF0aW9uXG4gIHN0YXRpYyBhc3luYyBjcmVhdGVUcmFkZVJlY29tbWVuZGF0aW9uKHJlY29tbWVuZGF0aW9uOiBPbWl0PFRyYWRlUmVjb21tZW5kYXRpb24sICdpZCcgfCAnY3JlYXRlZF9hdCc+KSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd0cmFkZV9yZWNvbW1lbmRhdGlvbnMnKVxuICAgICAgLmluc2VydCh7XG4gICAgICAgIC4uLnJlY29tbWVuZGF0aW9uLFxuICAgICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgdHJhZGUgcmVjb21tZW5kYXRpb246JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgVHJhZGVSZWNvbW1lbmRhdGlvbjtcbiAgfVxuXG4gIC8vIEV4ZWN1dGUgdHJhZGUgKGxvZyB0aGUgdHJhZGUpXG4gIHN0YXRpYyBhc3luYyBleGVjdXRlVHJhZGUodHJhZGVJZDogc3RyaW5nLCB1c2VySWQ6IHN0cmluZykge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnZXhlY3V0ZWRfdHJhZGVzJylcbiAgICAgIC5pbnNlcnQoe1xuICAgICAgICB0cmFkZV9yZWNvbW1lbmRhdGlvbl9pZDogdHJhZGVJZCxcbiAgICAgICAgdXNlcl9pZDogdXNlcklkLFxuICAgICAgICBleGVjdXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGV4ZWN1dGluZyB0cmFkZTonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YTtcbiAgfVxufVxuXG4vLyBJbmp1cnkgc2VydmljZVxuZXhwb3J0IGNsYXNzIEluanVyeVNlcnZpY2Uge1xuICAvLyBHZXQgY3VycmVudCBpbmp1cnkgcmVwb3J0c1xuICBzdGF0aWMgYXN5bmMgZ2V0SW5qdXJ5UmVwb3J0cyhsaW1pdDogbnVtYmVyID0gMTApIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2luanVyeV9yZXBvcnRzJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBwbGF5ZXI6cGxheWVycygqKVxuICAgICAgYClcbiAgICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgLmxpbWl0KGxpbWl0KTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgaW5qdXJ5IHJlcG9ydHM6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGE7XG4gIH1cblxuICAvLyBDcmVhdGUgaW5qdXJ5IHJlcG9ydFxuICBzdGF0aWMgYXN5bmMgY3JlYXRlSW5qdXJ5UmVwb3J0KHJlcG9ydDogT21pdDxJbmp1cnlSZXBvcnQsICdpZCcgfCAnY3JlYXRlZF9hdCcgfCAndXBkYXRlZF9hdCc+KSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdpbmp1cnlfcmVwb3J0cycpXG4gICAgICAuaW5zZXJ0KHtcbiAgICAgICAgLi4ucmVwb3J0LFxuICAgICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSlcbiAgICAgIC5zZWxlY3QoKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBpbmp1cnkgcmVwb3J0OicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cblxuICAgIHJldHVybiBkYXRhIGFzIEluanVyeVJlcG9ydDtcbiAgfVxufVxuXG4vLyBVc2VyIHNxdWFkIHNlcnZpY2VcbmV4cG9ydCBjbGFzcyBTcXVhZFNlcnZpY2Uge1xuICAvLyBHZXQgdXNlcidzIHNxdWFkXG4gIHN0YXRpYyBhc3luYyBnZXRVc2VyU3F1YWQodXNlcklkOiBzdHJpbmcpIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3VzZXJfc3F1YWRzJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBwbGF5ZXI6cGxheWVycygqKVxuICAgICAgYClcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgIC5vcmRlcigncG9zaXRpb25faW5fc3F1YWQnKTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdXNlciBzcXVhZDonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YTtcbiAgfVxuXG4gIC8vIEFkZCBwbGF5ZXIgdG8gc3F1YWRcbiAgc3RhdGljIGFzeW5jIGFkZFBsYXllclRvU3F1YWQoc3F1YWRFbnRyeTogT21pdDxVc2VyU3F1YWQsICdpZCcgfCAnY3JlYXRlZF9hdCc+KSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd1c2VyX3NxdWFkcycpXG4gICAgICAuaW5zZXJ0KHtcbiAgICAgICAgLi4uc3F1YWRFbnRyeSxcbiAgICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9KVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFkZGluZyBwbGF5ZXIgdG8gc3F1YWQ6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgVXNlclNxdWFkO1xuICB9XG5cbiAgLy8gU2V0IGNhcHRhaW5cbiAgc3RhdGljIGFzeW5jIHNldENhcHRhaW4odXNlcklkOiBzdHJpbmcsIHBsYXllcklkOiBzdHJpbmcpIHtcbiAgICAvLyBGaXJzdCwgcmVtb3ZlIGNhcHRhaW4gc3RhdHVzIGZyb20gYWxsIHBsYXllcnNcbiAgICBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3VzZXJfc3F1YWRzJylcbiAgICAgIC51cGRhdGUoeyBpc19jYXB0YWluOiBmYWxzZSwgaXNfdmljZV9jYXB0YWluOiBmYWxzZSB9KVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlcklkKTtcblxuICAgIC8vIFRoZW4gc2V0IHRoZSBuZXcgY2FwdGFpblxuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndXNlcl9zcXVhZHMnKVxuICAgICAgLnVwZGF0ZSh7IGlzX2NhcHRhaW46IHRydWUgfSlcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgIC5lcSgncGxheWVyX2lkJywgcGxheWVySWQpXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2V0dGluZyBjYXB0YWluOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cblxuICAgIHJldHVybiBkYXRhIGFzIFVzZXJTcXVhZDtcbiAgfVxufVxuXG4vLyBBbmFseXRpY3Mgc2VydmljZVxuZXhwb3J0IGNsYXNzIEFuYWx5dGljc1NlcnZpY2Uge1xuICAvLyBHZXQgZGFzaGJvYXJkIHN0YXRzXG4gIHN0YXRpYyBhc3luYyBnZXREYXNoYm9hcmRTdGF0cygpIHtcbiAgICBjb25zdCBbcGxheWVyc0NvdW50LCB0ZWFtc0NvdW50LCBpbmp1cmllc0NvdW50XSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgIHN1cGFiYXNlLmZyb20oJ3BsYXllcnMnKS5zZWxlY3QoJ2lkJywgeyBjb3VudDogJ2V4YWN0JywgaGVhZDogdHJ1ZSB9KSxcbiAgICAgIHN1cGFiYXNlLmZyb20oJ3BsYXllcnMnKS5zZWxlY3QoJ3RlYW0nLCB7IGNvdW50OiAnZXhhY3QnLCBoZWFkOiB0cnVlIH0pLmRpc3RpbmN0KCksXG4gICAgICBzdXBhYmFzZS5mcm9tKCdpbmp1cnlfcmVwb3J0cycpLnNlbGVjdCgnaWQnLCB7IGNvdW50OiAnZXhhY3QnLCBoZWFkOiB0cnVlIH0pXG4gICAgXSk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgdG90YWxfcGxheWVyczogcGxheWVyc0NvdW50LmNvdW50IHx8IDAsXG4gICAgICB0b3RhbF90ZWFtczogMTcsIC8vIE5STCBoYXMgMTcgdGVhbXNcbiAgICAgIGFjdGl2ZV9pbmp1cmllczogaW5qdXJpZXNDb3VudC5jb3VudCB8fCAwLFxuICAgICAgbGFzdF91cGRhdGVkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9O1xuICB9XG5cbiAgLy8gR2V0IHRyZW5kaW5nIHBsYXllcnNcbiAgc3RhdGljIGFzeW5jIGdldFRyZW5kaW5nUGxheWVycyhsaW1pdDogbnVtYmVyID0gMTApIHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3BsYXllcnMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAub3JkZXIoJ2Zvcm0nLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcbiAgICAgIC5saW1pdChsaW1pdCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHRyZW5kaW5nIHBsYXllcnM6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuXG4gICAgcmV0dXJuIGRhdGEgYXMgUGxheWVyW107XG4gIH1cbn1cblxuLy8gQ2FjaGUgc2VydmljZSBmb3Igb2ZmbGluZSBzdXBwb3J0XG5leHBvcnQgY2xhc3MgQ2FjaGVTZXJ2aWNlIHtcbiAgcHJpdmF0ZSBzdGF0aWMgQ0FDSEVfUFJFRklYID0gJ2ZhbnRhc3lwcm9fY2FjaGVfJztcbiAgcHJpdmF0ZSBzdGF0aWMgQ0FDSEVfRFVSQVRJT04gPSA1ICogNjAgKiAxMDAwOyAvLyA1IG1pbnV0ZXNcblxuICBzdGF0aWMgc2V0KGtleTogc3RyaW5nLCBkYXRhOiBhbnkpIHtcbiAgICBjb25zdCBjYWNoZURhdGEgPSB7XG4gICAgICBkYXRhLFxuICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gICAgfTtcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSh0aGlzLkNBQ0hFX1BSRUZJWCArIGtleSwgSlNPTi5zdHJpbmdpZnkoY2FjaGVEYXRhKSk7XG4gIH1cblxuICBzdGF0aWMgZ2V0KGtleTogc3RyaW5nKSB7XG4gICAgY29uc3QgY2FjaGVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0odGhpcy5DQUNIRV9QUkVGSVggKyBrZXkpO1xuICAgIGlmICghY2FjaGVkKSByZXR1cm4gbnVsbDtcblxuICAgIGNvbnN0IHsgZGF0YSwgdGltZXN0YW1wIH0gPSBKU09OLnBhcnNlKGNhY2hlZCk7XG4gICAgaWYgKERhdGUubm93KCkgLSB0aW1lc3RhbXAgPiB0aGlzLkNBQ0hFX0RVUkFUSU9OKSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSh0aGlzLkNBQ0hFX1BSRUZJWCArIGtleSk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YTtcbiAgfVxuXG4gIHN0YXRpYyBjbGVhcigpIHtcbiAgICBPYmplY3Qua2V5cyhsb2NhbFN0b3JhZ2UpLmZvckVhY2goa2V5ID0+IHtcbiAgICAgIGlmIChrZXkuc3RhcnRzV2l0aCh0aGlzLkNBQ0hFX1BSRUZJWCkpIHtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oa2V5KTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxufVxuXG5leHBvcnQgZGVmYXVsdCBzdXBhYmFzZTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJzdXBhYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJzdXBhYmFzZUFub25LZXkiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsInN1cGFiYXNlU2VydmljZUtleSIsIlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkiLCJzdXBhYmFzZSIsInN1cGFiYXNlQWRtaW4iLCJQbGF5ZXJTZXJ2aWNlIiwiZ2V0UGxheWVycyIsIm9wdGlvbnMiLCJxdWVyeSIsImRhdGEiLCJlcnJvciIsImZyb20iLCJzZWxlY3QiLCJzZWFyY2giLCJvciIsInBvc2l0aW9uIiwiZXEiLCJ0ZWFtIiwibGltaXQiLCJvZmZzZXQiLCJyYW5nZSIsIm9yZGVyIiwiYXNjZW5kaW5nIiwiY29uc29sZSIsImdldEFsbFBsYXllcnMiLCJ0cmFuc2Zvcm1lZFBsYXllcnMiLCJsb2ciLCJsZW5ndGgiLCJtYXAiLCJwbGF5ZXIiLCJpZCIsInRvU3RyaW5nIiwibmFtZSIsInRlYW1fbmFtZSIsInByaWNlIiwic3RhdGlzdGljcyIsInBvaW50cyIsInRvdGFsX3BvaW50cyIsImZvcm0iLCJvd25lcnNoaXAiLCJicmVha2V2ZW4iLCJpbWFnZV91cmwiLCJjcmVhdGVkX2F0IiwidXBkYXRlZF9hdCIsImdldFBsYXllciIsInNpbmdsZSIsInNlYXJjaFBsYXllcnMiLCJnZXRUb3BQZXJmb3JtZXJzIiwiZ2V0UHJpY2VSaXNlcnMiLCJ1cGRhdGVQbGF5ZXIiLCJ1cGRhdGVzIiwidXBkYXRlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiVHJhZGVTZXJ2aWNlIiwiZ2V0VHJhZGVSZWNvbW1lbmRhdGlvbnMiLCJ1c2VySWQiLCJjcmVhdGVUcmFkZVJlY29tbWVuZGF0aW9uIiwicmVjb21tZW5kYXRpb24iLCJpbnNlcnQiLCJleGVjdXRlVHJhZGUiLCJ0cmFkZUlkIiwidHJhZGVfcmVjb21tZW5kYXRpb25faWQiLCJ1c2VyX2lkIiwiZXhlY3V0ZWRfYXQiLCJJbmp1cnlTZXJ2aWNlIiwiZ2V0SW5qdXJ5UmVwb3J0cyIsImNyZWF0ZUluanVyeVJlcG9ydCIsInJlcG9ydCIsIlNxdWFkU2VydmljZSIsImdldFVzZXJTcXVhZCIsImFkZFBsYXllclRvU3F1YWQiLCJzcXVhZEVudHJ5Iiwic2V0Q2FwdGFpbiIsInBsYXllcklkIiwiaXNfY2FwdGFpbiIsImlzX3ZpY2VfY2FwdGFpbiIsIkFuYWx5dGljc1NlcnZpY2UiLCJnZXREYXNoYm9hcmRTdGF0cyIsInBsYXllcnNDb3VudCIsInRlYW1zQ291bnQiLCJpbmp1cmllc0NvdW50IiwiUHJvbWlzZSIsImFsbCIsImNvdW50IiwiaGVhZCIsImRpc3RpbmN0IiwidG90YWxfcGxheWVycyIsInRvdGFsX3RlYW1zIiwiYWN0aXZlX2luanVyaWVzIiwibGFzdF91cGRhdGVkIiwiZ2V0VHJlbmRpbmdQbGF5ZXJzIiwiQ2FjaGVTZXJ2aWNlIiwic2V0Iiwia2V5IiwiY2FjaGVEYXRhIiwidGltZXN0YW1wIiwibm93IiwibG9jYWxTdG9yYWdlIiwic2V0SXRlbSIsIkNBQ0hFX1BSRUZJWCIsIkpTT04iLCJzdHJpbmdpZnkiLCJnZXQiLCJjYWNoZWQiLCJnZXRJdGVtIiwicGFyc2UiLCJDQUNIRV9EVVJBVElPTiIsInJlbW92ZUl0ZW0iLCJjbGVhciIsIk9iamVjdCIsImtleXMiLCJmb3JFYWNoIiwic3RhcnRzV2l0aCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/services/supabase.ts\n"));

/***/ })

});