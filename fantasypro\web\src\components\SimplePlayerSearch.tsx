import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';

// Simplified Player interface
interface Player {
  id: string;
  name: string;
  team: string;
  position: string;
  price: number;
  average: number;
}

interface SimplePlayerSearchProps {
  placeholder?: string;
  onPlayerSelect?: (player: Player) => void;
  onSearchChange?: (query: string, filteredPlayers: Player[]) => void;
  className?: string;
  disabled?: boolean;
  maxResults?: number;
  minQueryLength?: number;
  showDropdown?: boolean;
}

const SimplePlayerSearch: React.FC<SimplePlayerSearchProps> = ({
  placeholder = "Search players...",
  onPlayerSelect,
  onSearchChange,
  className = "",
  disabled = false,
  maxResults = 8,
  minQueryLength = 1,
  showDropdown = true
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<Player[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [allPlayers, setAllPlayers] = useState<Player[]>([]);
  
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Load players from Supabase
  useEffect(() => {
    const loadPlayers = async () => {
      try {
        console.log('🔍 SimplePlayerSearch loading players from Supabase...');

        // Import PlayerService dynamically to avoid SSR issues
        const { PlayerService } = await import('../services/supabase');
        const players = await PlayerService.getAllPlayers();

        console.log(`✅ SimplePlayerSearch loaded ${players.length} players from Supabase`);
        setAllPlayers(players);

      } catch (error) {
        console.error('❌ Error loading players from Supabase:', error);

        // Fallback: try API endpoint
        try {
          console.log('🔄 Trying fallback API endpoint...');
          const response = await fetch('/api/players');
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.data.players) {
              console.log(`✅ SimplePlayerSearch loaded ${data.data.players.length} players from API fallback`);
              setAllPlayers(data.data.players);
            }
          }
        } catch (fallbackError) {
          console.error('❌ Fallback API also failed:', fallbackError);
        }
      }
    };

    loadPlayers();
  }, []);

  // Search function
  const searchPlayers = (searchQuery: string): Player[] => {
    if (!searchQuery.trim() || searchQuery.length < minQueryLength) {
      return [];
    }

    const query = searchQuery.toLowerCase();
    const filtered = allPlayers.filter(player => {
      const nameMatch = player.name.toLowerCase().includes(query);
      const teamMatch = player.team.toLowerCase().includes(query);
      return nameMatch || teamMatch;
    });

    // Sort by relevance (name starts with query first)
    filtered.sort((a, b) => {
      const aStartsWith = a.name.toLowerCase().startsWith(query);
      const bStartsWith = b.name.toLowerCase().startsWith(query);
      
      if (aStartsWith && !bStartsWith) return -1;
      if (!aStartsWith && bStartsWith) return 1;
      
      return a.name.localeCompare(b.name);
    });

    return filtered.slice(0, maxResults);
  };

  // Handle input change
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      const searchResults = searchPlayers(query);
      setResults(searchResults);
      
      if (showDropdown) {
        setIsOpen(query.length >= minQueryLength && searchResults.length > 0);
      }
      
      // Notify parent
      if (onSearchChange) {
        onSearchChange(query, searchResults);
      }
      
      setSelectedIndex(-1);
    }, 150);

    return () => clearTimeout(delayedSearch);
  }, [query, allPlayers, minQueryLength, maxResults, onSearchChange, showDropdown]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showDropdown || !isOpen || results.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev < results.length - 1 ? prev + 1 : 0));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : results.length - 1));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < results.length) {
          handlePlayerSelect(results[selectedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle player selection
  const handlePlayerSelect = (player: Player) => {
    if (onPlayerSelect) {
      onPlayerSelect(player);
    }
    
    setIsOpen(false);
    setSelectedIndex(-1);
  };

  // Clear search
  const clearSearch = () => {
    setQuery('');
    setResults([]);
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Format currency
  const formatCurrency = (amount: number | undefined | null) => {
    if (!amount || amount === 0) return '$0.00M';
    return `$${(amount / 1000000).toFixed(2)}M`;
  };

  // Get position color
  const getPositionColor = (position: string) => {
    const colors: { [key: string]: string } = {
      'FLB': 'bg-blue-500/20 text-blue-400',
      'CTW': 'bg-green-500/20 text-green-400',
      'HFB': 'bg-purple-500/20 text-purple-400',
      '5/8': 'bg-purple-500/20 text-purple-400',
      'HOK': 'bg-orange-500/20 text-orange-400',
      'FRF': 'bg-red-500/20 text-red-400',
      '2RF': 'bg-yellow-500/20 text-yellow-400',
      'LCK': 'bg-pink-500/20 text-pink-400'
    };
    return colors[position] || 'bg-gray-500/20 text-gray-400';
  };

  return (
    <div ref={searchRef} className={`relative search-container dropdown-container ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 themed-text-tertiary" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (showDropdown && results.length > 0) setIsOpen(true);
          }}
          placeholder={placeholder}
          disabled={disabled}
          className={`input-primary w-full pl-10 pr-10 ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        />

        {query && (
          <button
            type="button"
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            title="Clear search"
          >
            <XMarkIcon className="h-4 w-4 themed-text-tertiary hover:themed-text-primary transition-colors" />
          </button>
        )}
      </div>

      {/* Dropdown Results */}
      {showDropdown && (
        <AnimatePresence>
          {isOpen && results.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.15 }}
              className="search-dropdown dropdown-content themed-bg-secondary themed-border rounded-lg themed-shadow-xl overflow-hidden"
            >
              <div className="max-h-64 overflow-y-auto">
                {results.map((player, index) => (
                  <motion.div
                    key={player.id}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.1, delay: index * 0.02 }}
                    className={`p-3 cursor-pointer transition-all duration-150 ${
                      index === selectedIndex 
                        ? 'themed-bg-tertiary border-l-4 border-blue-400' 
                        : 'hover:themed-bg-tertiary'
                    } ${index < results.length - 1 ? 'border-b themed-border' : ''}`}
                    onClick={() => handlePlayerSelect(player)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <div className="font-medium themed-text-primary">
                            {player.name}
                          </div>
                          <span className={`px-2 py-1 rounded text-xs font-medium ${getPositionColor(player.position)}`}>
                            {player.position}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-3 text-sm themed-text-tertiary">
                          <span>{player.team}</span>
                          <span>•</span>
                          <span>Avg: {(player.average || 0).toFixed(1)}</span>
                          <span>•</span>
                          <span>{formatCurrency(player.price)}</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </div>
  );
};

export default SimplePlayerSearch;
