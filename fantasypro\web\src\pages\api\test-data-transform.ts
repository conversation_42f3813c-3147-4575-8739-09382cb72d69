import { NextApiRequest, NextApiResponse } from 'next';
import { PlayerService } from '../../services/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Testing data transformation...');
    
    // Test getting a few players
    const players = await PlayerService.getAllPlayers();
    
    if (!players || players.length === 0) {
      return res.status(500).json({
        success: false,
        error: 'No players returned from Supabase',
        timestamp: new Date().toISOString()
      });
    }
    
    // Check if all required fields are present
    const samplePlayer = players[0];
    const requiredFields = ['id', 'name', 'team', 'position', 'price', 'points', 'average', 'form'];
    const missingFields = requiredFields.filter(field => !(field in samplePlayer));
    
    // Test search functionality
    let searchResults = [];
    try {
      searchResults = await PlayerService.searchPlayers('Nathan', 3);
    } catch (searchError) {
      console.warn('Search test failed:', searchError);
    }
    
    const result = {
      success: true,
      data_transformation_test: {
        total_players: players.length,
        sample_player: samplePlayer,
        required_fields_present: missingFields.length === 0,
        missing_fields: missingFields,
        all_fields: Object.keys(samplePlayer),
        data_types: {
          id: typeof samplePlayer.id,
          name: typeof samplePlayer.name,
          team: typeof samplePlayer.team,
          position: typeof samplePlayer.position,
          price: typeof samplePlayer.price,
          points: typeof samplePlayer.points,
          average: typeof samplePlayer.average,
          form: typeof samplePlayer.form
        }
      },
      search_test: {
        query: 'Nathan',
        results_count: searchResults.length,
        sample_results: searchResults.slice(0, 2),
        search_working: searchResults.length > 0
      },
      validation: {
        data_complete: players.length >= 500,
        fields_valid: missingFields.length === 0,
        search_functional: searchResults.length > 0,
        ready_for_ui: players.length >= 500 && missingFields.length === 0
      },
      timestamp: new Date().toISOString()
    };
    
    console.log(`✅ Data transformation test complete:`);
    console.log(`   - Players: ${players.length}`);
    console.log(`   - Missing fields: ${missingFields.join(', ') || 'None'}`);
    console.log(`   - Search results: ${searchResults.length}`);
    console.log(`   - Ready for UI: ${result.validation.ready_for_ui}`);
    
    return res.status(200).json(result);
    
  } catch (error) {
    console.error('❌ Data transformation test failed:', error);
    return res.status(500).json({
      success: false,
      error: 'Data transformation test failed',
      details: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
  }
}
