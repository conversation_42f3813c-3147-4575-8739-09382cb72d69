"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/sportradar-proxy";
exports.ids = ["pages/api/sportradar-proxy"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsportradar-proxy&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csportradar-proxy.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsportradar-proxy&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csportradar-proxy.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_sportradar_proxy_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\sportradar-proxy.ts */ \"(api)/./src/pages/api/sportradar-proxy.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_sportradar_proxy_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_sportradar_proxy_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/sportradar-proxy\",\n        pathname: \"/api/sportradar-proxy\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_sportradar_proxy_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsportradar-proxy&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csportradar-proxy.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/sportradar-proxy.ts":
/*!*******************************************!*\
  !*** ./src/pages/api/sportradar-proxy.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TEST_ENDPOINTS: () => (/* binding */ TEST_ENDPOINTS),\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/**\n * SportRadar API Proxy\n * Server-side proxy to bypass CORS restrictions\n */ const SPORTRADAR_API_KEY = \"aqGaiDzyWLa0FB4njBsrzPJWdhpNVoW8xVWPgvQN\";\nconst SPORTRADAR_BASE_URL = \"https://api.sportradar.com/rugby-league/trial/v3\";\nasync function handler(req, res) {\n    // Only allow GET requests\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const { endpoint } = req.query;\n    if (!endpoint || typeof endpoint !== \"string\") {\n        return res.status(400).json({\n            error: \"Endpoint parameter required\"\n        });\n    }\n    try {\n        // Construct the full URL\n        const url = `${SPORTRADAR_BASE_URL}/${endpoint}?api_key=${SPORTRADAR_API_KEY}`;\n        console.log(\"\\uD83D\\uDD04 Proxying request to:\", url);\n        // Make the request to SportRadar\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Accept\": \"application/json\",\n                \"User-Agent\": \"FantasyPro/1.0\"\n            }\n        });\n        console.log(\"\\uD83D\\uDCCA SportRadar response status:\", response.status);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"❌ SportRadar error:\", errorText);\n            return res.status(response.status).json({\n                error: `SportRadar API error: ${response.status} ${response.statusText}`,\n                details: errorText\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ SportRadar success, data keys:\", Object.keys(data));\n        // Return the data with CORS headers\n        res.setHeader(\"Access-Control-Allow-Origin\", \"*\");\n        res.setHeader(\"Access-Control-Allow-Methods\", \"GET\");\n        res.setHeader(\"Access-Control-Allow-Headers\", \"Content-Type\");\n        return res.status(200).json({\n            success: true,\n            data,\n            endpoint: url,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"\\uD83D\\uDCA5 Proxy error:\", error);\n        return res.status(500).json({\n            error: \"Internal server error\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n// Test endpoints that we can try\nconst TEST_ENDPOINTS = [\n    \"competitions.json\",\n    \"seasons.json\",\n    \"teams.json\",\n    \"players.json\",\n    \"schedules.json\",\n    \"fixtures.json\",\n    \"injuries.json\"\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/sportradar-proxy.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsportradar-proxy&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csportradar-proxy.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();