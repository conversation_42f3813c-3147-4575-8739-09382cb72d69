/* 
 * Dropdown Z-Index Fix
 * Ensures predictive search dropdowns appear above all other elements
 */

/* High z-index for all dropdown containers */
.dropdown-container {
  position: relative;
  z-index: 1000;
}

/* Ultra-high z-index for dropdown content */
.dropdown-content {
  position: absolute !important;
  z-index: 9999 !important;
  top: 100% !important;
  left: 0 !important;
  right: 0 !important;
  margin-top: 8px !important;
}

/* Ensure parent containers don't clip dropdowns */
.widget-container,
.card,
.card-premium,
.card-glow {
  overflow: visible !important;
}

/* Specific fix for search containers */
.search-container {
  position: relative;
  z-index: 1001;
}

.search-dropdown {
  position: absolute !important;
  z-index: 9999 !important;
  top: calc(100% + 8px) !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  max-height: 400px !important;
  overflow-y: auto !important;
  background: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 8px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

/* Dark mode specific fixes */
@media (prefers-color-scheme: dark) {
  .search-dropdown {
    background: #1e293b !important;
    border-color: #334155 !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2) !important;
  }
}

/* Ensure dropdowns work in grid layouts */
.grid > * {
  overflow: visible !important;
}

/* Fix for specific FantasyPro components */
.my-team-container,
.dashboard-container,
.players-container {
  overflow: visible !important;
}

.my-team-container .card,
.dashboard-container .card,
.players-container .card {
  overflow: visible !important;
}

/* Portal dropdown styles */
#dropdown-portal {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 9999 !important;
  pointer-events: none;
}

#dropdown-portal > * {
  pointer-events: auto;
}

/* Animation support for dropdowns */
.dropdown-enter {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.dropdown-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: opacity 150ms ease-out, transform 150ms ease-out;
}

.dropdown-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.dropdown-exit-active {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
  transition: opacity 150ms ease-in, transform 150ms ease-in;
}

/* Responsive dropdown positioning */
@media (max-width: 768px) {
  .search-dropdown {
    max-height: 300px !important;
  }
}

@media (max-width: 480px) {
  .search-dropdown {
    max-height: 250px !important;
    left: -8px !important;
    right: -8px !important;
    width: calc(100% + 16px) !important;
  }
}

/* Ensure dropdowns don't get cut off at screen edges */
.search-dropdown {
  max-width: 100vw;
  box-sizing: border-box;
}

/* Fix for containers with transform properties */
.transform,
.scale-95,
.scale-100 {
  transform-style: preserve-3d;
}

/* Ensure search inputs have proper stacking context */
input[type="text"]:focus + .search-dropdown,
input[type="search"]:focus + .search-dropdown {
  z-index: 9999 !important;
}

/* Override any conflicting z-index values */
.z-10, .z-20, .z-30, .z-40, .z-50 {
  z-index: auto !important;
}

.search-dropdown {
  z-index: 9999 !important;
}

/* Specific fixes for FantasyPro layout */
.layout-container {
  overflow: visible !important;
}

.main-content {
  overflow: visible !important;
}

.content-wrapper {
  overflow: visible !important;
}

/* Ensure modals and overlays don't interfere */
.modal-overlay {
  z-index: 10000 !important;
}

.dropdown-overlay {
  z-index: 9999 !important;
}
