import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MagnifyingGlassIcon, XMarkIcon, UserIcon, TrophyIcon } from '@heroicons/react/24/outline';
import PortalDropdown from './PortalDropdown';

// Player interface
interface Player {
  id: string;
  name: string;
  team: string;
  position: string;
  price: number;
  points: number;
  average: number;
  form: number;
  ownership?: number;
  breakeven?: number;
  games_played?: number;
  source: string;
  last_updated: string;
}

interface SearchResult {
  player: Player;
  relevanceScore: number;
  matchType: 'name' | 'position' | 'team';
  highlightedName?: string;
}

interface PredictiveSearchProps {
  placeholder?: string;
  onPlayerSelect?: (player: Player) => void;
  onCaptainSelect?: (player: Player) => void;
  onSearchChange?: (query: string, results: Player[]) => void;
  showCaptainOption?: boolean;
  showPlayerDetails?: boolean;
  maxResults?: number;
  minQueryLength?: number;
  className?: string;
  disabled?: boolean;
  autoFocus?: boolean;
  clearOnSelect?: boolean;
  filterByPosition?: string[];
  filterByTeam?: string[];
  excludePlayerIds?: string[];
}

const PredictiveSearch: React.FC<PredictiveSearchProps> = ({
  placeholder = "Search players...",
  onPlayerSelect,
  onCaptainSelect,
  onSearchChange,
  showCaptainOption = false,
  showPlayerDetails = true,
  maxResults = 8,
  minQueryLength = 1,
  className = "",
  disabled = false,
  autoFocus = false,
  clearOnSelect = false,
  filterByPosition = [],
  filterByTeam = [],
  excludePlayerIds = []
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [allPlayers, setAllPlayers] = useState<Player[]>([]);
  
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Load all players on mount
  useEffect(() => {
    const loadPlayers = async () => {
      try {
        console.log('🔍 PredictiveSearch loading players...');
        const response = await fetch('/api/players');
        
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data.players) {
            console.log(`✅ PredictiveSearch loaded ${data.data.players.length} players`);
            setAllPlayers(data.data.players);
          }
        }
      } catch (error) {
        console.error('❌ Error loading players for predictive search:', error);
      }
    };
    
    loadPlayers();
  }, []);

  // Auto-focus if requested
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Highlight matching text in player name
  const highlightMatch = (text: string, query: string): string => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-400 text-black px-1 rounded">$1</mark>');
  };

  // Enhanced search algorithm with predictive capabilities
  const searchPlayers = useCallback(async (searchQuery: string): Promise<SearchResult[]> => {
    if (!searchQuery.trim() || searchQuery.length < minQueryLength) {
      return [];
    }

    setIsLoading(true);

    try {
      const players = allPlayers;
      const query = searchQuery.toLowerCase();
      const searchResults: SearchResult[] = [];

      players.forEach(player => {
        // Apply filters
        if (excludePlayerIds.includes(player.id)) return;
        if (filterByPosition.length > 0 && !filterByPosition.includes(player.position)) return;
        if (filterByTeam.length > 0 && !filterByTeam.includes(player.team)) return;

        let relevanceScore = 0;
        let matchType: 'name' | 'position' | 'team' = 'name';

        // Name matching (highest priority)
        const nameMatch = player.name.toLowerCase();
        if (nameMatch.includes(query)) {
          if (nameMatch.startsWith(query)) {
            relevanceScore += 100; // Exact start match
          } else if (nameMatch.split(' ').some(word => word.startsWith(query))) {
            relevanceScore += 90; // Word start match
          } else {
            relevanceScore += 70; // Contains match
          }
          matchType = 'name';
        }

        // Position matching
        if (player.position.toLowerCase().includes(query)) {
          relevanceScore += 50;
          if (matchType === 'name' && relevanceScore < 70) {
            matchType = 'position';
          }
        }

        // Team matching
        if (player.team.toLowerCase().includes(query)) {
          relevanceScore += 30;
          if (matchType === 'name' && relevanceScore < 70) {
            matchType = 'team';
          }
        }

        // Boost for high-performing players
        relevanceScore += (player.average / 10);
        relevanceScore += (player.form * 2);

        if (relevanceScore > 0) {
          searchResults.push({
            player,
            relevanceScore,
            matchType,
            highlightedName: highlightMatch(player.name, searchQuery)
          });
        }
      });

      // Sort by relevance score
      searchResults.sort((a, b) => b.relevanceScore - a.relevanceScore);

      setIsLoading(false);
      return searchResults.slice(0, maxResults);
    } catch (error) {
      console.error('Search failed:', error);
      setIsLoading(false);
      return [];
    }
  }, [allPlayers, minQueryLength, maxResults, excludePlayerIds, filterByPosition, filterByTeam]);

  // Handle search input with debouncing
  useEffect(() => {
    const delayedSearch = setTimeout(async () => {
      if (query.length >= minQueryLength) {
        const searchResults = await searchPlayers(query);
        setResults(searchResults);
        setIsOpen(true);
        
        // Notify parent of search results
        if (onSearchChange) {
          onSearchChange(query, searchResults.map(r => r.player));
        }
      } else {
        setResults([]);
        setIsOpen(false);
        
        if (onSearchChange) {
          onSearchChange(query, []);
        }
      }
      setSelectedIndex(-1);
    }, 150); // Fast response for predictive search

    return () => clearTimeout(delayedSearch);
  }, [query, searchPlayers, minQueryLength, onSearchChange]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || results.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev < results.length - 1 ? prev + 1 : 0));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : results.length - 1));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < results.length) {
          handlePlayerSelect(results[selectedIndex].player);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle player selection
  const handlePlayerSelect = (player: Player) => {
    if (onPlayerSelect) {
      onPlayerSelect(player);
    }
    
    if (clearOnSelect) {
      setQuery('');
      setResults([]);
    }
    
    setIsOpen(false);
    setSelectedIndex(-1);
  };

  // Handle captain selection
  const handleCaptainSelect = (player: Player) => {
    if (onCaptainSelect) {
      onCaptainSelect(player);
    }
    
    setIsOpen(false);
    setSelectedIndex(-1);
  };

  // Clear search
  const clearSearch = () => {
    setQuery('');
    setResults([]);
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Format currency
  const formatCurrency = (amount: number | undefined | null) => {
    if (!amount || amount === 0) return '$0.00M';
    return `$${(amount / 1000000).toFixed(2)}M`;
  };

  // Get position color
  const getPositionColor = (position: string) => {
    const colors: { [key: string]: string } = {
      'FLB': 'bg-blue-500/20 text-blue-400',
      'CTW': 'bg-green-500/20 text-green-400',
      'HFB': 'bg-purple-500/20 text-purple-400',
      '5/8': 'bg-purple-500/20 text-purple-400',
      'HOK': 'bg-orange-500/20 text-orange-400',
      'FRF': 'bg-red-500/20 text-red-400',
      '2RF': 'bg-yellow-500/20 text-yellow-400',
      'LCK': 'bg-pink-500/20 text-pink-400'
    };
    return colors[position] || 'bg-gray-500/20 text-gray-400';
  };

  return (
    <div ref={searchRef} className={`relative search-container dropdown-container ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 themed-text-tertiary" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (results.length > 0) setIsOpen(true);
          }}
          placeholder={placeholder}
          disabled={disabled}
          className={`input-primary w-full pl-10 pr-10 ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        />

        {query && (
          <button
            type="button"
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            title="Clear search"
          >
            <XMarkIcon className="h-4 w-4 themed-text-tertiary hover:themed-text-primary transition-colors" />
          </button>
        )}

        {isLoading && (
          <div className="absolute inset-y-0 right-8 pr-3 flex items-center">
            <div className="loading-spinner w-4 h-4"></div>
          </div>
        )}
      </div>

      {/* Predictive Results Dropdown */}
      <PortalDropdown
        isOpen={isOpen && (results.length > 0 || isLoading)}
        targetRef={searchRef}
        className="search-dropdown dropdown-content themed-bg-secondary themed-border rounded-lg themed-shadow-xl overflow-hidden"
      >
        <AnimatePresence>
          {isOpen && (results.length > 0 || isLoading) && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.15 }}
            >
            {isLoading ? (
              <div className="p-4 text-center themed-text-tertiary">
                <div className="loading-spinner w-5 h-5 mx-auto mb-2"></div>
                Searching players...
              </div>
            ) : results.length > 0 ? (
              <div className="max-h-80 overflow-y-auto">
                {results.map((result, index) => (
                  <motion.div
                    key={result.player.id}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.1, delay: index * 0.03 }}
                    className={`p-3 cursor-pointer transition-all duration-150 ${
                      index === selectedIndex 
                        ? 'themed-bg-tertiary border-l-4 border-blue-400' 
                        : 'hover:themed-bg-tertiary'
                    } ${index < results.length - 1 ? 'border-b themed-border' : ''}`}
                    onClick={() => handlePlayerSelect(result.player)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <div 
                            className="font-medium themed-text-primary"
                            dangerouslySetInnerHTML={{ __html: result.highlightedName || result.player.name }}
                          />
                          <span className={`px-2 py-1 rounded text-xs font-medium ${getPositionColor(result.player.position)}`}>
                            {result.player.position}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-3 text-sm themed-text-tertiary">
                          <span>{result.player.team}</span>
                          {showPlayerDetails && (
                            <>
                              <span>•</span>
                              <span>Avg: {(result.player.average || 0).toFixed(1)}</span>
                              <span>•</span>
                              <span>{formatCurrency(result.player.price)}</span>
                            </>
                          )}
                        </div>
                      </div>

                      {showCaptainOption && (
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCaptainSelect(result.player);
                          }}
                          className="ml-3 p-2 rounded-lg bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30 transition-colors"
                          title="Set as Captain"
                        >
                          <TrophyIcon className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center themed-text-tertiary">
                <UserIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
                No players found for "{query}"
              </div>
            )}
            </motion.div>
          )}
        </AnimatePresence>
      </PortalDropdown>
    </div>
  );
};

export default PredictiveSearch;
