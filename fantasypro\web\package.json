{"name": "fantasypro-web", "version": "1.0.0", "description": "FantasyPro - AI-Integrated Fantasy Sports Platform Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "analyze": "ANALYZE=true npm run build", "lighthouse": "lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html", "perf": "npm run build && npm run analyze"}, "dependencies": {"@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "@hookform/resolvers": "^3.3.0", "@radix-ui/react-dialog": "^1.0.0", "@radix-ui/react-dropdown-menu": "^2.0.0", "@radix-ui/react-progress": "^1.0.0", "@radix-ui/react-slider": "^1.1.0", "@radix-ui/react-switch": "^1.0.0", "@radix-ui/react-tabs": "^1.0.0", "@radix-ui/react-tooltip": "^1.0.0", "@supabase/supabase-js": "^2.50.2", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0", "@tanstack/react-query": "^5.0.0", "@tanstack/react-table": "^8.10.0", "@types/bcryptjs": "^2.4.0", "@types/d3": "^7.4.0", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-virtualized": "^9.21.0", "@types/react-window": "^1.8.0", "autoprefixer": "^10.4.0", "axios": "^1.5.0", "bcryptjs": "^2.4.0", "chart.js": "^4.4.0", "cheerio": "^1.0.0-rc.12", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "d3": "^7.8.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.0", "jose": "^5.0.0", "lottie-react": "^2.4.0", "lucide-react": "^0.288.0", "next": "^14.0.0", "next-auth": "^4.24.0", "next-pwa": "^5.6.0", "postcss": "^8.4.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-datepicker": "^4.21.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.0", "react-intersection-observer": "^9.5.0", "react-loading-skeleton": "^3.3.0", "react-query": "^3.39.0", "react-select": "^5.7.0", "react-spring": "^9.7.0", "react-table": "^7.8.0", "react-virtualized": "^9.22.0", "react-window": "^1.8.0", "recharts": "^2.8.0", "socket.io-client": "^4.7.0", "swr": "^2.2.0", "tailwind-merge": "^1.14.0", "tailwindcss": "^3.3.0", "typescript": "^5.2.0", "workbox-webpack-plugin": "^7.0.0", "zod": "^3.22.0"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.0", "@storybook/addon-essentials": "^7.5.0", "@storybook/addon-interactions": "^7.5.0", "@storybook/addon-links": "^7.5.0", "@storybook/blocks": "^7.5.0", "@storybook/nextjs": "^7.5.0", "@storybook/react": "^7.5.0", "@storybook/testing-library": "^0.2.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.0", "@types/cheerio": "^0.22.35", "@types/jest": "^29.5.0", "@types/testing-library__jest-dom": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "commitizen": "^4.3.0", "cross-env": "^7.0.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-jsx-a11y": "^6.7.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "storybook": "^7.5.0", "webpack-bundle-analyzer": "^4.9.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1", "^@/components/(.*)$": "<rootDir>/src/components/$1", "^@/pages/(.*)$": "<rootDir>/src/pages/$1", "^@/utils/(.*)$": "<rootDir>/src/utils/$1", "^@/hooks/(.*)$": "<rootDir>/src/hooks/$1", "^@/types/(.*)$": "<rootDir>/src/types/$1", "^@/lib/(.*)$": "<rootDir>/src/lib/$1"}, "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/**/*.stories.{js,jsx,ts,tsx}", "!src/pages/_app.tsx", "!src/pages/_document.tsx"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}, "keywords": ["fantasy-sports", "nrl", "supercoach", "ai", "machine-learning", "react", "nextjs", "typescript", "tailwindcss"], "author": "FantasyPro Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/fantasypro.git"}, "bugs": {"url": "https://github.com/your-org/fantasypro/issues"}, "homepage": "https://fantasypro.ai"}