import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../services/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Testing direct Supabase connection...');
    
    // Test 1: Basic connection
    console.log('Test 1: Basic connection test...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('nrl_players')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      console.error('❌ Connection test failed:', connectionError);
      return res.status(500).json({
        success: false,
        test: 'connection',
        error: connectionError.message,
        timestamp: new Date().toISOString()
      });
    }
    
    console.log('✅ Connection test passed');
    
    // Test 2: Count all players
    console.log('Test 2: Counting all players...');
    const { count: totalCount, error: countError } = await supabase
      .from('nrl_players')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('❌ Count test failed:', countError);
      return res.status(500).json({
        success: false,
        test: 'count',
        error: countError.message,
        timestamp: new Date().toISOString()
      });
    }
    
    console.log(`✅ Count test passed: ${totalCount} players`);
    
    // Test 3: Get sample players
    console.log('Test 3: Getting sample players...');
    const { data: samplePlayers, error: sampleError } = await supabase
      .from('nrl_players')
      .select('*')
      .limit(5)
      .order('name');
    
    if (sampleError) {
      console.error('❌ Sample test failed:', sampleError);
      return res.status(500).json({
        success: false,
        test: 'sample',
        error: sampleError.message,
        timestamp: new Date().toISOString()
      });
    }
    
    console.log(`✅ Sample test passed: ${samplePlayers?.length || 0} players retrieved`);
    
    // Test 4: Transform sample data
    console.log('Test 4: Testing data transformation...');
    const transformedSample = samplePlayers?.map(player => {
      const stats = player.statistics || {};
      const totalPoints = stats.total_points || 0;
      const gamesPlayed = stats.games_played || 1;
      const averagePoints = gamesPlayed > 0 ? totalPoints / gamesPlayed : 0;
      
      return {
        id: player.id.toString(),
        name: player.name,
        position: player.position || 'Unknown',
        team: player.team_name || 'Unknown Team',
        price: stats.price || 300000,
        points: totalPoints,
        average: averagePoints,
        form: stats.form || (averagePoints / 10),
        ownership: stats.ownership || 0,
        breakeven: stats.breakeven || 0,
        created_at: player.created_at,
        updated_at: player.updated_at
      };
    }) || [];
    
    console.log(`✅ Transformation test passed: ${transformedSample.length} players transformed`);
    
    // Test 5: Check required fields
    console.log('Test 5: Checking required fields...');
    const requiredFields = ['id', 'name', 'team', 'position', 'price', 'points', 'average', 'form'];
    const samplePlayer = transformedSample[0];
    const missingFields = requiredFields.filter(field => !(field in samplePlayer));
    
    console.log(`✅ Field check: ${missingFields.length === 0 ? 'All required fields present' : `Missing: ${missingFields.join(', ')}`}`);
    
    const result = {
      success: true,
      tests: {
        connection: { passed: true, message: 'Supabase connection successful' },
        count: { passed: true, total_players: totalCount, message: `Found ${totalCount} players` },
        sample: { passed: true, sample_count: samplePlayers?.length || 0, message: 'Sample data retrieved' },
        transformation: { passed: true, transformed_count: transformedSample.length, message: 'Data transformation successful' },
        fields: { passed: missingFields.length === 0, missing_fields: missingFields, message: missingFields.length === 0 ? 'All required fields present' : `Missing fields: ${missingFields.join(', ')}` }
      },
      data: {
        total_players: totalCount,
        sample_players: transformedSample.slice(0, 3),
        data_structure: samplePlayer ? Object.keys(samplePlayer) : [],
        expected_count: 581,
        data_complete: (totalCount || 0) >= 500
      },
      analysis: {
        ready_for_ui: (totalCount || 0) >= 500 && missingFields.length === 0,
        issues: [
          ...(totalCount && totalCount < 500 ? ['Player count below expected 581'] : []),
          ...(missingFields.length > 0 ? [`Missing required fields: ${missingFields.join(', ')}`] : [])
        ]
      },
      timestamp: new Date().toISOString()
    };
    
    console.log('🎉 All Supabase tests completed successfully');
    console.log(`   - Total players: ${totalCount}`);
    console.log(`   - Data complete: ${result.analysis.ready_for_ui}`);
    console.log(`   - Issues: ${result.analysis.issues.length === 0 ? 'None' : result.analysis.issues.join(', ')}`);
    
    return res.status(200).json(result);
    
  } catch (error) {
    console.error('❌ Supabase direct test failed:', error);
    return res.status(500).json({
      success: false,
      error: 'Direct Supabase test failed',
      details: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
  }
}
