import type { AppProps } from 'next/app';
import '../styles/globals.css';
import '../styles/dropdown-fix.css';
import { ThemeProvider } from '../contexts/ThemeContext';
import ErrorBoundary from '../components/ErrorBoundary';

export default function App({ Component, pageProps }: AppProps) {
  return (
    <ErrorBoundary>
      <ThemeProvider defaultTheme="dark" storageKey="fantasypro-theme">
        <Component {...pageProps} />
      </ThemeProvider>
    </ErrorBoundary>
  );
}
