"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/my-team",{

/***/ "./src/pages/my-team.tsx":
/*!*******************************!*\
  !*** ./src/pages/my-team.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"./node_modules/next/node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"./node_modules/next/node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"./node_modules/next/node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"./node_modules/next/node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"./node_modules/next/node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"./node_modules/next/node_modules/@swc/helpers/esm/_ts_generator.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var _components_PredictiveSearch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/PredictiveSearch */ \"./src/components/PredictiveSearch.tsx\");\n/* harmony import */ var _components_TradeAnalysis__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/TradeAnalysis */ \"./src/components/TradeAnalysis.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,ChartBarIcon,CurrencyDollarIcon,FireIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowTrendingUpIcon,ChartBarIcon,CurrencyDollarIcon,FireIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,StarIcon,TrashIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar MyTeam = function() {\n    var _squadData_captain_form_rating, _squadData_vice_captain_form_rating;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), squadData = _useState[0], setSquadData = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true), 2), loading = _useState1[0], setLoading = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), selectedPlayer = _useState2[0], setSelectedPlayer = _useState2[1];\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), showAddPlayerModal = _useState3[0], setShowAddPlayerModal = _useState3[1];\n    var _useState4 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), showEditPlayerModal = _useState4[0], setShowEditPlayerModal = _useState4[1];\n    var _useState5 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), showTradeAnalysis = _useState5[0], setShowTradeAnalysis = _useState5[1];\n    var _useState6 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), showCaptainModal = _useState6[0], setShowCaptainModal = _useState6[1];\n    var _useState7 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), editingPlayer = _useState7[0], setEditingPlayer = _useState7[1];\n    var _useState8 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), tradePlayer = _useState8[0], setTradePlayer = _useState8[1];\n    var _useState9 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        team: \"\",\n        position: \"\",\n        current_price: 0,\n        season_points: 0,\n        season_average: 0,\n        form_rating: 0,\n        is_playing: true\n    }), 2), newPlayer = _useState9[0], setNewPlayer = _useState9[1];\n    // Manual squad management functions\n    var addPlayer = function() {\n        if (!squadData || !newPlayer.name || !newPlayer.team || !newPlayer.position) return;\n        var _newPlayer_is_playing;\n        var player = {\n            id: Date.now(),\n            name: newPlayer.name,\n            team: newPlayer.team,\n            position: newPlayer.position,\n            current_price: newPlayer.current_price || 0,\n            season_points: newPlayer.season_points || 0,\n            season_average: newPlayer.season_average || 0,\n            form_rating: newPlayer.form_rating || 0,\n            is_playing: (_newPlayer_is_playing = newPlayer.is_playing) !== null && _newPlayer_is_playing !== void 0 ? _newPlayer_is_playing : true,\n            recent_scores: []\n        };\n        var updatedSquadData = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, squadData), {\n            players: (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(squadData.players).concat([\n                player\n            ]),\n            total_value: squadData.total_value + player.current_price,\n            remaining_budget: squadData.remaining_budget - player.current_price\n        });\n        setSquadData(updatedSquadData);\n        setNewPlayer({\n            name: \"\",\n            team: \"\",\n            position: \"\",\n            current_price: 0,\n            season_points: 0,\n            season_average: 0,\n            form_rating: 0,\n            is_playing: true\n        });\n        setShowAddPlayerModal(false);\n    };\n    var editPlayer = function(player) {\n        setEditingPlayer(player);\n        setNewPlayer(player);\n        setShowEditPlayerModal(true);\n    };\n    var updatePlayer = function() {\n        if (!squadData || !editingPlayer || !newPlayer.name) return;\n        var updatedPlayers = squadData.players.map(function(p) {\n            return p.id === editingPlayer.id ? (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, p, newPlayer) : p;\n        });\n        var updatedSquadData = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, squadData), {\n            players: updatedPlayers\n        });\n        // Recalculate totals\n        var totalValue = updatedPlayers.reduce(function(sum, p) {\n            return sum + p.current_price;\n        }, 0);\n        updatedSquadData.total_value = totalValue;\n        updatedSquadData.remaining_budget = squadData.salary_cap - totalValue;\n        setSquadData(updatedSquadData);\n        setShowEditPlayerModal(false);\n        setEditingPlayer(null);\n        setNewPlayer({\n            name: \"\",\n            team: \"\",\n            position: \"\",\n            current_price: 0,\n            season_points: 0,\n            season_average: 0,\n            form_rating: 0,\n            is_playing: true\n        });\n    };\n    var removePlayer = function(playerId) {\n        if (!squadData) return;\n        var playerToRemove = squadData.players.find(function(p) {\n            return p.id === playerId;\n        });\n        if (!playerToRemove) return;\n        var updatedPlayers = squadData.players.filter(function(p) {\n            return p.id !== playerId;\n        });\n        var updatedSquadData = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, squadData), {\n            players: updatedPlayers,\n            total_value: squadData.total_value - playerToRemove.current_price,\n            remaining_budget: squadData.remaining_budget + playerToRemove.current_price\n        });\n        setSquadData(updatedSquadData);\n    };\n    var setCaptain = function(playerId) {\n        if (!squadData) return;\n        var updatedPlayers = squadData.players.map(function(p) {\n            return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, p), {\n                is_captain: p.id === playerId,\n                is_vice_captain: p.is_captain && p.id !== playerId ? false : p.is_vice_captain\n            });\n        });\n        setSquadData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, squadData), {\n            players: updatedPlayers,\n            captain: updatedPlayers.find(function(p) {\n                return p.is_captain;\n            })\n        }));\n    };\n    var setViceCaptain = function(playerId) {\n        if (!squadData) return;\n        var updatedPlayers = squadData.players.map(function(p) {\n            return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, p), {\n                is_vice_captain: p.id === playerId,\n                is_captain: p.is_vice_captain && p.id !== playerId ? false : p.is_captain\n            });\n        });\n        setSquadData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, squadData), {\n            players: updatedPlayers,\n            vice_captain: updatedPlayers.find(function(p) {\n                return p.is_vice_captain;\n            })\n        }));\n    };\n    // Enhanced handler functions\n    var handlePlayerSelect = function(player) {\n        setSelectedPlayer(player);\n        console.log(\"Player selected:\", player);\n    };\n    var handleTradePlayer = function(player) {\n        setTradePlayer(player);\n        setShowTradeAnalysis(true);\n    };\n    var handleExecuteTrade = function(tradeData) {\n        console.log(\"Executing trade:\", tradeData);\n        // Add actual trade execution logic here\n        alert(\"Trade executed successfully!\");\n        setShowTradeAnalysis(false);\n        setTradePlayer(null);\n    };\n    var handleImportFromSuperCoach = function() {\n        alert(\"Import from SuperCoach functionality coming soon!\");\n    };\n    var togglePlayerStatus = function(playerId) {\n        if (!squadData) return;\n        var updatedPlayers = squadData.players.map(function(p) {\n            return p.id === playerId ? (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, p), {\n                is_playing: !p.is_playing\n            }) : p;\n        });\n        setSquadData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, squadData), {\n            players: updatedPlayers,\n            playing_players: updatedPlayers.filter(function(p) {\n                return p.is_playing;\n            }),\n            bench_players: updatedPlayers.filter(function(p) {\n                return !p.is_playing;\n            })\n        }));\n    };\n    // Initialize with sample Sousside Rattlers data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var initializeSquadData = function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_10__._)(function() {\n                var initialSquadData;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_11__._)(this, function(_state) {\n                    try {\n                        // Initialize with sample Sousside Rattlers data (users can modify manually)\n                        initialSquadData = {\n                            total_value: 11766600,\n                            salary_cap: 11800000,\n                            remaining_budget: 33400,\n                            total_points: 1079,\n                            round_score: 1079,\n                            trades_remaining: 9,\n                            players: [\n                                // Starting/Bench players from screenshots\n                                {\n                                    id: 1,\n                                    name: \"Herbie Farnworth\",\n                                    team: \"Dolphins\",\n                                    position: \"CTW\",\n                                    current_price: 815400,\n                                    season_points: 1161,\n                                    season_average: 82.9,\n                                    form_rating: 8.7,\n                                    is_captain: true,\n                                    is_playing: true,\n                                    recent_scores: [\n                                        150,\n                                        108,\n                                        95,\n                                        87,\n                                        82\n                                    ]\n                                },\n                                {\n                                    id: 2,\n                                    name: \"James Tedesco\",\n                                    team: \"Sydney Roosters\",\n                                    position: \"FLB\",\n                                    current_price: 817700,\n                                    season_points: 1144,\n                                    season_average: 88.0,\n                                    form_rating: 8.5,\n                                    is_vice_captain: true,\n                                    is_playing: true,\n                                    recent_scores: [\n                                        97,\n                                        85,\n                                        78,\n                                        92,\n                                        88\n                                    ]\n                                },\n                                {\n                                    id: 3,\n                                    name: \"Blayke Brailey\",\n                                    team: \"Cronulla Sharks\",\n                                    position: \"HOK\",\n                                    current_price: 627300,\n                                    season_points: 813,\n                                    season_average: 58.1,\n                                    form_rating: 6.6,\n                                    is_playing: true,\n                                    recent_scores: [\n                                        102,\n                                        66,\n                                        45,\n                                        58,\n                                        72\n                                    ]\n                                },\n                                {\n                                    id: 4,\n                                    name: \"Briton Nikora\",\n                                    team: \"Cronulla Sharks\",\n                                    position: \"2RF\",\n                                    current_price: 531700,\n                                    season_points: 842,\n                                    season_average: 60.1,\n                                    form_rating: 7.2,\n                                    is_playing: true,\n                                    recent_scores: [\n                                        108,\n                                        72,\n                                        55,\n                                        68,\n                                        61\n                                    ]\n                                },\n                                {\n                                    id: 5,\n                                    name: \"Reuben Garrick\",\n                                    team: \"Manly Sea Eagles\",\n                                    position: \"CTW\",\n                                    current_price: 621500,\n                                    season_points: 1023,\n                                    season_average: 73.1,\n                                    form_rating: 8.1,\n                                    is_playing: true,\n                                    recent_scores: [\n                                        93,\n                                        81,\n                                        76,\n                                        85,\n                                        73\n                                    ]\n                                },\n                                {\n                                    id: 6,\n                                    name: \"Jayden Campbell\",\n                                    team: \"Gold Coast Titans\",\n                                    position: \"5/8\",\n                                    current_price: 643800,\n                                    season_points: 713,\n                                    season_average: 71.3,\n                                    form_rating: 6.9,\n                                    is_playing: true,\n                                    recent_scores: [\n                                        87,\n                                        69,\n                                        58,\n                                        75,\n                                        71\n                                    ]\n                                },\n                                {\n                                    id: 7,\n                                    name: \"Keaon Koloamatangi\",\n                                    team: \"South Sydney Rabbitohs\",\n                                    position: \"2RF\",\n                                    current_price: 795300,\n                                    season_points: 987,\n                                    season_average: 70.5,\n                                    form_rating: 10.0,\n                                    is_playing: true,\n                                    recent_scores: [\n                                        73,\n                                        100,\n                                        95,\n                                        88,\n                                        92\n                                    ]\n                                },\n                                {\n                                    id: 8,\n                                    name: \"Scott Drinkwater\",\n                                    team: \"North Queensland Cowboys\",\n                                    position: \"FLB\",\n                                    current_price: 708100,\n                                    season_points: 1016,\n                                    season_average: 78.2,\n                                    form_rating: 6.9,\n                                    is_playing: true,\n                                    recent_scores: [\n                                        33,\n                                        69,\n                                        82,\n                                        95,\n                                        78\n                                    ]\n                                },\n                                // Additional bench players\n                                {\n                                    id: 9,\n                                    name: \"Terrell May\",\n                                    team: \"Wests Tigers\",\n                                    position: \"FRF\",\n                                    current_price: 832800,\n                                    season_points: 1134,\n                                    season_average: 87.2,\n                                    form_rating: 9.4,\n                                    is_playing: false,\n                                    recent_scores: [\n                                        0,\n                                        93,\n                                        88,\n                                        95,\n                                        87\n                                    ]\n                                },\n                                {\n                                    id: 10,\n                                    name: \"Fletcher Sharpe\",\n                                    team: \"Dolphins\",\n                                    position: \"CTW\",\n                                    current_price: 754200,\n                                    season_points: 994,\n                                    season_average: 76.5,\n                                    form_rating: 9.8,\n                                    is_playing: false,\n                                    recent_scores: [\n                                        0,\n                                        98,\n                                        92,\n                                        85,\n                                        76\n                                    ]\n                                },\n                                {\n                                    id: 11,\n                                    name: \"Dylan Lucas\",\n                                    team: \"Dolphins\",\n                                    position: \"CTW\",\n                                    current_price: 715600,\n                                    season_points: 811,\n                                    season_average: 81.1,\n                                    form_rating: 6.8,\n                                    is_playing: true,\n                                    recent_scores: [\n                                        82,\n                                        68,\n                                        75,\n                                        81,\n                                        85\n                                    ]\n                                },\n                                {\n                                    id: 12,\n                                    name: \"Nicholas Hynes\",\n                                    team: \"Cronulla Sharks\",\n                                    position: \"HFB\",\n                                    current_price: 619000,\n                                    season_points: 969,\n                                    season_average: 69.2,\n                                    form_rating: 5.8,\n                                    is_playing: true,\n                                    recent_scores: [\n                                        57,\n                                        58,\n                                        72,\n                                        69,\n                                        65\n                                    ]\n                                },\n                                {\n                                    id: 13,\n                                    name: \"Beau Fermor\",\n                                    team: \"Gold Coast Titans\",\n                                    position: \"2RF\",\n                                    current_price: 588700,\n                                    season_points: 855,\n                                    season_average: 65.8,\n                                    form_rating: 6.0,\n                                    is_playing: true,\n                                    recent_scores: [\n                                        67,\n                                        60,\n                                        68,\n                                        66,\n                                        72\n                                    ]\n                                },\n                                {\n                                    id: 14,\n                                    name: \"Ryan Papenhuyzen\",\n                                    team: \"Melbourne Storm\",\n                                    position: \"FLB\",\n                                    current_price: 760900,\n                                    season_points: 1045,\n                                    season_average: 87.1,\n                                    form_rating: 5.8,\n                                    is_playing: false,\n                                    recent_scores: [\n                                        0,\n                                        57,\n                                        85,\n                                        92,\n                                        87\n                                    ]\n                                },\n                                {\n                                    id: 15,\n                                    name: \"Lyhkan King-Togia\",\n                                    team: \"St George Illawarra Dragons\",\n                                    position: \"HFB\",\n                                    current_price: 329000,\n                                    season_points: 289,\n                                    season_average: 48.2,\n                                    form_rating: 5.0,\n                                    is_playing: true,\n                                    recent_scores: [\n                                        60,\n                                        50,\n                                        45,\n                                        48,\n                                        52\n                                    ]\n                                },\n                                {\n                                    id: 16,\n                                    name: \"Tino Fa'asuamaleaui\",\n                                    team: \"Gold Coast Titans\",\n                                    position: \"2RF\",\n                                    current_price: 699900,\n                                    season_points: 849,\n                                    season_average: 77.2,\n                                    form_rating: 6.3,\n                                    is_playing: false,\n                                    recent_scores: [\n                                        0,\n                                        63,\n                                        77,\n                                        82,\n                                        75\n                                    ]\n                                },\n                                {\n                                    id: 17,\n                                    name: \"Sandon Smith\",\n                                    team: \"Sydney Roosters\",\n                                    position: \"HOK\",\n                                    current_price: 499600,\n                                    season_points: 706,\n                                    season_average: 54.3,\n                                    form_rating: 5.3,\n                                    is_playing: false,\n                                    recent_scores: [\n                                        29,\n                                        53,\n                                        58,\n                                        54,\n                                        61\n                                    ]\n                                }\n                            ],\n                            captain: undefined,\n                            vice_captain: undefined,\n                            bench_players: [],\n                            playing_players: []\n                        };\n                        // Set captain and vice captain\n                        initialSquadData.captain = initialSquadData.players.find(function(p) {\n                            return p.is_captain;\n                        });\n                        initialSquadData.vice_captain = initialSquadData.players.find(function(p) {\n                            return p.is_vice_captain;\n                        });\n                        initialSquadData.playing_players = initialSquadData.players.filter(function(p) {\n                            return p.is_playing;\n                        });\n                        initialSquadData.bench_players = initialSquadData.players.filter(function(p) {\n                            return !p.is_playing;\n                        });\n                        setSquadData(initialSquadData);\n                    } catch (error) {\n                        console.error(\"Error fetching squad data:\", error);\n                    } finally{\n                        setLoading(false);\n                    }\n                    return [\n                        2\n                    ];\n                });\n            });\n            return function initializeSquadData() {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        initializeSquadData();\n    }, []);\n    var formatCurrency = function(amount) {\n        return \"$\".concat((amount / 1000000).toFixed(2), \"M\");\n    };\n    var getBreakevenColor = function(breakeven) {\n        if (!breakeven) return \"text-slate-400\";\n        return breakeven < 0 ? \"text-green-400\" : \"text-red-400\";\n    };\n    var getFormRatingColor = function(rating) {\n        if (!rating) return \"text-slate-400\";\n        if (rating >= 8) return \"text-green-400\";\n        if (rating >= 6) return \"text-yellow-400\";\n        return \"text-red-400\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"loading-spinner w-8 h-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 11\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                lineNumber: 511,\n                columnNumber: 9\n            }, _this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n            lineNumber: 510,\n            columnNumber: 7\n        }, _this);\n    }\n    if (!squadData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-400 text-xl\",\n                    children: \"Error loading squad data\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                    lineNumber: 522,\n                    columnNumber: 11\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                lineNumber: 521,\n                columnNumber: 9\n            }, _this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n            lineNumber: 520,\n            columnNumber: 7\n        }, _this);\n    }\n    var _newPlayer_is_playing, _newPlayer_is_playing1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"My Team - FantasyPro\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Manage your NRL SuperCoach team with AI-powered insights\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                lineNumber: 530,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-white\",\n                                        children: \"My Team\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-400 mt-1\",\n                                        children: \"Manage your NRL SuperCoach team with AI-powered insights\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"btn-primary btn-ripple\",\n                                        onClick: function() {\n                                            return setShowAddPlayerModal(true);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 15\n                                            }, _this),\n                                            \"Add Player\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"btn-secondary btn-ripple\",\n                                        onClick: handleImportFromSuperCoach,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 15\n                                            }, _this),\n                                            \"Import from SuperCoach\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"btn-accent btn-ripple\",\n                                        onClick: function() {\n                                            return setShowCaptainModal(true);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.StarIcon, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 15\n                                            }, _this),\n                                            \"Set Captain\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        className: \"card-premium p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.MagnifyingGlassIcon, {\n                                        className: \"w-6 h-6 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold themed-text-primary\",\n                                        children: \"Add Players to Team\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"status-live\",\n                                        children: \"581 Players\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium themed-text-secondary mb-2\",\n                                                children: \"\\uD83D\\uDD0D Search & Add Players\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PredictiveSearch__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                placeholder: \"Type 'Na' for Nathan Cleary, 'Re' for Reece Walsh...\",\n                                                onPlayerSelect: handlePlayerSelect,\n                                                showPlayerDetails: true,\n                                                maxResults: 8,\n                                                minQueryLength: 1,\n                                                clearOnSelect: false\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium themed-text-secondary mb-2\",\n                                                children: \"\\uD83D\\uDC51 Quick Captain Selection\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PredictiveSearch__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                placeholder: \"Search for captain...\",\n                                                showCaptainOption: true,\n                                                onCaptainSelect: function(player) {\n                                                    return setCaptain(player.id);\n                                                },\n                                                onPlayerSelect: handlePlayerSelect,\n                                                showPlayerDetails: true,\n                                                maxResults: 6,\n                                                minQueryLength: 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 11\n                            }, _this),\n                            selectedPlayer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: \"auto\"\n                                },\n                                className: \"mt-6 pt-6 border-t themed-border\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold themed-text-primary\",\n                                                    children: [\n                                                        \"Selected: \",\n                                                        selectedPlayer.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm themed-text-tertiary\",\n                                                    children: [\n                                                        selectedPlayer.position,\n                                                        \" - \",\n                                                        selectedPlayer.team\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: function() {\n                                                        // Add player to team logic\n                                                        var newPlayer = {\n                                                            id: Date.now(),\n                                                            name: selectedPlayer.name,\n                                                            team: selectedPlayer.team,\n                                                            position: selectedPlayer.position,\n                                                            current_price: selectedPlayer.price || 400000,\n                                                            season_points: selectedPlayer.points || 0,\n                                                            season_average: selectedPlayer.average || 0,\n                                                            form_rating: selectedPlayer.form || 5,\n                                                            is_playing: true,\n                                                            recent_scores: []\n                                                        };\n                                                        if (squadData) {\n                                                            setSquadData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, squadData), {\n                                                                players: (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_9__._)(squadData.players).concat([\n                                                                    newPlayer\n                                                                ]),\n                                                                total_value: squadData.total_value + newPlayer.current_price,\n                                                                remaining_budget: squadData.remaining_budget - newPlayer.current_price\n                                                            }));\n                                                        }\n                                                        setSelectedPlayer(null);\n                                                    },\n                                                    className: \"btn-primary btn-ripple flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Add to Team\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 21\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: function() {\n                                                        return setCaptain(selectedPlayer.id);\n                                                    },\n                                                    className: \"btn-secondary btn-ripple flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.StarIcon, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Set Captain\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 21\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 612,\n                                columnNumber: 13\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                className: \"card p-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.CurrencyDollarIcon, {\n                                                        className: \"w-5 h-5 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-slate-400\",\n                                                        children: \"Squad Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-400\",\n                                                children: [\n                                                    (squadData.remaining_budget / squadData.salary_cap * 100).toFixed(1),\n                                                    \"% left\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white mb-1\",\n                                        children: formatCurrency(squadData.total_value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-slate-400\",\n                                        children: [\n                                            formatCurrency(squadData.remaining_budget),\n                                            \" remaining\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                className: \"card p-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.TrophyIcon, {\n                                                        className: \"w-5 h-5 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-slate-400\",\n                                                        children: \"Total Points\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-blue-400\",\n                                                children: \"Season\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white mb-1\",\n                                        children: squadData.total_points.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-slate-400\",\n                                        children: [\n                                            \"Round: \",\n                                            squadData.round_score,\n                                            \" pts\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                className: \"card p-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.ArrowTrendingUpIcon, {\n                                                        className: \"w-5 h-5 text-orange-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-slate-400\",\n                                                        children: \"Trades Left\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-orange-400\",\n                                                children: \"This round\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 724,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white mb-1\",\n                                        children: squadData.trades_remaining\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-slate-400\",\n                                        children: \"Use wisely\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                className: \"card p-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.UserGroupIcon, {\n                                                        className: \"w-5 h-5 text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-slate-400\",\n                                                        children: \"Squad Size\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-purple-400\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white mb-1\",\n                                        children: [\n                                            squadData.playing_players.length,\n                                            \"/17\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 747,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-slate-400\",\n                                        children: [\n                                            squadData.bench_players.length,\n                                            \" on bench\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                className: \"card\",\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.4\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-slate-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.FireIcon, {\n                                                    className: \"w-5 h-5 text-orange-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Captain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-orange-400\",\n                                                    children: \"2x Points\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 765,\n                                            columnNumber: 15\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: squadData.captain ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-orange-500/20 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-orange-400 font-bold\",\n                                                                children: \"C\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-white\",\n                                                                    children: squadData.captain.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 23\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: [\n                                                                        squadData.captain.team,\n                                                                        \" • \",\n                                                                        squadData.captain.position\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 23\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: [\n                                                                        \"Form: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: getFormRatingColor(squadData.captain.form_rating),\n                                                                            children: [\n                                                                                ((_squadData_captain_form_rating = squadData.captain.form_rating) === null || _squadData_captain_form_rating === void 0 ? void 0 : _squadData_captain_form_rating.toFixed(1)) || \"N/A\",\n                                                                                \"/10\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                            lineNumber: 782,\n                                                                            columnNumber: 31\n                                                                        }, _this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                    lineNumber: 781,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 21\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"btn-outline text-sm\",\n                                                    children: \"Change\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 17\n                                        }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-slate-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: \"No captain selected\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"btn-primary mt-2\",\n                                                    children: \"Select Captain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 17\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 758,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                className: \"card\",\n                                initial: {\n                                    opacity: 0,\n                                    x: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.5\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-slate-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.ChartBarIcon, {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 807,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Vice Captain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-blue-400\",\n                                                    children: \"Backup\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 15\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 805,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: squadData.vice_captain ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-blue-400 font-bold\",\n                                                                children: \"VC\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-white\",\n                                                                    children: squadData.vice_captain.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                    lineNumber: 820,\n                                                                    columnNumber: 23\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-slate-400\",\n                                                                    children: [\n                                                                        squadData.vice_captain.team,\n                                                                        \" • \",\n                                                                        squadData.vice_captain.position\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 23\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: [\n                                                                        \"Form: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: getFormRatingColor(squadData.vice_captain.form_rating),\n                                                                            children: [\n                                                                                ((_squadData_vice_captain_form_rating = squadData.vice_captain.form_rating) === null || _squadData_vice_captain_form_rating === void 0 ? void 0 : _squadData_vice_captain_form_rating.toFixed(1)) || \"N/A\",\n                                                                                \"/10\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                            lineNumber: 823,\n                                                                            columnNumber: 31\n                                                                        }, _this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                    lineNumber: 822,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 21\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"btn-outline text-sm\",\n                                                    children: \"Change\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 829,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 17\n                                        }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8 text-slate-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: \"No vice captain selected\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"btn-primary mt-2\",\n                                                    children: \"Select Vice Captain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 832,\n                                            columnNumber: 17\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 812,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 799,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                        lineNumber: 757,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"card\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.7\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-slate-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.UserGroupIcon, {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 851,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Squad Players\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 852,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-blue-400\",\n                                                    children: [\n                                                        squadData.players.length,\n                                                        \"/26 players\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 850,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-slate-400\",\n                                            children: \"Click players to set as captain/vice captain\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 857,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                    lineNumber: 849,\n                                    columnNumber: 13\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 848,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        squadData.players.map(function(player, index) {\n                                            var _player_season_average, _player_form_rating;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                className: \"flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600 hover:border-slate-500 transition-colors\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.3,\n                                                    delay: index * 0.05\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col items-center space-y-1\",\n                                                                children: [\n                                                                    player.is_captain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6 bg-orange-500/20 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-orange-400 text-xs font-bold\",\n                                                                            children: \"C\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                            lineNumber: 877,\n                                                                            columnNumber: 27\n                                                                        }, _this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 876,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    player.is_vice_captain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-400 text-xs font-bold\",\n                                                                            children: \"VC\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                            lineNumber: 882,\n                                                                            columnNumber: 27\n                                                                        }, _this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 881,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    !player.is_captain && !player.is_vice_captain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 886,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium text-white\",\n                                                                                children: player.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                                lineNumber: 892,\n                                                                                columnNumber: 25\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 rounded text-xs font-medium bg-slate-600 text-slate-300\",\n                                                                                children: player.position\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                                lineNumber: 893,\n                                                                                columnNumber: 25\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-slate-400\",\n                                                                                children: player.team\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                                lineNumber: 896,\n                                                                                columnNumber: 25\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 891,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4 mt-1 text-xs text-slate-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    \"Price: \",\n                                                                                    formatCurrency(player.current_price)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                                lineNumber: 899,\n                                                                                columnNumber: 25\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    \"Avg: \",\n                                                                                    ((_player_season_average = player.season_average) === null || _player_season_average === void 0 ? void 0 : _player_season_average.toFixed(1)) || \"N/A\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                                lineNumber: 900,\n                                                                                columnNumber: 25\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: getFormRatingColor(player.form_rating),\n                                                                                children: [\n                                                                                    \"Form: \",\n                                                                                    ((_player_form_rating = player.form_rating) === null || _player_form_rating === void 0 ? void 0 : _player_form_rating.toFixed(1)) || \"N/A\",\n                                                                                    \"/10\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                                lineNumber: 901,\n                                                                                columnNumber: 25\n                                                                            }, _this),\n                                                                            !player.is_playing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-400\",\n                                                                                children: \"BENCH\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                                lineNumber: 905,\n                                                                                columnNumber: 27\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 898,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 21\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 19\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: function() {\n                                                                    return setCaptain(player.id);\n                                                                },\n                                                                className: \"px-2 py-1 text-xs bg-orange-600/20 text-orange-400 rounded hover:bg-orange-600/30 transition-colors\",\n                                                                disabled: player.is_captain,\n                                                                children: \"Captain\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 912,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: function() {\n                                                                    return setViceCaptain(player.id);\n                                                                },\n                                                                className: \"px-2 py-1 text-xs bg-blue-600/20 text-blue-400 rounded hover:bg-blue-600/30 transition-colors\",\n                                                                disabled: player.is_vice_captain,\n                                                                children: \"Vice\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 919,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: function() {\n                                                                    return handleTradePlayer(player);\n                                                                },\n                                                                className: \"px-2 py-1 text-xs bg-green-600/20 text-green-400 rounded hover:bg-green-600/30 transition-colors\",\n                                                                children: \"Trade\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 926,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: function() {\n                                                                    return togglePlayerStatus(player.id);\n                                                                },\n                                                                className: \"px-2 py-1 text-xs bg-purple-600/20 text-purple-400 rounded hover:bg-purple-600/30 transition-colors\",\n                                                                children: player.is_playing ? \"Bench\" : \"Play\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: function() {\n                                                                    return editPlayer(player);\n                                                                },\n                                                                className: \"p-2 text-slate-400 hover:text-white transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.PencilIcon, {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                    lineNumber: 942,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 938,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: function() {\n                                                                    return removePlayer(player.id);\n                                                                },\n                                                                className: \"p-2 text-red-400 hover:text-red-300 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.TrashIcon, {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                    lineNumber: 948,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 21\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                ]\n                                            }, player.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 866,\n                                                columnNumber: 17\n                                            }, _this);\n                                        }),\n                                        squadData.players.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12 text-slate-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.UserGroupIcon, {\n                                                    className: \"w-12 h-12 mx-auto mb-4 text-slate-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-medium mb-2\",\n                                                    children: \"No players in squad\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 957,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm mb-4\",\n                                                    children: \"Add players to start building your team\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 958,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"btn-primary\",\n                                                    onClick: function() {\n                                                        return setShowAddPlayerModal(true);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.PlusIcon, {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                            lineNumber: 963,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        \"Add Your First Player\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 959,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                    lineNumber: 864,\n                                    columnNumber: 13\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 863,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                        lineNumber: 842,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                        children: showAddPlayerModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50\",\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            exit: {\n                                opacity: 0\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                className: \"bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md\",\n                                initial: {\n                                    scale: 0.95,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    scale: 1,\n                                    opacity: 1\n                                },\n                                exit: {\n                                    scale: 0.95,\n                                    opacity: 0\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-slate-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Add New Player\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 989,\n                                                    columnNumber: 21\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: function() {\n                                                        return setShowAddPlayerModal(false);\n                                                    },\n                                                    className: \"text-slate-400 hover:text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.XMarkIcon, {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 23\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 990,\n                                                    columnNumber: 21\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 988,\n                                            columnNumber: 19\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 987,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                        children: \"Player Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1001,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PredictiveSearch__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        placeholder: \"Type 'Na' for Nathan Cleary, 'Re' for Reece Walsh...\",\n                                                        onPlayerSelect: function(player) {\n                                                            setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                name: player.name,\n                                                                team: player.team,\n                                                                position: player.position,\n                                                                current_price: player.price || 0,\n                                                                season_points: player.points || 0,\n                                                                season_average: player.average || 0,\n                                                                form_rating: player.form || 0\n                                                            }));\n                                                        },\n                                                        showPlayerDetails: true,\n                                                        maxResults: 8,\n                                                        minQueryLength: 1,\n                                                        clearOnSelect: false,\n                                                        className: \"w-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    newPlayer.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 p-2 bg-green-500/10 border border-green-500/20 rounded text-sm text-green-400\",\n                                                        children: [\n                                                            \"✅ Selected: \",\n                                                            newPlayer.name,\n                                                            \" (\",\n                                                            newPlayer.team,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1025,\n                                                        columnNumber: 23\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1000,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                                children: [\n                                                                    \"Team * \",\n                                                                    newPlayer.team && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-400 text-xs\",\n                                                                        children: \"(Auto-filled)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1034,\n                                                                        columnNumber: 51\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1033,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"input-primary w-full\",\n                                                                placeholder: \"e.g. Brisbane Broncos\",\n                                                                value: newPlayer.team || \"\",\n                                                                onChange: function(e) {\n                                                                    return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                        team: e.target.value\n                                                                    }));\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1036,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                                children: [\n                                                                    \"Position * \",\n                                                                    newPlayer.position && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-400 text-xs\",\n                                                                        children: \"(Auto-filled)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1046,\n                                                                        columnNumber: 59\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1045,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                className: \"input-primary w-full\",\n                                                                value: newPlayer.position || \"\",\n                                                                onChange: function(e) {\n                                                                    return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                        position: e.target.value\n                                                                    }));\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Select position\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1053,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"FLB\",\n                                                                        children: \"Fullback\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1054,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"CTW\",\n                                                                        children: \"Centre/Wing\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1055,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"HFB\",\n                                                                        children: \"Halfback\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1056,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"5/8\",\n                                                                        children: \"Five-eighth\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1057,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"HOK\",\n                                                                        children: \"Hooker\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1058,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"FRF\",\n                                                                        children: \"Front Row Forward\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1059,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"2RF\",\n                                                                        children: \"Second Row Forward\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1060,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"LCK\",\n                                                                        children: \"Lock\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1061,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1048,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1044,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                                children: [\n                                                                    \"Current Price ($) \",\n                                                                    newPlayer.current_price && newPlayer.current_price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-400 text-xs\",\n                                                                        children: \"(Auto-filled)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1069,\n                                                                        columnNumber: 102\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1068,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                className: \"input-primary w-full\",\n                                                                placeholder: \"0\",\n                                                                value: newPlayer.current_price || \"\",\n                                                                onChange: function(e) {\n                                                                    return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                        current_price: parseInt(e.target.value) || 0\n                                                                    }));\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1071,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                                children: [\n                                                                    \"Season Average \",\n                                                                    newPlayer.season_average && newPlayer.season_average > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-400 text-xs\",\n                                                                        children: \"(Auto-filled)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1081,\n                                                                        columnNumber: 101\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1080,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                step: \"0.1\",\n                                                                className: \"input-primary w-full\",\n                                                                placeholder: \"0.0\",\n                                                                value: newPlayer.season_average || \"\",\n                                                                onChange: function(e) {\n                                                                    return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                        season_average: parseFloat(e.target.value) || 0\n                                                                    }));\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1083,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1079,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1066,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                                children: [\n                                                                    \"Season Points \",\n                                                                    newPlayer.season_points && newPlayer.season_points > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-400 text-xs\",\n                                                                        children: \"(Auto-filled)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1097,\n                                                                        columnNumber: 98\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1096,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                className: \"input-primary w-full\",\n                                                                placeholder: \"0\",\n                                                                value: newPlayer.season_points || \"\",\n                                                                onChange: function(e) {\n                                                                    return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                        season_points: parseInt(e.target.value) || 0\n                                                                    }));\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1099,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1095,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                                children: [\n                                                                    \"Form Rating (0-10) \",\n                                                                    newPlayer.form_rating && newPlayer.form_rating > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-400 text-xs\",\n                                                                        children: \"(Auto-filled)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1109,\n                                                                        columnNumber: 99\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1108,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                step: \"0.1\",\n                                                                min: \"0\",\n                                                                max: \"10\",\n                                                                className: \"input-primary w-full\",\n                                                                placeholder: \"0.0\",\n                                                                value: newPlayer.form_rating || \"\",\n                                                                onChange: function(e) {\n                                                                    return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                        form_rating: parseFloat(e.target.value) || 0\n                                                                    }));\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1111,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1107,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1094,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"is_playing\",\n                                                        className: \"rounded border-slate-600 bg-slate-800 text-green-600 focus:ring-green-500\",\n                                                        checked: (_newPlayer_is_playing = newPlayer.is_playing) !== null && _newPlayer_is_playing !== void 0 ? _newPlayer_is_playing : true,\n                                                        onChange: function(e) {\n                                                            return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                is_playing: e.target.checked\n                                                            }));\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1125,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"is_playing\",\n                                                        className: \"text-sm text-slate-300\",\n                                                        children: \"Playing this round (uncheck for bench)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1132,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1124,\n                                                columnNumber: 19\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 999,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-t border-slate-700 flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: function() {\n                                                    return setShowAddPlayerModal(false);\n                                                },\n                                                className: \"btn-outline\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1139,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: addPlayer,\n                                                className: \"btn-primary\",\n                                                disabled: !newPlayer.name || !newPlayer.team || !newPlayer.position,\n                                                children: \"Add Player\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1145,\n                                                columnNumber: 19\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 1138,\n                                        columnNumber: 17\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                            lineNumber: 975,\n                            columnNumber: 13\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                        lineNumber: 973,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                        children: showEditPlayerModal && editingPlayer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50\",\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            exit: {\n                                opacity: 0\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                className: \"bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md\",\n                                initial: {\n                                    scale: 0.95,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    scale: 1,\n                                    opacity: 1\n                                },\n                                exit: {\n                                    scale: 0.95,\n                                    opacity: 0\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b border-slate-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Edit Player\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 1175,\n                                                    columnNumber: 21\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: function() {\n                                                        return setShowEditPlayerModal(false);\n                                                    },\n                                                    className: \"text-slate-400 hover:text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_ChartBarIcon_CurrencyDollarIcon_FireIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_StarIcon_TrashIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__.XMarkIcon, {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1180,\n                                                        columnNumber: 23\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                    lineNumber: 1176,\n                                                    columnNumber: 21\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                            lineNumber: 1174,\n                                            columnNumber: 19\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 1173,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                        children: \"Player Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1187,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        className: \"input-primary w-full\",\n                                                        placeholder: \"Enter player name\",\n                                                        value: newPlayer.name || \"\",\n                                                        onChange: function(e) {\n                                                            return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                name: e.target.value\n                                                            }));\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1190,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1186,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                                children: \"Team *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1201,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"input-primary w-full\",\n                                                                placeholder: \"e.g. Brisbane Broncos\",\n                                                                value: newPlayer.team || \"\",\n                                                                onChange: function(e) {\n                                                                    return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                        team: e.target.value\n                                                                    }));\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1204,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1200,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                                children: \"Position *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1213,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                className: \"input-primary w-full\",\n                                                                value: newPlayer.position || \"\",\n                                                                onChange: function(e) {\n                                                                    return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                        position: e.target.value\n                                                                    }));\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Select position\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1221,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"FLB\",\n                                                                        children: \"Fullback\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1222,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"CTW\",\n                                                                        children: \"Centre/Wing\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1223,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"HFB\",\n                                                                        children: \"Halfback\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1224,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"5/8\",\n                                                                        children: \"Five-eighth\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1225,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"HOK\",\n                                                                        children: \"Hooker\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1226,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"FRF\",\n                                                                        children: \"Front Row Forward\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1227,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"2RF\",\n                                                                        children: \"Second Row Forward\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1228,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"LCK\",\n                                                                        children: \"Lock\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                        lineNumber: 1229,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1216,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1212,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1199,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                                children: \"Current Price ($)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1236,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                className: \"input-primary w-full\",\n                                                                placeholder: \"0\",\n                                                                value: newPlayer.current_price || \"\",\n                                                                onChange: function(e) {\n                                                                    return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                        current_price: parseInt(e.target.value) || 0\n                                                                    }));\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1239,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1235,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                                children: \"Season Average\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1248,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                step: \"0.1\",\n                                                                className: \"input-primary w-full\",\n                                                                placeholder: \"0.0\",\n                                                                value: newPlayer.season_average || \"\",\n                                                                onChange: function(e) {\n                                                                    return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                        season_average: parseFloat(e.target.value) || 0\n                                                                    }));\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1251,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1247,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1234,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                                children: \"Season Points\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1264,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                className: \"input-primary w-full\",\n                                                                placeholder: \"0\",\n                                                                value: newPlayer.season_points || \"\",\n                                                                onChange: function(e) {\n                                                                    return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                        season_points: parseInt(e.target.value) || 0\n                                                                    }));\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1267,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1263,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-slate-300 mb-2\",\n                                                                children: \"Form Rating (0-10)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1276,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                step: \"0.1\",\n                                                                min: \"0\",\n                                                                max: \"10\",\n                                                                className: \"input-primary w-full\",\n                                                                placeholder: \"0.0\",\n                                                                value: newPlayer.form_rating || \"\",\n                                                                onChange: function(e) {\n                                                                    return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                        form_rating: parseFloat(e.target.value) || 0\n                                                                    }));\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                                lineNumber: 1279,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1275,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1262,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"edit_is_playing\",\n                                                        className: \"rounded border-slate-600 bg-slate-800 text-green-600 focus:ring-green-500\",\n                                                        checked: (_newPlayer_is_playing1 = newPlayer.is_playing) !== null && _newPlayer_is_playing1 !== void 0 ? _newPlayer_is_playing1 : true,\n                                                        onChange: function(e) {\n                                                            return setNewPlayer((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newPlayer), {\n                                                                is_playing: e.target.checked\n                                                            }));\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1293,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"edit_is_playing\",\n                                                        className: \"text-sm text-slate-300\",\n                                                        children: \"Playing this round (uncheck for bench)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                        lineNumber: 1300,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1292,\n                                                columnNumber: 19\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 1185,\n                                        columnNumber: 17\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-t border-slate-700 flex justify-end space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: function() {\n                                                    return setShowEditPlayerModal(false);\n                                                },\n                                                className: \"btn-outline\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1307,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: updatePlayer,\n                                                className: \"btn-primary\",\n                                                disabled: !newPlayer.name || !newPlayer.team || !newPlayer.position,\n                                                children: \"Update Player\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                                lineNumber: 1313,\n                                                columnNumber: 19\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                        lineNumber: 1306,\n                                        columnNumber: 17\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                                lineNumber: 1167,\n                                columnNumber: 15\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                            lineNumber: 1161,\n                            columnNumber: 13\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                        lineNumber: 1159,\n                        columnNumber: 9\n                    }, _this),\n                    showTradeAnalysis && tradePlayer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TradeAnalysis__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        player: tradePlayer,\n                        onClose: function() {\n                            setShowTradeAnalysis(false);\n                            setTradePlayer(null);\n                        },\n                        onExecuteTrade: handleExecuteTrade\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                        lineNumber: 1328,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n                lineNumber: 535,\n                columnNumber: 7\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\my-team.tsx\",\n        lineNumber: 529,\n        columnNumber: 5\n    }, _this);\n};\n_s(MyTeam, \"ZOuPZB73MUZhcJ6T0Ft7eR8/kyM=\");\n_c = MyTeam;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MyTeam);\nvar _c;\n$RefreshReg$(_c, \"MyTeam\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/my-team.tsx\n"));

/***/ })

});