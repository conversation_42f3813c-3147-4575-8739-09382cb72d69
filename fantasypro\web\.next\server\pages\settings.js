/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/settings";
exports.ids = ["pages/settings"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArrowTrendingUpIcon,Bars3Icon,BellIcon,ChartBarIcon,CogIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FireIcon,HomeIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowTrendingUpIcon,Bars3Icon,BellIcon,ChartBarIcon,CogIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FireIcon,HomeIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowTrendingUpIcon: () => (/* reexport safe */ _ArrowTrendingUpIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Bars3Icon: () => (/* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   BellIcon: () => (/* reexport safe */ _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   ChartBarIcon: () => (/* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   CogIcon: () => (/* reexport safe */ _CogIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   CurrencyDollarIcon: () => (/* reexport safe */ _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   ExclamationTriangleIcon: () => (/* reexport safe */ _ExclamationTriangleIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   FireIcon: () => (/* reexport safe */ _FireIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   TrophyIcon: () => (/* reexport safe */ _TrophyIcon_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   UserGroupIcon: () => (/* reexport safe */ _UserGroupIcon_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowTrendingUpIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowTrendingUpIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Bars3Icon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BellIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _CogIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CogIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CurrencyDollarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _ExclamationTriangleIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ExclamationTriangleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _FireIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FireIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./HomeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _TrophyIcon_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./TrophyIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _UserGroupIcon_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./UserGroupIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1RyZW5kaW5nVXBJY29uLEJhcnMzSWNvbixCZWxsSWNvbixDaGFydEJhckljb24sQ29nSWNvbixDdXJyZW5jeURvbGxhckljb24sRXhjbGFtYXRpb25UcmlhbmdsZUljb24sRmlyZUljb24sSG9tZUljb24sVHJvcGh5SWNvbixVc2VyR3JvdXBJY29uLFhNYXJrSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUN5RTtBQUNwQjtBQUNGO0FBQ1E7QUFDVjtBQUNzQjtBQUNVO0FBQzlCO0FBQ0E7QUFDSTtBQUNNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmFudGFzeXByby13ZWIvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz81MTc4Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd1RyZW5kaW5nVXBJY29uIH0gZnJvbSBcIi4vQXJyb3dUcmVuZGluZ1VwSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJhcnMzSWNvbiB9IGZyb20gXCIuL0JhcnMzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJlbGxJY29uIH0gZnJvbSBcIi4vQmVsbEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGFydEJhckljb24gfSBmcm9tIFwiLi9DaGFydEJhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb2dJY29uIH0gZnJvbSBcIi4vQ29nSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEN1cnJlbmN5RG9sbGFySWNvbiB9IGZyb20gXCIuL0N1cnJlbmN5RG9sbGFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEV4Y2xhbWF0aW9uVHJpYW5nbGVJY29uIH0gZnJvbSBcIi4vRXhjbGFtYXRpb25UcmlhbmdsZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBGaXJlSWNvbiB9IGZyb20gXCIuL0ZpcmVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSG9tZUljb24gfSBmcm9tIFwiLi9Ib21lSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRyb3BoeUljb24gfSBmcm9tIFwiLi9Ucm9waHlJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlckdyb3VwSWNvbiB9IGZyb20gXCIuL1VzZXJHcm91cEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYTWFya0ljb24gfSBmcm9tIFwiLi9YTWFya0ljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowTrendingUpIcon,Bars3Icon,BellIcon,ChartBarIcon,CogIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FireIcon,HomeIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=BellIcon,CheckCircleIcon,CogIcon,KeyIcon,MoonIcon,ShieldCheckIcon,SunIcon,TrashIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!****************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BellIcon,CheckCircleIcon,CogIcon,KeyIcon,MoonIcon,ShieldCheckIcon,SunIcon,TrashIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BellIcon: () => (/* reexport safe */ _BellIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CheckCircleIcon: () => (/* reexport safe */ _CheckCircleIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   CogIcon: () => (/* reexport safe */ _CogIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   KeyIcon: () => (/* reexport safe */ _KeyIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   MoonIcon: () => (/* reexport safe */ _MoonIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   ShieldCheckIcon: () => (/* reexport safe */ _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   SunIcon: () => (/* reexport safe */ _SunIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   TrashIcon: () => (/* reexport safe */ _TrashIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   UserIcon: () => (/* reexport safe */ _UserIcon_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _BellIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BellIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _CheckCircleIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CheckCircleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _CogIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CogIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _KeyIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./KeyIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _MoonIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MoonIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MoonIcon.js\");\n/* harmony import */ var _ShieldCheckIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ShieldCheckIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _SunIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./SunIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/SunIcon.js\");\n/* harmony import */ var _TrashIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TrashIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _UserIcon_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UserIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CZWxsSWNvbixDaGVja0NpcmNsZUljb24sQ29nSWNvbixLZXlJY29uLE1vb25JY29uLFNoaWVsZENoZWNrSWNvbixTdW5JY29uLFRyYXNoSWNvbixVc2VySWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNtRDtBQUNjO0FBQ2hCO0FBQ0E7QUFDRTtBQUNjO0FBQ2hCO0FBQ0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mYW50YXN5cHJvLXdlYi8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzPzhiNjUiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJlbGxJY29uIH0gZnJvbSBcIi4vQmVsbEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVja0NpcmNsZUljb24gfSBmcm9tIFwiLi9DaGVja0NpcmNsZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb2dJY29uIH0gZnJvbSBcIi4vQ29nSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEtleUljb24gfSBmcm9tIFwiLi9LZXlJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTW9vbkljb24gfSBmcm9tIFwiLi9Nb29uSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNoaWVsZENoZWNrSWNvbiB9IGZyb20gXCIuL1NoaWVsZENoZWNrSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFN1bkljb24gfSBmcm9tIFwiLi9TdW5JY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVHJhc2hJY29uIH0gZnJvbSBcIi4vVHJhc2hJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlckljb24gfSBmcm9tIFwiLi9Vc2VySWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BellIcon,CheckCircleIcon,CogIcon,KeyIcon,MoonIcon,ShieldCheckIcon,SunIcon,TrashIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=CheckIcon,ComputerDesktopIcon,MoonIcon,SunIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!******************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CheckIcon,ComputerDesktopIcon,MoonIcon,SunIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckIcon: () => (/* reexport safe */ _CheckIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ComputerDesktopIcon: () => (/* reexport safe */ _ComputerDesktopIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   MoonIcon: () => (/* reexport safe */ _MoonIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   SunIcon: () => (/* reexport safe */ _SunIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _CheckIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CheckIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _ComputerDesktopIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ComputerDesktopIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _MoonIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MoonIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MoonIcon.js\");\n/* harmony import */ var _SunIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SunIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/SunIcon.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVja0ljb24sQ29tcHV0ZXJEZXNrdG9wSWNvbixNb29uSWNvbixTdW5JY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDcUQ7QUFDb0I7QUFDdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mYW50YXN5cHJvLXdlYi8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzPzY1MjMiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZWNrSWNvbiB9IGZyb20gXCIuL0NoZWNrSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENvbXB1dGVyRGVza3RvcEljb24gfSBmcm9tIFwiLi9Db21wdXRlckRlc2t0b3BJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTW9vbkljb24gfSBmcm9tIFwiLi9Nb29uSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFN1bkljb24gfSBmcm9tIFwiLi9TdW5JY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=CheckIcon,ComputerDesktopIcon,MoonIcon,SunIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fsettings&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Csettings.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fsettings&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Csettings.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\settings.tsx */ \"./src/pages/settings.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/settings\",\n        pathname: \"/settings\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_settings_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fsettings&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Csettings.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ThemeToggle */ \"./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingUpIcon,Bars3Icon,BellIcon,ChartBarIcon,CogIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FireIcon,HomeIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowTrendingUpIcon,Bars3Icon,BellIcon,ChartBarIcon,CogIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FireIcon,HomeIcon,TrophyIcon,UserGroupIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_4__, _ThemeToggle__WEBPACK_IMPORTED_MODULE_5__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_4__, _ThemeToggle__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst Layout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const navigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.HomeIcon,\n            current: router.pathname === \"/dashboard\"\n        },\n        {\n            name: \"My Team\",\n            href: \"/my-team\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.UserGroupIcon,\n            current: router.pathname === \"/my-team\"\n        },\n        {\n            name: \"Players\",\n            href: \"/players\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.TrophyIcon,\n            current: router.pathname === \"/players\"\n        },\n        {\n            name: \"Analytics\",\n            href: \"/analytics\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ChartBarIcon,\n            current: router.pathname === \"/analytics\"\n        },\n        {\n            name: \"Trades\",\n            href: \"/trades\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ArrowTrendingUpIcon,\n            current: router.pathname === \"/trades\"\n        },\n        {\n            name: \"Injuries\",\n            href: \"/injuries\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ExclamationTriangleIcon,\n            current: router.pathname === \"/injuries\"\n        },\n        {\n            name: \"Captain\",\n            href: \"/captain\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.FireIcon,\n            current: router.pathname === \"/captain\"\n        },\n        {\n            name: \"Pricing\",\n            href: \"/pricing\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CurrencyDollarIcon,\n            current: router.pathname === \"/pricing\"\n        }\n    ];\n    const secondaryNavigation = [\n        {\n            name: \"Settings\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CogIcon\n        },\n        {\n            name: \"API Test\",\n            href: \"/api-test\",\n            icon: _barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.BellIcon\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen themed-bg-primary theme-transition\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"fixed inset-0 z-40 lg:hidden\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 themed-bg-overlay\",\n                                onClick: ()=>setSidebarOpen(false)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"fixed inset-y-0 left-0 z-50 w-64 themed-bg-secondary themed-border\",\n                                initial: {\n                                    x: -256\n                                },\n                                animate: {\n                                    x: 0\n                                },\n                                exit: {\n                                    x: -256\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    damping: 30,\n                                    stiffness: 300\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between h-16 px-6 themed-border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center themed-glow-primary glow-pulse-green\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"themed-text-inverse font-bold text-sm\",\n                                                            children: \"FP\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold themed-text-primary\",\n                                                        children: \"FantasyPro\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                        lineNumber: 70,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSidebarOpen(false),\n                                                className: \"text-slate-400 hover:text-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                                    className: \"w-6 h-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"mt-6 px-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: item.href,\n                                                        className: item.current ? \"nav-link-active\" : \"nav-link-inactive\",\n                                                        onClick: ()=>setSidebarOpen(false),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"w-5 h-5 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                                lineNumber: 88,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            item.name\n                                                        ]\n                                                    }, item.name, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-8 pt-6 border-t border-slate-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: secondaryNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: item.href,\n                                                            className: \"nav-link-inactive\",\n                                                            onClick: ()=>setSidebarOpen(false),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    className: \"w-5 h-5 mr-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                                    lineNumber: 102,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                item.name\n                                                            ]\n                                                        }, item.name, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex grow flex-col gap-y-5 overflow-y-auto bg-slate-800 border-r border-slate-700 px-6 pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-16 shrink-0 items-center border-b border-slate-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center shadow-glow-green glow-pulse-green\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"FP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"FantasyPro\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex flex-1 flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                role: \"list\",\n                                className: \"flex flex-1 flex-col gap-y-7\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            role: \"list\",\n                                            className: \"-mx-2 space-y-1\",\n                                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: item.href,\n                                                        className: `${item.current ? \"nav-link-active shadow-glow-green\" : \"nav-link-inactive\"} magnetic`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"w-5 h-5 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            item.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, item.name, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"mt-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            role: \"list\",\n                                            className: \"-mx-2 space-y-1\",\n                                            children: secondaryNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: item.href,\n                                                        className: \"nav-link-inactive\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"w-5 h-5 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            item.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, item.name, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:pl-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-slate-700 bg-slate-800 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"-m-2.5 p-2.5 text-slate-400 lg:hidden\",\n                                onClick: ()=>setSidebarOpen(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open sidebar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.Bars3Icon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 w-px bg-slate-700 lg:hidden\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-x-4 lg:gap-x-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 bg-green-900/20 px-3 py-1 rounded-lg status-live shadow-glow-green\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-400\",\n                                                    children: \"SuperCoach Live\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-slate-400\",\n                                                    children: \"Real Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-x-4 lg:gap-x-6 ml-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                variant: \"dropdown\",\n                                                size: \"md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"-m-2.5 p-2.5 themed-text-tertiary hover:themed-text-primary magnetic hover:themed-glow-secondary transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: \"View notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingUpIcon_Bars3Icon_BellIcon_ChartBarIcon_CogIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FireIcon_HomeIcon_TrophyIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.BellIcon, {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm themed-text-tertiary\",\n                                                children: [\n                                                    \"Last updated: \",\n                                                    new Date().toLocaleTimeString(),\n                                                    \" • Live\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 lg:px-8\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\Layout.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n");

/***/ }),

/***/ "./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ComputerDesktopIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ComputerDesktopIcon,MoonIcon,SunIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=CheckIcon,ComputerDesktopIcon,MoonIcon,SunIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nconst ThemeToggle = ({ variant = \"button\", size = \"md\", showLabel = false, className = \"\" })=>{\n    const { theme, resolvedTheme, setTheme, toggleTheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const themes = [\n        {\n            value: \"light\",\n            label: \"Light\",\n            icon: _barrel_optimize_names_CheckIcon_ComputerDesktopIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.SunIcon\n        },\n        {\n            value: \"dark\",\n            label: \"Dark\",\n            icon: _barrel_optimize_names_CheckIcon_ComputerDesktopIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.MoonIcon\n        },\n        {\n            value: \"system\",\n            label: \"System\",\n            icon: _barrel_optimize_names_CheckIcon_ComputerDesktopIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ComputerDesktopIcon\n        }\n    ];\n    const currentTheme = themes.find((t)=>t.value === theme) || themes[1];\n    const CurrentIcon = currentTheme.icon;\n    const sizeClasses = {\n        sm: \"w-8 h-8 text-sm\",\n        md: \"w-10 h-10 text-base\",\n        lg: \"w-12 h-12 text-lg\"\n    };\n    const iconSizes = {\n        sm: \"w-4 h-4\",\n        md: \"w-5 h-5\",\n        lg: \"w-6 h-6\"\n    };\n    if (variant === \"switch\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex items-center space-x-3 ${className}`,\n            children: [\n                showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium themed-text-secondary\",\n                    children: [\n                        resolvedTheme === \"dark\" ? \"Dark\" : \"Light\",\n                        \" Mode\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: toggleTheme,\n                    className: `\n            relative inline-flex items-center justify-center\n            ${sizeClasses[size]} rounded-full\n            themed-bg-tertiary themed-border\n            hover:themed-bg-elevated\n            transition-all duration-300 ease-in-out\n            focus:outline-none focus:ring-2 focus:ring-offset-2 \n            focus:ring-offset-var(--bg-primary) focus:ring-var(--brand-primary)\n            group overflow-hidden\n          `,\n                    \"aria-label\": `Switch to ${resolvedTheme === \"dark\" ? \"light\" : \"dark\"} mode`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 themed-glow-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                rotate: -180,\n                                opacity: 0\n                            },\n                            animate: {\n                                rotate: 0,\n                                opacity: 1\n                            },\n                            exit: {\n                                rotate: 180,\n                                opacity: 0\n                            },\n                            transition: {\n                                duration: 0.3,\n                                ease: \"easeInOut\"\n                            },\n                            className: \"relative z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrentIcon, {\n                                className: `${iconSizes[size]} themed-text-primary`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, undefined)\n                        }, resolvedTheme, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (variant === \"dropdown\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `relative ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsOpen(!isOpen),\n                    className: `\n            flex items-center space-x-2 px-3 py-2 rounded-lg\n            themed-bg-secondary themed-border\n            hover:themed-bg-tertiary\n            transition-all duration-200\n            focus:outline-none focus:ring-2 focus:ring-var(--brand-primary)\n          `,\n                    \"aria-label\": \"Theme selector\",\n                    \"aria-expanded\": isOpen,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrentIcon, {\n                            className: iconSizes[size]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, undefined),\n                        showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium themed-text-primary\",\n                            children: currentTheme.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.svg, {\n                            animate: {\n                                rotate: isOpen ? 180 : 0\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            className: \"w-4 h-4 themed-text-tertiary\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 9l-7 7-7-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                    children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 z-10\",\n                                onClick: ()=>setIsOpen(false)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: -10,\n                                    scale: 0.95\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0,\n                                    scale: 1\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -10,\n                                    scale: 0.95\n                                },\n                                transition: {\n                                    duration: 0.2\n                                },\n                                className: \" absolute right-0 mt-2 w-48 z-20 themed-bg-secondary themed-border rounded-lg themed-shadow-lg overflow-hidden \",\n                                children: themes.map((themeOption)=>{\n                                    const Icon = themeOption.icon;\n                                    const isSelected = theme === themeOption.value;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setTheme(themeOption.value);\n                                            setIsOpen(false);\n                                        },\n                                        className: `\n                        w-full flex items-center justify-between px-4 py-3\n                        hover:themed-bg-tertiary\n                        transition-colors duration-150\n                        ${isSelected ? \"themed-bg-tertiary\" : \"\"}\n                      `,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-5 h-5 themed-text-secondary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium themed-text-primary\",\n                                                        children: themeOption.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ComputerDesktopIcon_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CheckIcon, {\n                                                className: \"w-4 h-4 text-var(--brand-primary)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, themeOption.value, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 21\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Default button variant\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: `\n        relative inline-flex items-center justify-center\n        ${sizeClasses[size]} rounded-lg\n        themed-bg-secondary themed-border\n        hover:themed-bg-tertiary hover:themed-glow-primary\n        transition-all duration-300 ease-in-out\n        focus:outline-none focus:ring-2 focus:ring-offset-2 \n        focus:ring-offset-var(--bg-primary) focus:ring-var(--brand-primary)\n        group overflow-hidden\n        ${className}\n      `,\n        \"aria-label\": `Switch to ${resolvedTheme === \"dark\" ? \"light\" : \"dark\"} mode`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 shimmer opacity-0 group-hover:opacity-100\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                mode: \"wait\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        rotate: -90,\n                        opacity: 0\n                    },\n                    animate: {\n                        rotate: 0,\n                        opacity: 1\n                    },\n                    exit: {\n                        rotate: 90,\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.3,\n                        ease: \"easeInOut\"\n                    },\n                    className: \"relative z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrentIcon, {\n                        className: `${iconSizes[size]} themed-text-primary`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, undefined)\n                }, resolvedTheme, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, undefined),\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2 text-sm font-medium themed-text-primary\",\n                children: currentTheme.label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ThemeToggle);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   ThemeTransition: () => (/* binding */ ThemeTransition),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useThemeTransition: () => (/* binding */ useThemeTransition)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\nconst ThemeProvider = ({ children, defaultTheme = \"system\", storageKey = \"fantasypro-theme\" })=>{\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dark\");\n    // Get system preference\n    const getSystemTheme = ()=>{\n        if (false) {}\n        return \"dark\";\n    };\n    // Resolve theme based on current setting\n    const resolveTheme = (currentTheme)=>{\n        if (currentTheme === \"system\") {\n            return getSystemTheme();\n        }\n        return currentTheme;\n    };\n    // Apply theme to document\n    const applyTheme = (resolvedTheme)=>{\n        if (false) {}\n    };\n    // Set theme with persistence\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        if (false) {}\n        const resolved = resolveTheme(newTheme);\n        setResolvedTheme(resolved);\n        applyTheme(resolved);\n    };\n    // Toggle between dark and light (skips system)\n    const toggleTheme = ()=>{\n        const newTheme = resolvedTheme === \"dark\" ? \"light\" : \"dark\";\n        setTheme(newTheme);\n    };\n    // Initialize theme on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        defaultTheme,\n        storageKey\n    ]);\n    // Listen for system theme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        theme\n    ]);\n    const value = {\n        theme,\n        resolvedTheme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, undefined);\n};\n// Theme transition component for smooth animations\nconst ThemeTransition = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"theme-transition\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook for theme-aware animations\nconst useThemeTransition = ()=>{\n    const { resolvedTheme } = useTheme();\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const triggerTransition = ()=>{\n        setIsTransitioning(true);\n        setTimeout(()=>setIsTransitioning(false), 300);\n    };\n    return {\n        isTransitioning,\n        triggerTransition,\n        resolvedTheme\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ThemeProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_dropdown_fix_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/dropdown-fix.css */ \"./src/styles/dropdown-fix.css\");\n/* harmony import */ var _styles_dropdown_fix_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_dropdown_fix_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"./src/contexts/ThemeContext.tsx\");\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n        defaultTheme: \"dark\",\n        storageKey: \"fantasypro-theme\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQytCO0FBQ0s7QUFDcUI7QUFFMUMsU0FBU0MsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBWTtJQUM1RCxxQkFDRSw4REFBQ0gsaUVBQWFBO1FBQUNJLGNBQWE7UUFBT0MsWUFBVztrQkFDNUMsNEVBQUNIO1lBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7QUFHOUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mYW50YXN5cHJvLXdlYi8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgJy4uL3N0eWxlcy9kcm9wZG93bi1maXguY3NzJztcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICcuLi9jb250ZXh0cy9UaGVtZUNvbnRleHQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxUaGVtZVByb3ZpZGVyIGRlZmF1bHRUaGVtZT1cImRhcmtcIiBzdG9yYWdlS2V5PVwiZmFudGFzeXByby10aGVtZVwiPlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvVGhlbWVQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJUaGVtZVByb3ZpZGVyIiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiZGVmYXVsdFRoZW1lIiwic3RvcmFnZUtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/settings.tsx":
/*!********************************!*\
  !*** ./src/pages/settings.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,CheckCircleIcon,CogIcon,KeyIcon,MoonIcon,ShieldCheckIcon,SunIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BellIcon,CheckCircleIcon,CogIcon,KeyIcon,MoonIcon,ShieldCheckIcon,SunIcon,TrashIcon,UserIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_3__, _components_Layout__WEBPACK_IMPORTED_MODULE_4__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_3__, _components_Layout__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst Settings = ()=>{\n    const { theme, toggleTheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        profile: {\n            name: \"SuperCoach Pro\",\n            email: \"<EMAIL>\",\n            team_name: \"Sousside Rattlers\",\n            favorite_team: \"Brisbane Broncos\"\n        },\n        notifications: {\n            injury_alerts: true,\n            trade_recommendations: true,\n            price_changes: false,\n            team_news: true,\n            email_notifications: true,\n            push_notifications: false\n        },\n        preferences: {\n            theme: theme,\n            currency_format: \"short\",\n            time_format: \"12h\",\n            language: \"en\",\n            auto_refresh: true,\n            show_advanced_stats: true\n        },\n        privacy: {\n            public_profile: false,\n            share_team: true,\n            analytics_tracking: true,\n            data_collection: true\n        }\n    });\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"profile\");\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const teams = [\n        \"Brisbane Broncos\",\n        \"Sydney Roosters\",\n        \"Melbourne Storm\",\n        \"Penrith Panthers\",\n        \"Cronulla Sharks\",\n        \"Manly Sea Eagles\",\n        \"South Sydney Rabbitohs\",\n        \"Parramatta Eels\",\n        \"Newcastle Knights\",\n        \"Canberra Raiders\",\n        \"Gold Coast Titans\",\n        \"St George Illawarra Dragons\",\n        \"Canterbury Bulldogs\",\n        \"Wests Tigers\",\n        \"North Queensland Cowboys\",\n        \"New Zealand Warriors\",\n        \"Dolphins\"\n    ];\n    const updateSettings = (section, key, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [section]: {\n                    ...prev[section],\n                    [key]: value\n                }\n            }));\n        setHasChanges(true);\n    };\n    const saveSettings = async ()=>{\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            setHasChanges(false);\n            alert(\"Settings saved successfully!\");\n        } catch (error) {\n            alert(\"Error saving settings. Please try again.\");\n        }\n    };\n    const resetSettings = ()=>{\n        if (confirm(\"Are you sure you want to reset all settings to default?\")) {\n            // Reset to default settings\n            setHasChanges(true);\n            alert(\"Settings reset to default values\");\n        }\n    };\n    const exportData = ()=>{\n        alert(\"Data export feature coming soon!\");\n    };\n    const deleteAccount = ()=>{\n        if (confirm(\"Are you sure you want to delete your account? This action cannot be undone.\")) {\n            alert(\"Account deletion feature coming soon!\");\n        }\n    };\n    const tabs = [\n        {\n            id: \"profile\",\n            name: \"Profile\",\n            icon: _barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.UserIcon\n        },\n        {\n            id: \"notifications\",\n            name: \"Notifications\",\n            icon: _barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.BellIcon\n        },\n        {\n            id: \"preferences\",\n            name: \"Preferences\",\n            icon: _barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CogIcon\n        },\n        {\n            id: \"privacy\",\n            name: \"Privacy\",\n            icon: _barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ShieldCheckIcon\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Settings - FantasyPro\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Customize your FantasyPro experience\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold themed-text-primary\",\n                                        children: \"Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"themed-text-tertiary mt-1\",\n                                        children: \"Customize your FantasyPro experience\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: saveSettings,\n                                    className: \"btn-primary btn-ripple\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CheckCircleIcon, {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Save Changes\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"space-y-2\",\n                                        children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: `w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${activeTab === tab.id ? \"bg-blue-600/20 text-blue-400 border border-blue-600/30\" : \"themed-text-secondary hover:themed-text-primary hover:bg-slate-700/50\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: tab.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, tab.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    className: \"card p-6\",\n                                    children: [\n                                        activeTab === \"profile\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.UserIcon, {\n                                                            className: \"w-6 h-6 text-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-semibold themed-text-primary\",\n                                                            children: \"Profile Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium themed-text-secondary mb-2\",\n                                                                    children: \"Display Name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    className: \"input-primary w-full\",\n                                                                    value: settings.profile.name,\n                                                                    onChange: (e)=>updateSettings(\"profile\", \"name\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium themed-text-secondary mb-2\",\n                                                                    children: \"Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    className: \"input-primary w-full\",\n                                                                    value: settings.profile.email,\n                                                                    onChange: (e)=>updateSettings(\"profile\", \"email\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium themed-text-secondary mb-2\",\n                                                                    children: \"Team Name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    className: \"input-primary w-full\",\n                                                                    value: settings.profile.team_name,\n                                                                    onChange: (e)=>updateSettings(\"profile\", \"team_name\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium themed-text-secondary mb-2\",\n                                                                    children: \"Favorite NRL Team\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    className: \"input-primary w-full\",\n                                                                    value: settings.profile.favorite_team,\n                                                                    onChange: (e)=>updateSettings(\"profile\", \"favorite_team\", e.target.value),\n                                                                    children: teams.map((team)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: team,\n                                                                            children: team\n                                                                        }, team, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 260,\n                                                                            columnNumber: 27\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 254,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        activeTab === \"notifications\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.BellIcon, {\n                                                            className: \"w-6 h-6 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-semibold themed-text-primary\",\n                                                            children: \"Notification Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-4 card-hover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium themed-text-primary\",\n                                                                            children: \"Injury Alerts\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 279,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm themed-text-tertiary\",\n                                                                            children: \"Get notified when players get injured\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            className: \"sr-only peer\",\n                                                                            checked: settings.notifications.injury_alerts,\n                                                                            onChange: (e)=>updateSettings(\"notifications\", \"injury_alerts\", e.target.checked)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 283,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-4 card-hover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium themed-text-primary\",\n                                                                            children: \"Trade Recommendations\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 295,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm themed-text-tertiary\",\n                                                                            children: \"AI-powered trade suggestions\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 296,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            className: \"sr-only peer\",\n                                                                            checked: settings.notifications.trade_recommendations,\n                                                                            onChange: (e)=>updateSettings(\"notifications\", \"trade_recommendations\", e.target.checked)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-4 card-hover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium themed-text-primary\",\n                                                                            children: \"Price Changes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm themed-text-tertiary\",\n                                                                            children: \"Player price rise/fall notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 312,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            className: \"sr-only peer\",\n                                                                            checked: settings.notifications.price_changes,\n                                                                            onChange: (e)=>updateSettings(\"notifications\", \"price_changes\", e.target.checked)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-4 card-hover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium themed-text-primary\",\n                                                                            children: \"Team News\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 327,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm themed-text-tertiary\",\n                                                                            children: \"Latest news about your players\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            className: \"sr-only peer\",\n                                                                            checked: settings.notifications.team_news,\n                                                                            onChange: (e)=>updateSettings(\"notifications\", \"team_news\", e.target.checked)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 331,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        activeTab === \"preferences\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CogIcon, {\n                                                            className: \"w-6 h-6 text-purple-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-semibold themed-text-primary\",\n                                                            children: \"Preferences\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium themed-text-secondary mb-2\",\n                                                                            children: \"Theme\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 355,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>{\n                                                                                        updateSettings(\"preferences\", \"theme\", \"dark\");\n                                                                                        if (theme !== \"dark\") toggleTheme();\n                                                                                    },\n                                                                                    className: `flex-1 flex items-center justify-center space-x-2 p-3 rounded-lg border transition-colors ${settings.preferences.theme === \"dark\" ? \"border-blue-600 bg-blue-600/20 text-blue-400\" : \"border-slate-600 themed-text-secondary hover:themed-text-primary\"}`,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.MoonIcon, {\n                                                                                            className: \"w-4 h-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                            lineNumber: 370,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"Dark\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                            lineNumber: 371,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                    lineNumber: 359,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    onClick: ()=>{\n                                                                                        updateSettings(\"preferences\", \"theme\", \"light\");\n                                                                                        if (theme !== \"light\") toggleTheme();\n                                                                                    },\n                                                                                    className: `flex-1 flex items-center justify-center space-x-2 p-3 rounded-lg border transition-colors ${settings.preferences.theme === \"light\" ? \"border-blue-600 bg-blue-600/20 text-blue-400\" : \"border-slate-600 themed-text-secondary hover:themed-text-primary\"}`,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.SunIcon, {\n                                                                                            className: \"w-4 h-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                            lineNumber: 384,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"Light\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                            lineNumber: 385,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                    lineNumber: 373,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 358,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium themed-text-secondary mb-2\",\n                                                                            children: \"Currency Format\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 391,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            className: \"input-primary w-full\",\n                                                                            value: settings.preferences.currency_format,\n                                                                            onChange: (e)=>updateSettings(\"preferences\", \"currency_format\", e.target.value),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"short\",\n                                                                                    children: \"Short ($1.2M)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                    lineNumber: 399,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"full\",\n                                                                                    children: \"Full ($1,200,000)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                    lineNumber: 400,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-4 card-hover\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium themed-text-primary\",\n                                                                                    children: \"Auto Refresh\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                    lineNumber: 408,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm themed-text-tertiary\",\n                                                                                    children: \"Automatically refresh data every 5 minutes\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                    lineNumber: 409,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"relative inline-flex items-center cursor-pointer\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    className: \"sr-only peer\",\n                                                                                    checked: settings.preferences.auto_refresh,\n                                                                                    onChange: (e)=>updateSettings(\"preferences\", \"auto_refresh\", e.target.checked)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                    lineNumber: 412,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                    lineNumber: 418,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-4 card-hover\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium themed-text-primary\",\n                                                                                    children: \"Advanced Statistics\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                    lineNumber: 424,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm themed-text-tertiary\",\n                                                                                    children: \"Show detailed analytics and metrics\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                    lineNumber: 425,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 423,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"relative inline-flex items-center cursor-pointer\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    className: \"sr-only peer\",\n                                                                                    checked: settings.preferences.show_advanced_stats,\n                                                                                    onChange: (e)=>updateSettings(\"preferences\", \"show_advanced_stats\", e.target.checked)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                    lineNumber: 428,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                                    lineNumber: 434,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 427,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        activeTab === \"privacy\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ShieldCheckIcon, {\n                                                            className: \"w-6 h-6 text-red-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-semibold themed-text-primary\",\n                                                            children: \"Privacy & Security\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-4 card-hover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium themed-text-primary\",\n                                                                            children: \"Public Profile\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 453,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm themed-text-tertiary\",\n                                                                            children: \"Allow others to view your profile\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            className: \"sr-only peer\",\n                                                                            checked: settings.privacy.public_profile,\n                                                                            onChange: (e)=>updateSettings(\"privacy\", \"public_profile\", e.target.checked)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 457,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 463,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-4 card-hover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium themed-text-primary\",\n                                                                            children: \"Share Team\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 469,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm themed-text-tertiary\",\n                                                                            children: \"Allow others to view your team composition\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 470,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"relative inline-flex items-center cursor-pointer\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            className: \"sr-only peer\",\n                                                                            checked: settings.privacy.share_team,\n                                                                            onChange: (e)=>updateSettings(\"privacy\", \"share_team\", e.target.checked)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 473,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-6 border-t themed-border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold themed-text-primary mb-4\",\n                                                            children: \"Data Management\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: exportData,\n                                                                    className: \"btn-secondary btn-ripple\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.KeyIcon, {\n                                                                            className: \"w-4 h-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"Export My Data\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: deleteAccount,\n                                                                    className: \"btn-danger btn-ripple\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_CheckCircleIcon_CogIcon_KeyIcon_MoonIcon_ShieldCheckIcon_SunIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.TrashIcon, {\n                                                                            className: \"w-4 h-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"Delete Account\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, activeTab, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORIGVMI\\\\Github\\\\Playground\\\\OCTOAGENT\\\\fantasypro\\\\web\\\\src\\\\pages\\\\settings.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Settings);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/pages/settings.tsx\n");

/***/ }),

/***/ "./src/styles/dropdown-fix.css":
/*!*************************************!*\
  !*** ./src/styles/dropdown-fix.css ***!
  \*************************************/
/***/ (() => {



/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@heroicons"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fsettings&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Csettings.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();