import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://fuxpdgsixnbbsdspusmp.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1MDgwNTYsImV4cCI6MjA2NjA4NDA1Nn0.ifF15ZorqCzGZvL-buLhm7t51T0MWtV-9EPSNotzqaA';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ1eHBkZ3NpeG5iYnNkc3B1c21wIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDUwODA1NiwiZXhwIjoyMDY2MDg0MDU2fQ.f5qZOHQlAKz_sbIwoGmY4Gub8bGHyx_jxlRYZk-1VPg';

// Create Supabase clients
export const supabase = createClient(supabaseUrl, supabaseAnonKey);
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// Types for our database schema
export interface Player {
  id: string;
  name: string;
  position: string;
  team: string;
  price: number;
  points: number;
  average: number; // Required by Players page
  form: number;
  ownership: number;
  breakeven: number;
  image_url?: string;
  created_at: string;
  updated_at: string;
}

export interface TradeRecommendation {
  id: string;
  user_id: string;
  player_out_id: string;
  player_in_id: string;
  reason: string;
  confidence: number;
  price_change: number;
  points_gain: number;
  risk_level: 'low' | 'medium' | 'high';
  created_at: string;
}

export interface InjuryReport {
  id: string;
  player_id: string;
  injury_type: string;
  status: string;
  expected_return?: string;
  severity: 'minor' | 'moderate' | 'major';
  created_at: string;
  updated_at: string;
}

export interface UserSquad {
  id: string;
  user_id: string;
  player_id: string;
  position_in_squad: string;
  is_captain: boolean;
  is_vice_captain: boolean;
  created_at: string;
}

// Player service functions
export class PlayerService {
  // Get all players with search and filtering
  static async getPlayers(options: {
    search?: string;
    position?: string;
    team?: string;
    limit?: number;
    offset?: number;
  } = {}) {
    let query = supabase
      .from('players')
      .select('*');

    if (options.search) {
      query = query.or(`name.ilike.%${options.search}%,team.ilike.%${options.search}%,position.ilike.%${options.search}%`);
    }

    if (options.position) {
      query = query.eq('position', options.position);
    }

    if (options.team) {
      query = query.eq('team', options.team);
    }

    if (options.limit) {
      query = query.limit(options.limit);
    }

    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error } = await query.order('points', { ascending: false });

    if (error) {
      console.error('Error fetching players:', error);
      throw error;
    }

    return data as Player[];
  }

  // Get ALL players (for Players page - should return 581 players)
  static async getAllPlayers() {
    console.log('🔄 PlayerService.getAllPlayers() - fetching complete 581 player dataset from nrl_players table');

    try {
      // Get base player data from nrl_players
      const { data: playersData, error: playersError } = await supabase
        .from('nrl_players')
        .select('*')
        .order('name');

      if (playersError) {
        console.error('❌ Error fetching players from nrl_players table:', playersError);
        throw playersError;
      }

      // Get supplementary stats from nrl_player_stats
      const { data: statsData, error: statsError } = await supabase
        .from('nrl_player_stats')
        .select('*');

      if (statsError) {
        console.warn('⚠️ Could not fetch supplementary stats:', statsError);
      }

      console.log(`✅ Supabase nrl_players table returned ${playersData?.length || 0} players`);
      console.log(`✅ Supabase nrl_player_stats table returned ${statsData?.length || 0} stat records`);

      // Create a map of stats by player name for quick lookup
      const statsMap = new Map();
      if (statsData) {
        statsData.forEach(stat => {
          if (stat.name) {
            statsMap.set(stat.name.toLowerCase(), stat);
          }
        });
      }

      // Transform nrl_players data to match our Player interface
      const transformedPlayers = playersData?.map(player => {
        const baseStats = player.statistics || {};
        const supplementaryStats = statsMap.get(player.name.toLowerCase()) || {};

        // Use supplementary stats if available, otherwise use base stats
        const price = supplementaryStats.price || baseStats.price || 300000;
        const totalPoints = supplementaryStats.total_points || baseStats.total_points || 0;
        const averagePoints = supplementaryStats.average_points || baseStats.average_points || 0;
        const breakeven = supplementaryStats.breakeven || baseStats.breakeven || 0;

        // Calculate average if not available
        const calculatedAverage = averagePoints > 0 ? averagePoints :
          (totalPoints > 0 && supplementaryStats.games_played > 0) ?
          totalPoints / supplementaryStats.games_played : 0;

        return {
          id: player.id.toString(),
          name: player.name,
          position: player.position || supplementaryStats.position || 'Unknown',
          team: player.team_name || supplementaryStats.team || 'Unknown Team',
          price: price,
          points: totalPoints,
          average: calculatedAverage, // Required by Players page
          form: baseStats.form || (calculatedAverage / 10), // Calculate form from average
          ownership: baseStats.ownership || 0,
          breakeven: breakeven,
          image_url: player.image_url,
          created_at: player.created_at,
          updated_at: player.updated_at
        };
      }) || [];

      console.log(`✅ Transformed ${transformedPlayers.length} players with enhanced stats`);
      return transformedPlayers as Player[];

    } catch (error) {
      console.error('❌ Error in getAllPlayers:', error);
      throw error;
    }
  }

  // Get player by ID
  static async getPlayer(id: string) {
    const { data, error } = await supabase
      .from('players')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching player:', error);
      throw error;
    }

    return data as Player;
  }

  // Search players with predictive algorithm (searches all 581 players)
  static async searchPlayers(query: string, limit: number = 10) {
    if (!query || query.length < 2) return [];

    console.log(`🔍 Searching nrl_players table for: "${query}"`);

    const { data, error } = await supabase
      .from('nrl_players')
      .select('*')
      .or(`name.ilike.%${query}%,team_name.ilike.%${query}%,position.ilike.%${query}%`)
      .order('name')
      .limit(limit);

    if (error) {
      console.error('Error searching nrl_players:', error);
      return [];
    }

    console.log(`✅ Search found ${data?.length || 0} players in nrl_players table`);

    // Transform search results to match our Player interface
    const transformedResults = data?.map(player => {
      const stats = player.statistics || {};
      const totalPoints = stats.total_points || 0;
      const gamesPlayed = stats.games_played || 1;
      const averagePoints = gamesPlayed > 0 ? totalPoints / gamesPlayed : 0;

      return {
        id: player.id.toString(),
        name: player.name,
        position: player.position || 'Unknown',
        team: player.team_name || 'Unknown Team',
        price: stats.price || 300000,
        points: totalPoints,
        average: averagePoints, // Required by Players page
        form: stats.form || (averagePoints / 10), // Calculate form from average
        ownership: stats.ownership || 0,
        breakeven: stats.breakeven || 0,
        image_url: player.image_url,
        created_at: player.created_at,
        updated_at: player.updated_at
      };
    }) || [];

    return transformedResults as Player[];
  }

  // Get top performers
  static async getTopPerformers(limit: number = 10) {
    const { data, error } = await supabase
      .from('players')
      .select('*')
      .order('points', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching top performers:', error);
      throw error;
    }

    return data as Player[];
  }

  // Get price risers
  static async getPriceRisers(limit: number = 10) {
    const { data, error } = await supabase
      .from('players')
      .select('*')
      .order('price', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching price risers:', error);
      throw error;
    }

    return data as Player[];
  }

  // Update player data
  static async updatePlayer(id: string, updates: Partial<Player>) {
    const { data, error } = await supabase
      .from('players')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating player:', error);
      throw error;
    }

    return data as Player;
  }
}

// Trade recommendation service
export class TradeService {
  // Get trade recommendations for user
  static async getTradeRecommendations(userId: string, limit: number = 10) {
    const { data, error } = await supabase
      .from('trade_recommendations')
      .select(`
        *,
        player_out:players!player_out_id(*),
        player_in:players!player_in_id(*)
      `)
      .eq('user_id', userId)
      .order('confidence', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching trade recommendations:', error);
      throw error;
    }

    return data;
  }

  // Create trade recommendation
  static async createTradeRecommendation(recommendation: Omit<TradeRecommendation, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('trade_recommendations')
      .insert({
        ...recommendation,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating trade recommendation:', error);
      throw error;
    }

    return data as TradeRecommendation;
  }

  // Execute trade (log the trade)
  static async executeTrade(tradeId: string, userId: string) {
    const { data, error } = await supabase
      .from('executed_trades')
      .insert({
        trade_recommendation_id: tradeId,
        user_id: userId,
        executed_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error executing trade:', error);
      throw error;
    }

    return data;
  }
}

// Injury service
export class InjuryService {
  // Get current injury reports
  static async getInjuryReports(limit: number = 10) {
    const { data, error } = await supabase
      .from('injury_reports')
      .select(`
        *,
        player:players(*)
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching injury reports:', error);
      throw error;
    }

    return data;
  }

  // Create injury report
  static async createInjuryReport(report: Omit<InjuryReport, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('injury_reports')
      .insert({
        ...report,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating injury report:', error);
      throw error;
    }

    return data as InjuryReport;
  }
}

// User squad service
export class SquadService {
  // Get user's squad
  static async getUserSquad(userId: string) {
    const { data, error } = await supabase
      .from('user_squads')
      .select(`
        *,
        player:players(*)
      `)
      .eq('user_id', userId)
      .order('position_in_squad');

    if (error) {
      console.error('Error fetching user squad:', error);
      throw error;
    }

    return data;
  }

  // Add player to squad
  static async addPlayerToSquad(squadEntry: Omit<UserSquad, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('user_squads')
      .insert({
        ...squadEntry,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding player to squad:', error);
      throw error;
    }

    return data as UserSquad;
  }

  // Set captain
  static async setCaptain(userId: string, playerId: string) {
    // First, remove captain status from all players
    await supabase
      .from('user_squads')
      .update({ is_captain: false, is_vice_captain: false })
      .eq('user_id', userId);

    // Then set the new captain
    const { data, error } = await supabase
      .from('user_squads')
      .update({ is_captain: true })
      .eq('user_id', userId)
      .eq('player_id', playerId)
      .select()
      .single();

    if (error) {
      console.error('Error setting captain:', error);
      throw error;
    }

    return data as UserSquad;
  }
}

// Analytics service
export class AnalyticsService {
  // Get dashboard stats
  static async getDashboardStats() {
    const [playersCount, teamsCount, injuriesCount] = await Promise.all([
      supabase.from('players').select('id', { count: 'exact', head: true }),
      supabase.from('players').select('team', { count: 'exact', head: true }).distinct(),
      supabase.from('injury_reports').select('id', { count: 'exact', head: true })
    ]);

    return {
      total_players: playersCount.count || 0,
      total_teams: 17, // NRL has 17 teams
      active_injuries: injuriesCount.count || 0,
      last_updated: new Date().toISOString()
    };
  }

  // Get trending players
  static async getTrendingPlayers(limit: number = 10) {
    const { data, error } = await supabase
      .from('players')
      .select('*')
      .order('form', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching trending players:', error);
      throw error;
    }

    return data as Player[];
  }
}

// Cache service for offline support
export class CacheService {
  private static CACHE_PREFIX = 'fantasypro_cache_';
  private static CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  static set(key: string, data: any) {
    const cacheData = {
      data,
      timestamp: Date.now()
    };
    localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheData));
  }

  static get(key: string) {
    const cached = localStorage.getItem(this.CACHE_PREFIX + key);
    if (!cached) return null;

    const { data, timestamp } = JSON.parse(cached);
    if (Date.now() - timestamp > this.CACHE_DURATION) {
      localStorage.removeItem(this.CACHE_PREFIX + key);
      return null;
    }

    return data;
  }

  static clear() {
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(this.CACHE_PREFIX)) {
        localStorage.removeItem(key);
      }
    });
  }
}

export default supabase;
