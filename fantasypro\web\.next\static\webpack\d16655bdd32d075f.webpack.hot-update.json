{"c": ["webpack"], "r": ["pages/injuries"], "m": ["./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js", "./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js", "./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cjonah%5CDesktop%5CORIGVMI%5CGithub%5CPlayground%5COCTOAGENT%5Cfantasypro%5Cweb%5Csrc%5Cpages%5Cinjuries.tsx&page=%2Finjuries!", "./src/pages/injuries.tsx", "__barrel_optimize__?names=ArrowPathIcon,CheckCircleIcon,ClockIcon,ExclamationTriangleIcon,FireIcon,FunnelIcon,HeartIcon,UserGroupIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js"]}